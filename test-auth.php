<?php
header('Content-Type: application/json; charset=UTF-8');

// Iniciar sesión
session_start();

// Incluir funciones
require_once 'config/functions.php';

// Información de debug
$debug_info = [
    'session_status' => session_status(),
    'session_id' => session_id(),
    'session_data' => $_SESSION ?? [],
    'is_logged_in' => isLoggedIn(),
    'current_agent' => null,
    'has_admin_permission' => false,
    'server_info' => [
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'N/A',
        'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? 'N/A',
        'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'] ?? 'N/A',
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'N/A'
    ]
];

// Si está logueado, obtener información del agente
if (isLoggedIn()) {
    $debug_info['current_agent'] = getCurrentAgent();
    $debug_info['has_admin_permission'] = hasPermission('manage_admin');
}

// Probar conexión a base de datos
try {
    $database = new Database();
    $conn = $database->getConnection();
    $debug_info['database_connection'] = 'OK';
    
    // Probar query simple
    $test_query = "SELECT COUNT(*) as count FROM agentes";
    $stmt = $conn->prepare($test_query);
    $stmt->execute();
    $result = $stmt->fetch();
    $debug_info['agentes_count'] = $result['count'];
    
} catch (Exception $e) {
    $debug_info['database_connection'] = 'ERROR: ' . $e->getMessage();
}

// Probar endpoints específicos si está logueado
if (isLoggedIn()) {
    // Test mark_read endpoint
    $test_data = [
        'informativo_id' => 999,
        'action' => 'read'
    ];
    
    $debug_info['test_endpoints'] = [];
    
    // Simular llamada a mark_read.php
    try {
        // Verificar que el archivo existe
        if (file_exists('api/mark_read.php')) {
            $debug_info['test_endpoints']['mark_read_file'] = 'EXISTS';
        } else {
            $debug_info['test_endpoints']['mark_read_file'] = 'NOT_FOUND';
        }
        
        // Verificar que el archivo delete existe
        if (file_exists('api/delete_informativo.php')) {
            $debug_info['test_endpoints']['delete_file'] = 'EXISTS';
        } else {
            $debug_info['test_endpoints']['delete_file'] = 'NOT_FOUND';
        }
        
    } catch (Exception $e) {
        $debug_info['test_endpoints']['error'] = $e->getMessage();
    }
}

// Información adicional de PHP
$debug_info['php_info'] = [
    'version' => PHP_VERSION,
    'extensions' => [
        'pdo' => extension_loaded('pdo'),
        'pdo_mysql' => extension_loaded('pdo_mysql'),
        'json' => extension_loaded('json'),
        'session' => extension_loaded('session')
    ],
    'error_reporting' => error_reporting(),
    'display_errors' => ini_get('display_errors')
];

// Devolver información de debug
echo json_encode($debug_info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
