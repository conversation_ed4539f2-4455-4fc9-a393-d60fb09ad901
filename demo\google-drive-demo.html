<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Integración Google Drive</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            margin-bottom: 30px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 20px;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 15px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-card text-center">
            <h1 class="display-4 mb-4">
                <i class="fab fa-google-drive me-3" style="color: #4285f4;"></i>
                Integración Google Drive
            </h1>
            <p class="lead">Sistema de informativos con soporte para documentos de Google Drive</p>
            <div class="row mt-4">
                <div class="col-md-4">
                    <div class="text-primary">
                        <i class="fas fa-cloud fa-3x mb-3"></i>
                        <h5>Sin Subidas</h5>
                        <p>Los archivos permanecen en Google Drive</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-success">
                        <i class="fas fa-shield-alt fa-3x mb-3"></i>
                        <h5>Seguro</h5>
                        <p>Solo acceso de lectura autorizado</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-warning">
                        <i class="fas fa-bolt fa-3x mb-3"></i>
                        <h5>Rápido</h5>
                        <p>Acceso directo desde la nube</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Características -->
        <div class="demo-card">
            <h2 class="mb-4">
                <i class="fas fa-star me-2"></i>
                Características Principales
            </h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mouse-pointer"></i>
                    </div>
                    <h5>Selección Intuitiva</h5>
                    <p>Interfaz moderna con pestañas para elegir entre archivo local o Google Drive.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fab fa-google"></i>
                    </div>
                    <h5>Autenticación OAuth</h5>
                    <p>Conexión segura con Google Drive usando OAuth 2.0 estándar.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h5>Múltiples Formatos</h5>
                    <p>Soporte para PDF, DOC, DOCX, JPG, PNG directamente desde Drive.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h5>Vista Previa</h5>
                    <p>Previsualización del archivo seleccionado antes de confirmar.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h5>Validación Automática</h5>
                    <p>Verificación de tipo y tamaño de archivo automática.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h5>Responsive</h5>
                    <p>Funciona perfectamente en desktop, tablet y móvil.</p>
                </div>
            </div>
        </div>

        <!-- Cómo Funciona -->
        <div class="demo-card">
            <h2 class="mb-4">
                <i class="fas fa-cogs me-2"></i>
                ¿Cómo Funciona?
            </h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">1</div>
                        <div>
                            <h5>Seleccionar Google Drive</h5>
                            <p>El usuario hace clic en la pestaña "Google Drive" en el modal de subida.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">2</div>
                        <div>
                            <h5>Autenticación</h5>
                            <p>Se abre una ventana de Google para autorizar el acceso a Drive.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">3</div>
                        <div>
                            <h5>Selección de Archivo</h5>
                            <p>Se muestra una lista de archivos compatibles del Drive del usuario.</p>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">4</div>
                        <div>
                            <h5>Confirmación</h5>
                            <p>El usuario selecciona el archivo y ve una previsualización.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">5</div>
                        <div>
                            <h5>Guardado</h5>
                            <p>Se guarda la referencia al archivo (no el archivo en sí) en la base de datos.</p>
                        </div>
                    </div>
                    
                    <div class="d-flex align-items-start mb-4">
                        <div class="step-number">6</div>
                        <div>
                            <h5>Acceso Directo</h5>
                            <p>Los usuarios pueden ver el documento directamente desde Google Drive.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Configuración -->
        <div class="demo-card">
            <h2 class="mb-4">
                <i class="fas fa-wrench me-2"></i>
                Configuración Requerida
            </h2>
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Nota:</strong> Para usar esta funcionalidad en producción, necesitas configurar las credenciales de Google Cloud Console.
            </div>
            
            <h5>1. Archivo de Configuración</h5>
            <div class="code-block mb-3">
// config/google-drive-config.js
const GOOGLE_DRIVE_CONFIG = {
    CLIENT_ID: 'tu-client-id.apps.googleusercontent.com',
    API_KEY: 'tu-api-key',
    SCOPES: 'https://www.googleapis.com/auth/drive.readonly'
};
            </div>
            
            <h5>2. Base de Datos</h5>
            <div class="code-block mb-3">
-- Ejecutar en MySQL
ALTER TABLE informativos 
ADD COLUMN drive_file_id VARCHAR(255) NULL,
ADD COLUMN drive_file_name VARCHAR(500) NULL,
ADD COLUMN drive_file_url TEXT NULL,
ADD COLUMN upload_method ENUM('local', 'drive') DEFAULT 'local';
            </div>
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Importante:</strong> Consulta el archivo <code>docs/GOOGLE_DRIVE_SETUP.md</code> para instrucciones detalladas de configuración.
            </div>
        </div>

        <!-- Beneficios -->
        <div class="demo-card">
            <h2 class="mb-4">
                <i class="fas fa-thumbs-up me-2"></i>
                Beneficios
            </h2>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Ahorro de Espacio:</strong> No ocupa espacio en el servidor
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Siempre Actualizado:</strong> Los cambios en Drive se reflejan automáticamente
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Colaboración:</strong> Múltiples usuarios pueden editar el mismo documento
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Backup Automático:</strong> Google Drive maneja las copias de seguridad
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Control de Acceso:</strong> El propietario controla los permisos
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check text-success me-2"></i>
                            <strong>Historial de Versiones:</strong> Google Drive mantiene el historial
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
