<?php
/**
 * API para manejar firmas digitales de documentos
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar que el usuario esté autenticado
session_start();
if (!isset($_SESSION['agente_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Datos inválidos']);
        exit;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'record_signature':
            recordDocumentSignature($input);
            break;
            
        case 'get_signatures':
            getDocumentSignatures($input);
            break;
            
        case 'get_signature_stats':
            getSignatureStats($input);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Acción no válida']);
            break;
    }
} else if ($method === 'GET') {
    $action = $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_all_signatures':
            getAllSignatures();
            break;
            
        case 'get_document_signatures':
            getDocumentSignatures(['informativo_id' => $_GET['informativo_id'] ?? 0]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Acción no válida']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
}

/**
 * Registrar firma digital de documento
 */
function recordDocumentSignature($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $informativo_id = intval($data['informativo_id'] ?? 0);
        $action_type = $data['action_type'] ?? 'read'; // 'read' o 'signed'
        $agente_id = $_SESSION['agente_id'];
        
        if (!$informativo_id) {
            throw new Exception('ID de documento inválido');
        }
        
        if (!in_array($action_type, ['read', 'signed'])) {
            throw new Exception('Tipo de acción inválido');
        }
        
        // Obtener información adicional
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // Verificar si el documento existe
        $doc_query = "SELECT id, titulo FROM informativos WHERE id = ?";
        $doc_stmt = $conn->prepare($doc_query);
        $doc_stmt->execute([$informativo_id]);
        $documento = $doc_stmt->fetch();
        
        if (!$documento) {
            throw new Exception('Documento no encontrado');
        }
        
        // Insertar o actualizar registro
        $query = "INSERT INTO document_signatures (informativo_id, agente_id, action_type, ip_address, user_agent) 
                  VALUES (?, ?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE 
                  ip_address = VALUES(ip_address), 
                  user_agent = VALUES(user_agent), 
                  created_at = CURRENT_TIMESTAMP";
        
        $stmt = $conn->prepare($query);
        $result = $stmt->execute([$informativo_id, $agente_id, $action_type, $ip_address, $user_agent]);
        
        if ($result) {
            // Si es una firma, también registrar como leído automáticamente
            if ($action_type === 'signed') {
                $read_query = "INSERT INTO document_signatures (informativo_id, agente_id, action_type, ip_address, user_agent) 
                              VALUES (?, ?, 'read', ?, ?) 
                              ON DUPLICATE KEY UPDATE 
                              ip_address = VALUES(ip_address), 
                              user_agent = VALUES(user_agent)";
                $read_stmt = $conn->prepare($read_query);
                $read_stmt->execute([$informativo_id, $agente_id, $ip_address, $user_agent]);
            }
            
            echo json_encode([
                'success' => true, 
                'message' => $action_type === 'signed' ? 'Documento firmado digitalmente' : 'Documento marcado como leído',
                'action_type' => $action_type
            ]);
        } else {
            throw new Exception('Error al registrar la acción');
        }
        
    } catch (Exception $e) {
        error_log("Error al registrar firma digital: " . $e->getMessage());
        echo json_encode([
            'success' => false, 
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * Obtener firmas de un documento específico
 */
function getDocumentSignatures($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $informativo_id = intval($data['informativo_id'] ?? 0);
        
        if (!$informativo_id) {
            throw new Exception('ID de documento inválido');
        }
        
        $query = "SELECT * FROM document_signature_details WHERE informativo_id = ? ORDER BY created_at DESC";
        $stmt = $conn->prepare($query);
        $stmt->execute([$informativo_id]);
        $signatures = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'signatures' => $signatures
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * Obtener estadísticas de firmas
 */
function getSignatureStats($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $informativo_id = intval($data['informativo_id'] ?? 0);
        
        if ($informativo_id) {
            // Estadísticas de un documento específico
            $query = "SELECT * FROM document_signature_stats WHERE informativo_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$informativo_id]);
            $stats = $stmt->fetch();
        } else {
            // Estadísticas generales
            $query = "SELECT * FROM document_signature_stats ORDER BY fecha_publicacion DESC";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $stats = $stmt->fetchAll();
        }
        
        echo json_encode([
            'success' => true,
            'stats' => $stats
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}

/**
 * Obtener todas las firmas (para admin)
 */
function getAllSignatures() {
    try {
        // Verificar permisos de administrador
        if (!hasPermission('manage_admin')) {
            throw new Exception('Sin permisos suficientes');
        }
        
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "SELECT * FROM document_signature_details ORDER BY created_at DESC LIMIT 100";
        $stmt = $conn->prepare($query);
        $stmt->execute();
        $signatures = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'signatures' => $signatures
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
}
?>
