<?php
/**
 * API para firmar documentos digitalmente
 */

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../config/functions.php';

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

try {
    // Verificar sesión
    session_start();
    if (!isset($_SESSION['agente_id'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'No autorizado'
        ]);
        exit;
    }

    $agente_id = $_SESSION['agente_id'];

    // Obtener datos JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['documento_id']) || !isset($input['action'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Datos incompletos'
        ]);
        exit;
    }

    $documento_id = (int)$input['documento_id'];
    $action = $input['action'];

    // Conectar a la base de datos
    $database = new Database();
    $conn = $database->getConnection();

    // Verificar que el documento existe
    $doc_query = "SELECT id, titulo FROM informativos WHERE id = ?";
    $doc_stmt = $conn->prepare($doc_query);
    $doc_stmt->execute([$documento_id]);
    $documento = $doc_stmt->fetch();

    if (!$documento) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Documento no encontrado'
        ]);
        exit;
    }

    // Crear tabla de firmas si no existe
    $create_signatures_table = "
        CREATE TABLE IF NOT EXISTS document_signatures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_id INT NOT NULL,
            documento_id INT NOT NULL,
            signed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            signature_hash VARCHAR(255),
            UNIQUE KEY unique_signature (agente_id, documento_id),
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE,
            FOREIGN KEY (documento_id) REFERENCES informativos(id) ON DELETE CASCADE
        )
    ";
    $conn->exec($create_signatures_table);

    // Crear tabla de lecturas si no existe
    $create_reads_table = "
        CREATE TABLE IF NOT EXISTS document_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_id INT NOT NULL,
            documento_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            UNIQUE KEY unique_read (agente_id, documento_id),
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE,
            FOREIGN KEY (documento_id) REFERENCES informativos(id) ON DELETE CASCADE
        )
    ";
    $conn->exec($create_reads_table);

    // Verificar si ya fue firmado
    $check_signature_query = "SELECT id FROM document_signatures WHERE agente_id = ? AND documento_id = ?";
    $check_signature_stmt = $conn->prepare($check_signature_query);
    $check_signature_stmt->execute([$agente_id, $documento_id]);
    $existing_signature = $check_signature_stmt->fetch();

    if ($existing_signature) {
        echo json_encode([
            'success' => true,
            'message' => 'Documento ya estaba firmado',
            'already_signed' => true
        ]);
        exit;
    }

    // Iniciar transacción
    $conn->beginTransaction();

    try {
        // Marcar como leído si no lo estaba
        $check_read_query = "SELECT id FROM document_reads WHERE agente_id = ? AND documento_id = ?";
        $check_read_stmt = $conn->prepare($check_read_query);
        $check_read_stmt->execute([$agente_id, $documento_id]);
        $existing_read = $check_read_stmt->fetch();

        if (!$existing_read) {
            $insert_read_query = "
                INSERT INTO document_reads (agente_id, documento_id, ip_address, user_agent) 
                VALUES (?, ?, ?, ?)
            ";
            $insert_read_stmt = $conn->prepare($insert_read_query);
            $insert_read_stmt->execute([
                $agente_id,
                $documento_id,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        }

        // Crear hash de firma único
        $signature_data = $agente_id . $documento_id . time() . $_SERVER['REMOTE_ADDR'];
        $signature_hash = hash('sha256', $signature_data);

        // Insertar firma digital
        $insert_signature_query = "
            INSERT INTO document_signatures (agente_id, documento_id, ip_address, user_agent, signature_hash) 
            VALUES (?, ?, ?, ?, ?)
        ";
        $insert_signature_stmt = $conn->prepare($insert_signature_query);
        $insert_signature_stmt->execute([
            $agente_id,
            $documento_id,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null,
            $signature_hash
        ]);

        // Confirmar transacción
        $conn->commit();

        // Log de la acción
        error_log("Documento {$documento_id} firmado por agente {$agente_id} con hash {$signature_hash}");

        echo json_encode([
            'success' => true,
            'message' => 'Documento firmado correctamente',
            'documento_id' => $documento_id,
            'documento_titulo' => $documento['titulo'],
            'signature_hash' => $signature_hash,
            'timestamp' => date('Y-m-d H:i:s')
        ]);

    } catch (Exception $e) {
        // Revertir transacción en caso de error
        $conn->rollback();
        throw $e;
    }

} catch (Exception $e) {
    error_log("Error en sign_document.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor',
        'error' => $e->getMessage()
    ]);
}
?>
