<?php
require_once '../config/functions.php';
requireLogin();
requirePermission('all_permissions');

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Procesar acciones
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'crear':
                try {
                    $nombre = trim($_POST['nombre']);
                    $usuario_sabre = trim($_POST['usuario_sabre']);
                    $email = trim($_POST['email']);
                    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                    $grupo_id = $_POST['grupo_id'];
                    $telefono = trim($_POST['telefono']);

                    // Validar campos únicos
                    $check_query = "SELECT COUNT(*) as count FROM agentes WHERE (usuario_sabre = ? OR email = ?) AND activo = 1";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->execute([$usuario_sabre, $email]);
                    $exists = $check_stmt->fetch()['count'];

                    if ($exists > 0) {
                        $error = "El usuario Sabre o email ya existe";
                    } else {
                        $query = "INSERT INTO agentes (nombre, usuario_sabre, email, password, grupo_id, telefono) VALUES (?, ?, ?, ?, ?, ?)";
                        $stmt = $conn->prepare($query);
                        if ($stmt->execute([$nombre, $usuario_sabre, $email, $password, $grupo_id, $telefono])) {
                            $message = "Agente creado exitosamente";
                        } else {
                            $error = "Error al crear el agente";
                        }
                    }
                } catch (Exception $e) {
                    $error = "Error: " . $e->getMessage();
                }
                break;

            case 'editar':
                try {
                    $id = $_POST['id'];
                    $nombre = trim($_POST['nombre']);
                    $usuario_sabre = trim($_POST['usuario_sabre']);
                    $email = trim($_POST['email']);
                    $grupo_id = $_POST['grupo_id'];
                    $telefono = trim($_POST['telefono']);

                    // Validar campos únicos (excluyendo el agente actual)
                    $check_query = "SELECT COUNT(*) as count FROM agentes WHERE (usuario_sabre = ? OR email = ?) AND id != ? AND activo = 1";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->execute([$usuario_sabre, $email, $id]);
                    $exists = $check_stmt->fetch()['count'];

                    if ($exists > 0) {
                        $error = "El usuario Sabre o email ya existe";
                    } else {
                        $query = "UPDATE agentes SET nombre = ?, usuario_sabre = ?, email = ?, grupo_id = ?, telefono = ? WHERE id = ?";
                        $stmt = $conn->prepare($query);
                        if ($stmt->execute([$nombre, $usuario_sabre, $email, $grupo_id, $telefono, $id])) {
                            $message = "Agente actualizado exitosamente";
                        } else {
                            $error = "Error al actualizar el agente";
                        }
                    }
                } catch (Exception $e) {
                    $error = "Error: " . $e->getMessage();
                }
                break;

            case 'eliminar':
                try {
                    $id = $_POST['id'];
                    $query = "UPDATE agentes SET activo = 0 WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    if ($stmt->execute([$id])) {
                        $message = "Agente eliminado exitosamente";
                    } else {
                        $error = "Error al eliminar el agente";
                    }
                } catch (Exception $e) {
                    $error = "Error: " . $e->getMessage();
                }
                break;

            case 'cambiar_password':
                try {
                    $id = $_POST['id'];
                    $new_password = password_hash($_POST['new_password'], PASSWORD_DEFAULT);
                    $query = "UPDATE agentes SET password = ? WHERE id = ?";
                    $stmt = $conn->prepare($query);
                    if ($stmt->execute([$new_password, $id])) {
                        $message = "Contraseña actualizada exitosamente";
                    } else {
                        $error = "Error al actualizar la contraseña";
                    }
                } catch (Exception $e) {
                    $error = "Error: " . $e->getMessage();
                }
                break;
        }
    }
}

// Obtener filtros
$search = $_GET['search'] ?? '';
$grupo_filter = $_GET['grupo'] ?? '';
$estado_filter = $_GET['estado'] ?? '';

// Construir consulta con filtros
$where_conditions = ["a.activo = 1"];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(a.nombre LIKE ? OR a.usuario_sabre LIKE ? OR a.email LIKE ?)";
    $search_term = "%$search%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($grupo_filter)) {
    $where_conditions[] = "a.grupo_id = ?";
    $params[] = $grupo_filter;
}

if (!empty($estado_filter)) {
    $where_conditions[] = "a.estado = ?";
    $params[] = $estado_filter;
}

$where_clause = implode(" AND ", $where_conditions);

// Obtener agentes
$query = "SELECT a.*, g.nombre as grupo_nombre 
          FROM agentes a 
          LEFT JOIN grupos g ON a.grupo_id = g.id 
          WHERE $where_clause 
          ORDER BY a.nombre ASC";
$stmt = $conn->prepare($query);
$stmt->execute($params);
$agentes = $stmt->fetchAll();

// Obtener grupos para filtros y formularios
$grupos_query = "SELECT * FROM grupos ORDER BY nombre ASC";
$grupos_stmt = $conn->prepare($grupos_query);
$grupos_stmt->execute();
$grupos = $grupos_stmt->fetchAll();

// Obtener estadísticas
$stats_query = "SELECT 
    COUNT(*) as total,
    SUM(CASE WHEN estado IN ('🟢Disponible', '🟡En Colación', '🔴Ocupado') THEN 1 ELSE 0 END) as en_turno,
    SUM(CASE WHEN estado = '🟢Disponible' THEN 1 ELSE 0 END) as disponibles,
    SUM(CASE WHEN estado = '🔴Ocupado' THEN 1 ELSE 0 END) as ocupados
    FROM agentes WHERE activo = 1";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administración de Agentes - SwissportAgents</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="../css/custom.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .main-container {
            padding: 30px;
            min-height: 100vh;
        }

        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .admin-header h1 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            margin: 0;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .content-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-disponible { background: #d4edda; color: #155724; }
        .status-colacion { background: #fff3cd; color: #856404; }
        .status-ocupado { background: #f8d7da; color: #721c24; }
        .status-fuera { background: #e2e3e5; color: #383d41; }

        .btn-action {
            border-radius: 10px;
            font-weight: 600;
            padding: 8px 16px;
            margin: 2px;
            transition: all 0.3s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
        }

        .view-toggle {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 5px;
            display: inline-flex;
            margin-bottom: 20px;
        }

        .view-toggle .btn {
            border-radius: 8px;
            border: none;
            padding: 8px 16px;
            margin: 0 2px;
        }

        .view-toggle .btn.active {
            background: var(--gradient-primary);
            color: white;
        }

        .filters-section {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: 20px 20px 0 0;
            border: none;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1><i class="fas fa-users me-3"></i>Administración de Agentes</h1>
                    <p class="mb-0" style="opacity: 0.8;">Gestión completa de usuarios del sistema</p>
                </div>
                <div>
                    <a href="../admin.php" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Volver
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAgentModal">
                        <i class="fas fa-plus me-2"></i>Nuevo Agente
                    </button>
                </div>
            </div>
        </div>

        <!-- Mensajes -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Estadísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total']; ?></div>
                <div class="text-muted">Total Agentes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['en_turno']; ?></div>
                <div class="text-muted">En Turno</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['disponibles']; ?></div>
                <div class="text-muted">Disponibles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['ocupados']; ?></div>
                <div class="text-muted">Ocupados</div>
            </div>
        </div>

        <!-- Filtros -->
        <div class="filters-section">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="search" placeholder="Buscar por nombre, usuario o email..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="grupo">
                        <option value="">Todos los grupos</option>
                        <?php foreach ($grupos as $grupo): ?>
                            <option value="<?php echo $grupo['id']; ?>" <?php echo $grupo_filter == $grupo['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($grupo['nombre']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="estado">
                        <option value="">Todos los estados</option>
                        <option value="🟢Disponible" <?php echo $estado_filter == '🟢Disponible' ? 'selected' : ''; ?>>🟢 Disponible</option>
                        <option value="🟡En Colación" <?php echo $estado_filter == '🟡En Colación' ? 'selected' : ''; ?>>🟡 En Colación</option>
                        <option value="🔴Ocupado" <?php echo $estado_filter == '🔴Ocupado' ? 'selected' : ''; ?>>🔴 Ocupado</option>
                        <option value="🏠Fuera de Turno" <?php echo $estado_filter == '🏠Fuera de Turno' ? 'selected' : ''; ?>>🏠 Fuera de Turno</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search me-2"></i>Filtrar
                    </button>
                </div>
            </form>
        </div>

        <!-- Toggle de vista -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div class="view-toggle">
                <button class="btn active" onclick="toggleView('grid')" id="gridViewBtn">
                    <i class="fas fa-th me-2"></i>Cuadrícula
                </button>
                <button class="btn" onclick="toggleView('list')" id="listViewBtn">
                    <i class="fas fa-list me-2"></i>Lista
                </button>
            </div>
            <div class="text-muted">
                Mostrando <?php echo count($agentes); ?> agente(s)
            </div>
        </div>

        <!-- Vista de cuadrícula -->
        <div id="gridView" class="row">
            <?php foreach ($agentes as $ag): ?>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="agent-card">
                        <div class="d-flex align-items-center mb-3">
                            <img src="../<?php echo getAvatarUrl($ag['foto_perfil']); ?>" alt="Avatar"
                                 class="rounded-circle me-3" style="width: 60px; height: 60px; object-fit: cover;">
                            <div class="flex-grow-1">
                                <h5 class="mb-1"><?php echo htmlspecialchars($ag['nombre']); ?></h5>
                                <small class="text-muted"><?php echo htmlspecialchars($ag['grupo_nombre'] ?? 'Sin grupo'); ?></small>
                            </div>
                            <span class="status-badge <?php
                                echo match($ag['estado']) {
                                    '🟢Disponible' => 'status-disponible',
                                    '🟡En Colación' => 'status-colacion',
                                    '🔴Ocupado' => 'status-ocupado',
                                    default => 'status-fuera'
                                };
                            ?>"><?php echo $ag['estado']; ?></span>
                        </div>

                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <small class="text-muted d-block">ID Sabre</small>
                                <strong><?php echo htmlspecialchars($ag['usuario_sabre'] ?? 'N/A'); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block">WhatsApp</small>
                                <strong><?php echo htmlspecialchars($ag['telefono'] ?? 'N/A'); ?></strong>
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-action flex-fill"
                                    onclick="editAgent(<?php echo $ag['id']; ?>)">
                                <i class="fas fa-edit me-1"></i>Editar
                            </button>
                            <button class="btn btn-outline-warning btn-action"
                                    onclick="changePassword(<?php echo $ag['id']; ?>)">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-action"
                                    onclick="deleteAgent(<?php echo $ag['id']; ?>, '<?php echo htmlspecialchars($ag['nombre']); ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Vista de lista -->
        <div id="listView" class="content-card" style="display: none;">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Avatar</th>
                            <th>Nombre</th>
                            <th>ID Sabre</th>
                            <th>Email</th>
                            <th>Grupo</th>
                            <th>WhatsApp</th>
                            <th>Estado</th>
                            <th>Último Acceso</th>
                            <th>Acciones</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($agentes as $ag): ?>
                            <tr>
                                <td>
                                    <img src="../<?php echo getAvatarUrl($ag['foto_perfil']); ?>" alt="Avatar"
                                         class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($ag['nombre']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($ag['usuario_sabre'] ?? 'N/A'); ?></td>
                                <td><?php echo htmlspecialchars($ag['email']); ?></td>
                                <td>
                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($ag['grupo_nombre'] ?? 'Sin grupo'); ?></span>
                                </td>
                                <td><?php echo htmlspecialchars($ag['telefono'] ?? 'N/A'); ?></td>
                                <td>
                                    <span class="status-badge <?php
                                        echo match($ag['estado']) {
                                            '🟢Disponible' => 'status-disponible',
                                            '🟡En Colación' => 'status-colacion',
                                            '🔴Ocupado' => 'status-ocupado',
                                            default => 'status-fuera'
                                        };
                                    ?>"><?php echo $ag['estado']; ?></span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <?php echo $ag['ultimo_acceso'] ? date('d/m/Y H:i', strtotime($ag['ultimo_acceso'])) : 'Nunca'; ?>
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-outline-primary btn-sm"
                                                onclick="editAgent(<?php echo $ag['id']; ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn btn-outline-warning btn-sm"
                                                onclick="changePassword(<?php echo $ag['id']; ?>)">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm"
                                                onclick="deleteAgent(<?php echo $ag['id']; ?>, '<?php echo htmlspecialchars($ag['nombre']); ?>')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <?php if (empty($agentes)): ?>
            <div class="content-card text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">No se encontraron agentes</h4>
                <p class="text-muted">No hay agentes que coincidan con los filtros aplicados.</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAgentModal">
                    <i class="fas fa-plus me-2"></i>Crear Primer Agente
                </button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Crear Agente -->
    <div class="modal fade" id="createAgentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Crear Nuevo Agente
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="crear">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nombre" class="form-label">Nombre Completo *</label>
                                <input type="text" class="form-control" id="nombre" name="nombre" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="usuario_sabre" class="form-label">Usuario Sabre *</label>
                                <input type="text" class="form-control" id="usuario_sabre" name="usuario_sabre" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Contraseña *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="grupo_id" class="form-label">Grupo *</label>
                                <select class="form-select" id="grupo_id" name="grupo_id" required>
                                    <option value="">Seleccionar grupo</option>
                                    <?php foreach ($grupos as $grupo): ?>
                                        <option value="<?php echo $grupo['id']; ?>">
                                            <?php echo htmlspecialchars($grupo['nombre']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="telefono" class="form-label">WhatsApp</label>
                                <input type="text" class="form-control" id="telefono" name="telefono" placeholder="+56912345678">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Agente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Editar Agente -->
    <div class="modal fade" id="editAgentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-edit me-2"></i>Editar Agente
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editAgentForm">
                    <input type="hidden" name="action" value="editar">
                    <input type="hidden" name="id" id="edit_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_nombre" class="form-label">Nombre Completo *</label>
                                <input type="text" class="form-control" id="edit_nombre" name="nombre" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_usuario_sabre" class="form-label">Usuario Sabre *</label>
                                <input type="text" class="form-control" id="edit_usuario_sabre" name="usuario_sabre" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="edit_grupo_id" class="form-label">Grupo *</label>
                                <select class="form-select" id="edit_grupo_id" name="grupo_id" required>
                                    <option value="">Seleccionar grupo</option>
                                    <?php foreach ($grupos as $grupo): ?>
                                        <option value="<?php echo $grupo['id']; ?>">
                                            <?php echo htmlspecialchars($grupo['nombre']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="edit_telefono" class="form-label">WhatsApp</label>
                                <input type="text" class="form-control" id="edit_telefono" name="telefono" placeholder="+56912345678">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Guardar Cambios
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Cambiar Contraseña -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-key me-2"></i>Cambiar Contraseña
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="changePasswordForm">
                    <input type="hidden" name="action" value="cambiar_password">
                    <input type="hidden" name="id" id="password_id">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="new_password" class="form-label">Nueva Contraseña *</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirmar Contraseña *</label>
                            <input type="password" class="form-control" id="confirm_password" required>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Cambiar Contraseña
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modal Confirmar Eliminación -->
    <div class="modal fade" id="deleteAgentModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>Confirmar Eliminación
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="deleteAgentForm">
                    <input type="hidden" name="action" value="eliminar">
                    <input type="hidden" name="id" id="delete_id">
                    <div class="modal-body">
                        <div class="text-center">
                            <i class="fas fa-user-times fa-3x text-danger mb-3"></i>
                            <h5>¿Estás seguro?</h5>
                            <p class="text-muted">
                                Esta acción desactivará al agente <strong id="delete_name"></strong>.
                                El agente no podrá acceder al sistema pero sus datos se conservarán.
                            </p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash me-2"></i>Eliminar Agente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle entre vista de cuadrícula y lista
        function toggleView(view) {
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            if (view === 'grid') {
                gridView.style.display = 'block';
                listView.style.display = 'none';
                gridBtn.classList.add('active');
                listBtn.classList.remove('active');
            } else {
                gridView.style.display = 'none';
                listView.style.display = 'block';
                listBtn.classList.add('active');
                gridBtn.classList.remove('active');
            }
        }

        // Editar agente
        function editAgent(id) {
            fetch(`../ajax/get_agent.php?id=${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const agent = data.agent;
                        document.getElementById('edit_id').value = agent.id;
                        document.getElementById('edit_nombre').value = agent.nombre;
                        document.getElementById('edit_usuario_sabre').value = agent.usuario_sabre || '';
                        document.getElementById('edit_email').value = agent.email;
                        document.getElementById('edit_grupo_id').value = agent.grupo_id || '';
                        document.getElementById('edit_telefono').value = agent.telefono || '';

                        const modal = new bootstrap.Modal(document.getElementById('editAgentModal'));
                        modal.show();
                    } else {
                        alert('Error al cargar los datos del agente');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error al cargar los datos del agente');
                });
        }

        // Cambiar contraseña
        function changePassword(id) {
            document.getElementById('password_id').value = id;
            document.getElementById('new_password').value = '';
            document.getElementById('confirm_password').value = '';

            const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
            modal.show();
        }

        // Eliminar agente
        function deleteAgent(id, name) {
            document.getElementById('delete_id').value = id;
            document.getElementById('delete_name').textContent = name;

            const modal = new bootstrap.Modal(document.getElementById('deleteAgentModal'));
            modal.show();
        }

        // Validar confirmación de contraseña
        document.getElementById('changePasswordForm').addEventListener('submit', function(e) {
            const password = document.getElementById('new_password').value;
            const confirm = document.getElementById('confirm_password').value;

            if (password !== confirm) {
                e.preventDefault();
                alert('Las contraseñas no coinciden');
                return false;
            }

            if (password.length < 6) {
                e.preventDefault();
                alert('La contraseña debe tener al menos 6 caracteres');
                return false;
            }
        });

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
