-- Agregar soporte para Google Drive en la tabla informativos
-- Ejecutar este script para agregar las columnas necesarias

-- Agregar columnas para Google Drive
ALTER TABLE informativos 
ADD COLUMN drive_file_id VARCHAR(255) NULL COMMENT 'ID del archivo en Google Drive',
ADD COLUMN drive_file_name VARCHAR(500) NULL COMMENT 'Nombre del archivo en Google Drive',
ADD COLUMN drive_file_url TEXT NULL COMMENT 'URL de visualización del archivo en Google Drive',
ADD COLUMN upload_method ENUM('local', 'drive') DEFAULT 'local' COMMENT 'Método de subida del archivo';

-- Crear índice para búsquedas por drive_file_id
CREATE INDEX idx_informativos_drive_file_id ON informativos(drive_file_id);

-- Crear índice para búsquedas por método de subida
CREATE INDEX idx_informativos_upload_method ON informativos(upload_method);

-- Actualizar archivos existentes para marcarlos como 'local'
UPDATE informativos SET upload_method = 'local' WHERE upload_method IS NULL;

-- Comentarios sobre el uso:
-- drive_file_id: ID único del archivo en Google Drive (ej: "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms")
-- drive_file_name: Nombre original del archivo en Google Drive
-- drive_file_url: URL para ver/descargar el archivo (webViewLink de la API)
-- upload_method: 'local' para archivos subidos al servidor, 'drive' para archivos de Google Drive

-- Para archivos de Google Drive, el campo 'archivo' contendrá "drive://[drive_file_id]"
-- Esto permite identificar fácilmente qué archivos son de Google Drive vs locales
