<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Verificar autenticación y permisos
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

if (!hasPermission('manage_admin')) {
    header('Location: ../index.php');
    exit;
}

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

// Obtener documentos leídos
$query = "SELECT 
    ds.informativo_id,
    i.titulo as documento_titulo,
    i.tipo as documento_tipo,
    i.fecha_publicacion,
    ds.agente_id,
    a.nombre as agente_nombre,
    a.apellido as agente_apellido,
    a.sabre_id as agente_sabre_id,
    a.email as agente_email,
    ds.created_at as fecha_lectura,
    ds.ip_address
FROM document_signatures ds
JOIN informativos i ON ds.informativo_id = i.id
JOIN agentes a ON ds.agente_id = a.id
WHERE ds.action_type = 'read'
ORDER BY ds.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$documentos_leidos = $stmt->fetchAll();

// Agrupar por documento
$documentos_agrupados = [];
foreach ($documentos_leidos as $lectura) {
    $doc_id = $lectura['informativo_id'];
    if (!isset($documentos_agrupados[$doc_id])) {
        $documentos_agrupados[$doc_id] = [
            'documento' => [
                'id' => $doc_id,
                'titulo' => $lectura['documento_titulo'],
                'tipo' => $lectura['documento_tipo'],
                'fecha_publicacion' => $lectura['fecha_publicacion']
            ],
            'lecturas' => []
        ];
    }
    $documentos_agrupados[$doc_id]['lecturas'][] = $lectura;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentos Leídos - SwissportAgents</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .document-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
        }
        
        .document-card:hover {
            transform: translateY(-2px);
        }
        
        .document-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }
        
        .document-meta {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #64748b;
            margin-top: 8px;
        }
        
        .tipo-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .tipo-briefing { background: #dbeafe; color: #1d4ed8; }
        .tipo-boletin { background: #dcfce7; color: #166534; }
        .tipo-circular { background: #fef3c7; color: #92400e; }
        .tipo-otro { background: #f3e8ff; color: #7c3aed; }
        
        .readers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .reader-card {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            transition: all 0.3s ease;
        }
        
        .reader-card:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .reader-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }
        
        .reader-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }
        
        .reader-info {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 8px;
        }
        
        .reader-date {
            font-size: 11px;
            color: #a0aec0;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 600;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: transform 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-eye me-3" style="color: #667eea;"></i>
                        Documentos Leídos
                    </h1>
                    <p class="text-muted mb-0">Registro de agentes que han leído los documentos informativos</p>
                </div>
                <a href="../admin.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Volver al Panel
                </a>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($documentos_agrupados); ?></div>
                <div class="stat-label">Documentos con Lecturas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($documentos_leidos); ?></div>
                <div class="stat-label">Total de Lecturas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">
                    <?php 
                    $agentes_unicos = array_unique(array_column($documentos_leidos, 'agente_id'));
                    echo count($agentes_unicos);
                    ?>
                </div>
                <div class="stat-label">Agentes Activos</div>
            </div>
        </div>

        <!-- Lista de documentos -->
        <?php if (empty($documentos_agrupados)): ?>
            <div class="document-card text-center">
                <i class="fas fa-eye fa-3x text-muted mb-3"></i>
                <h5>No hay registros de lectura</h5>
                <p class="text-muted">Aún no hay agentes que hayan leído documentos informativos.</p>
            </div>
        <?php else: ?>
            <?php foreach ($documentos_agrupados as $doc_data): ?>
                <div class="document-card">
                    <div class="document-header">
                        <div class="flex-grow-1">
                            <h3 class="document-title"><?php echo htmlspecialchars($doc_data['documento']['titulo']); ?></h3>
                            <div class="document-meta">
                                <span>
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('d/m/Y', strtotime($doc_data['documento']['fecha_publicacion'])); ?>
                                </span>
                                <span>
                                    <i class="fas fa-users me-1"></i>
                                    <?php echo count($doc_data['lecturas']); ?> lecturas
                                </span>
                            </div>
                        </div>
                        <span class="tipo-badge tipo-<?php echo $doc_data['documento']['tipo']; ?>">
                            <?php echo ucfirst($doc_data['documento']['tipo']); ?>
                        </span>
                    </div>

                    <div class="readers-grid">
                        <?php foreach ($doc_data['lecturas'] as $lectura): ?>
                            <div class="reader-card">
                                <div class="reader-avatar">
                                    <?php echo strtoupper(substr($lectura['agente_nombre'], 0, 1) . substr($lectura['agente_apellido'], 0, 1)); ?>
                                </div>
                                <div class="reader-name">
                                    <?php echo htmlspecialchars($lectura['agente_nombre'] . ' ' . $lectura['agente_apellido']); ?>
                                </div>
                                <div class="reader-info">
                                    ID: <?php echo htmlspecialchars($lectura['agente_sabre_id']); ?>
                                </div>
                                <div class="reader-date">
                                    <?php echo date('d/m/Y H:i', strtotime($lectura['fecha_lectura'])); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
