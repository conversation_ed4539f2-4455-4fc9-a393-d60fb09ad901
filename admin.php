<?php
require_once 'config/functions.php';
requireLogin();
requirePermission('all_permissions');

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Procesar creación de agente desde admin.php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'crear_agente_admin') {
    try {
        $nombre = trim($_POST['nombre']);
        $usuario_sabre = trim($_POST['usuario_sabre']);
        $email = trim($_POST['email']);
        $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
        $grupo_id = $_POST['grupo_id'];
        $telefono = trim($_POST['telefono']);

        // Validar campos únicos
        $check_query = "SELECT COUNT(*) as count FROM agentes WHERE (usuario_sabre = ? OR email = ?) AND activo = 1";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->execute([$usuario_sabre, $email]);
        $exists = $check_stmt->fetch()['count'];

        if ($exists > 0) {
            $error = "El usuario Sabre o email ya existe";
        } else {
            $query = "INSERT INTO agentes (nombre, usuario_sabre, email, password, grupo_id, telefono) VALUES (?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            if ($stmt->execute([$nombre, $usuario_sabre, $email, $password, $grupo_id, $telefono])) {
                $message = "Agente creado exitosamente";
            } else {
                $error = "Error al crear el agente";
            }
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Obtener estadísticas generales
$stats = [];

// Total de agentes
$query = "SELECT COUNT(*) as total FROM agentes WHERE activo = 1";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['total_agentes'] = $stmt->fetch()['total'];

// Agentes en turno
$query = "SELECT COUNT(*) as total FROM agentes WHERE estado IN ('🟢Disponible', '🟡En Colación', '🔴Ocupado') AND activo = 1";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['agentes_turno'] = $stmt->fetch()['total'];

// Asignaciones de hoy
$query = "SELECT COUNT(*) as total FROM asignaciones WHERE fecha = CURDATE()";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['asignaciones_hoy'] = $stmt->fetch()['total'];

// Vuelos de hoy
$query = "SELECT COUNT(*) as total FROM vuelos WHERE fecha_vuelo = CURDATE()";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['vuelos_hoy'] = $stmt->fetch()['total'];

// Colaciones activas
$query = "SELECT COUNT(*) as total FROM colaciones WHERE fecha = CURDATE() AND hora_salida_real IS NOT NULL AND hora_regreso_real IS NULL";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['colaciones_activas'] = $stmt->fetch()['total'];

// Notificaciones no leídas
$query = "SELECT COUNT(*) as total FROM notificaciones WHERE leida = 0";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['notificaciones_pendientes'] = $stmt->fetch()['total'];

// Informativos nuevos
$query = "SELECT COUNT(*) as total FROM informativos
          WHERE fecha_publicacion >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
          AND activo = 1";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['informativos_nuevos'] = $stmt->fetch()['total'];

// Informativos sin leer
$query = "SELECT COUNT(DISTINCT i.id) as total
          FROM informativos i
          LEFT JOIN informativo_lecturas il ON i.id = il.informativo_id
          WHERE i.activo = 1
          AND (il.leido IS NULL OR il.leido = 0)
          AND i.fecha_publicacion >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats['informativos_sin_leer'] = $stmt->fetch()['total'];

// Estadísticas de sistema
$query = "SELECT
            (SELECT COUNT(*) FROM agentes WHERE activo = 1) as total_agentes,
            (SELECT COUNT(*) FROM vuelos WHERE fecha_vuelo >= CURDATE() - INTERVAL 7 DAY) as vuelos_semana,
            (SELECT COUNT(*) FROM asignaciones WHERE fecha >= CURDATE() - INTERVAL 7 DAY) as asignaciones_semana,
            (SELECT COUNT(*) FROM colaciones WHERE fecha >= CURDATE() - INTERVAL 7 DAY) as colaciones_semana";
$stmt = $conn->prepare($query);
$stmt->execute();
$stats_sistema = $stmt->fetch();

// Obtener grupos para el modal de crear agente
$grupos_query = "SELECT * FROM grupos ORDER BY nombre ASC";
$grupos_stmt = $conn->prepare($grupos_query);
$grupos_stmt->execute();
$grupos = $grupos_stmt->fetchAll();

// Obtener notificaciones para el agente actual
$notificaciones = [];
try {
    if (function_exists('getUnreadNotifications')) {
        $notificaciones = getUnreadNotifications($agente['id']);
    } else {
        // Fallback a consulta directa si la función no existe
        $notif_query = "SELECT * FROM notificaciones
                       WHERE agente_id = ? AND leida = 0 AND activo = 1
                       ORDER BY created_at DESC LIMIT 10";
        $notif_stmt = $conn->prepare($notif_query);
        $notif_stmt->execute([$agente['id']]);
        $notificaciones = $notif_stmt->fetchAll();
    }
} catch (Exception $e) {
    $notificaciones = [];
}

// Obtener agentes recientes
$query = "SELECT a.*, g.nombre as grupo_nombre FROM agentes a
          LEFT JOIN grupos g ON a.grupo_id = g.id
          WHERE a.activo = 1
          ORDER BY a.created_at DESC
          LIMIT 5";
$stmt = $conn->prepare($query);
$stmt->execute();
$agentes_recientes = $stmt->fetchAll();

// Obtener actividad reciente con paginación
$page = isset($_GET['activity_page']) ? (int)$_GET['activity_page'] : 1;
$records_per_page = 5;
$offset = ($page - 1) * $records_per_page;

// Contar total de registros de actividad
$count_query = "SELECT COUNT(*) as total FROM (
    SELECT a.created_at as fecha
    FROM asignaciones a
    WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

    UNION ALL

    SELECT v.created_at as fecha
    FROM vuelos v
    WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

    UNION ALL

    SELECT a.created_at as fecha
    FROM agentes a
    WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
) as total_activity";
$count_stmt = $conn->prepare($count_query);
$count_stmt->execute();
$total_records = $count_stmt->fetch()['total'];
$total_pages = ceil($total_records / $records_per_page);

// Obtener actividad reciente paginada
$query = "SELECT 'asignacion' as tipo, a.created_at as fecha,
                 CONCAT('Asignación creada para ', ag.nombre) as descripcion
          FROM asignaciones a
          JOIN agentes ag ON a.agente_id = ag.id
          WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

          UNION ALL

          SELECT 'vuelo' as tipo, v.created_at as fecha,
                 CONCAT('Vuelo ', v.numero_vuelo, ' registrado') as descripcion
          FROM vuelos v
          WHERE v.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

          UNION ALL

          SELECT 'agente' as tipo, a.created_at as fecha,
                 CONCAT('Nuevo agente registrado: ', a.nombre) as descripcion
          FROM agentes a
          WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)

          ORDER BY fecha DESC
          LIMIT $records_per_page OFFSET $offset";
$stmt = $conn->prepare($query);
$stmt->execute();
$actividad_reciente = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Administración - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --gradient-admin: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --sidebar-width: 280px;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
        }

        .user-info {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            border: 3px solid var(--primary-color);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-disponible { background: #d4edda; color: #155724; }
        .status-colacion { background: #fff3cd; color: #856404; }
        .status-ocupado { background: #f8d7da; color: #721c24; }
        .status-fuera { background: #e2e3e5; color: #383d41; }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin-right: 20px;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
        }
        /* ===== TOP BAR STYLES ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* ===== ADMIN HEADER ===== */
        .admin-header {
            background: var(--gradient-admin);
            border-radius: var(--border-radius);
            padding: 40px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
        }

        .admin-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 300px;
            height: 300px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .admin-header h1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        /* ===== STATS CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .admin-stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            text-align: center;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .admin-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .admin-stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .admin-stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .admin-stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-icon.primary { background: var(--gradient-primary); color: white; }
        .stat-icon.success { background: var(--gradient-success); color: white; }
        .stat-icon.warning { background: var(--gradient-warning); color: white; }
        .stat-icon.danger { background: var(--gradient-danger); color: white; }
        .stat-icon.info { background: var(--gradient-info); color: white; }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 10px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            line-height: 1;
        }

        .stat-label {
            color: #4a5568;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
            margin: 0;
        }

        .stat-trend {
            font-size: 12px;
            font-weight: 600;
            margin-top: 8px;
            position: relative;
            z-index: 1;
        }

        .trend-up { color: #48bb78; }
        .trend-down { color: #f56565; }
        .trend-warning { color: #f56500; }
        .trend-success { color: #38a169; }

        /* ===== ADMIN CARDS GRID ===== */
        .admin-cards-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            margin-bottom: 40px;
            position: relative;
            z-index: 100;
        }

        @media (max-width: 1400px) {
            .admin-cards-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 992px) {
            .admin-cards-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .action-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            z-index: 200;
        }

        .action-card .btn {
            position: relative;
            z-index: 300;
            pointer-events: auto;
            cursor: pointer;
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 20px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .action-card .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .action-card .d-grid {
            position: relative;
            z-index: 250;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .action-card:hover::before {
            opacity: 0.02;
        }

        .action-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .action-icon {
            width: 60px;
            height: 60px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .action-card:hover .action-icon {
            transform: scale(1.05) rotate(3deg);
        }

        .action-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #2d3748;
        }

        .action-subtitle {
            font-size: 14px;
            color: #718096;
            margin: 0;
        }

        .action-buttons {
            position: relative;
            z-index: 1;
        }

        .action-buttons .btn {
            border-radius: 12px;
            font-weight: 600;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px);
        }

        /* ===== ACTIVITY PANELS ===== */
        .activity-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .activity-header {
            background: var(--gradient-primary);
            color: white;
            padding: 20px 25px;
            border: none;
        }

        .activity-header h5 {
            margin: 0;
            font-weight: 700;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 20px 25px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .activity-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(5px);
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 50px;
            height: 50px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .activity-icon.asignacion { background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white; }
        .activity-icon.vuelo { background: linear-gradient(135deg, #00b894 0%, #00cec9 100%); color: white; }
        .activity-icon.agente { background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%); color: white; }
        .activity-icon.informativo { background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%); color: white; }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #718096;
        }

        .empty-state .empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.7;
        }

        .empty-state h5 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: #4a5568;
        }

        .empty-state p {
            font-size: 14px;
            margin: 0;
        }

        /* ===== NOTIFICATION STYLES ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            font-size: 24px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s ease;
        }

        .notification-bell:hover {
            transform: scale(1.1);
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        /* ===== REFRESH BUTTON STYLES ===== */
        #refreshBtn {
            border-radius: 12px;
            font-weight: 600;
            padding: 8px 16px;
            transition: all 0.3s ease;
            border: 2px solid rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        #refreshBtn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.25);
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        #refreshBtn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        #refreshBtn .fa-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* ===== PAGINATION STYLES ===== */
        .activity-pagination {
            padding: 15px 25px;
            background: rgba(102, 126, 234, 0.05);
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .pagination-info {
            font-size: 14px;
            color: #718096;
            font-weight: 500;
        }

        .pagination-controls {
            display: flex;
            gap: 8px;
        }

        .pagination-btn {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            color: #667eea;
            border-radius: 8px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .pagination-btn:hover {
            background: #667eea;
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .pagination-btn:disabled,
        .pagination-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .pagination-btn.active {
            background: #667eea;
            color: white;
        }

        .activity-time {
            font-size: 12px;
            color: #a0aec0;
            font-weight: 500;
            margin-top: 2px;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
            pointer-events: auto;
        }

        .notifications-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .notifications-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }
 
        /* ===== THEME STYLES ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }
 
        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }
 
        .theme-dark .top-bar,
        .theme-dark .admin-stat-card,
        .theme-dark .action-card,
        .theme-dark .activity-panel {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }
 
        .theme-dark .admin-header {
            background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
        }
 
        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .notifications-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            width: 380px;
            max-height: 600px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-20px) scale(0.95);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        .notifications-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }

        .notifications-header {
            background: var(--gradient-primary);
            color: white;
            padding: 20px;
        }
        .notifications-title {
            margin: 0;
            font-weight: 700;
            font-size: 18px;
        }
        .notifications-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 12px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 8px;
        }
        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: white;
            padding: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .notifications-filters {
            display: flex;
            padding: 15px 20px;
            gap: 8px;
            background: rgba(248, 249, 250, 0.8);
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            overflow-x: auto;
        }
        .filter-btn {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        .filter-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }
        .filter-btn.active {
            background: var(--gradient-primary);
            color: white;
            border-color: transparent;
        }
        .notifications-list {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px 0;
        }
        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
        }
        .notification-item.unread {
            background: rgba(102, 126, 234, 0.02);
            border-left: 4px solid var(--primary-color);
        }
        .notification-item.read {
            opacity: 0.7;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            flex-shrink: 0;
        }
        .notification-content {
            flex: 1;
        }
        .notification-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            color: #2d3748;
        }
        .notification-time {
            font-size: 11px;
            color: #718096;
            font-weight: 500;
        }
        .notification-message {
            font-size: 13px;
            color: #4a5568;
            margin: 4px 0 0 0;
        }
        .empty-notifications {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }
        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.7;
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .admin-cards-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .admin-stat-card {
                padding: 20px;
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .stat-number {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .admin-header {
                padding: 25px 20px;
            }

            .admin-header h1 {
                font-size: 1.8rem;
            }
        }

        /* ===== ANIMATIONS ===== */
        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .slide-in-left {
            animation: slideInLeft 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.svg" alt="Swissport Logo">
        </div>
        
        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1" style="color: #2d3748; font-weight: 600; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small style="color: #718096; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>" style="position: relative; z-index: 1;"><?php echo $agente['estado']; ?></span>
            </div>
        </div>
        
        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Lobby</span>
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    <span>CREC</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link">
                    <i class="fas fa-file-pdf"></i>
                    <span>Informativos</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Mi Perfil</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Buscar Agentes</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="admin.php" class="nav-link active">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </a>
            </div>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar fade-in">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                        <i class="fas fa-cog me-2"></i>
                        Panel de Administración
                    </h2>
                    <p class="text-muted mb-0" style="font-weight: 500;">
                        <i class="fas fa-calendar me-2"></i>
                        <?php
                        if (function_exists('formatearFechaEspanol')) {
                            echo formatearFechaEspanol(date('Y-m-d'));
                        } else {
                            echo date('d/m/Y');
                        }
                        ?>
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('H:i'); ?>
                        </span>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <!-- Panel de Notificaciones -->
                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        <i class="fas fa-bell"></i>
                        <?php
                        $unread_count = 0;
                        if (!empty($notificaciones)) {
                            foreach ($notificaciones as $notif) {
                                if (!$notif['leida']) {
                                    $unread_count++;
                                }
                            }
                        }
                        if ($unread_count > 0): ?>
                            <span class="notification-counter"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Botón de actualización -->
                <button id="refreshBtn" class="btn btn-outline-primary btn-sm" onclick="refreshData()" title="Actualizar datos">
                    <i class="fas fa-sync-alt me-2"></i>
                    Actualizar Datos
                </button>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = function_exists('getUserTheme') ? getUserTheme() : 'light';
                if (function_exists('renderModernThemeSelector')) {
                    echo renderModernThemeSelector($current_theme);
                } else {
                    // Fallback simple si la función no existe
                    echo '<div class="theme-selector">
                            <button class="theme-btn" onclick="toggleTheme()" title="Cambiar tema">
                                <i class="theme-icon fas fa-moon"></i>
                            </button>
                          </div>';
                }
                ?>
            </div>
        </div>



        <!-- Mensajes -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.2);">
                <i class="fas fa-check-circle me-2"></i><?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert" style="background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.2);">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.1s">
                <div class="stat-icon primary">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number"><?php echo $stats['total_agentes']; ?></div>
                <div class="stat-label">Total Agentes</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-arrow-up me-1"></i>
                    Sistema activo
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.2s">
                <div class="stat-icon success">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo $stats['agentes_turno']; ?></div>
                <div class="stat-label">Agentes en Turno</div>
                <div class="stat-trend trend-success">
                    <i class="fas fa-check-circle me-1"></i>
                    Operativo
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.3s">
                <div class="stat-icon warning">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-number"><?php echo $stats['asignaciones_hoy']; ?></div>
                <div class="stat-label">Asignaciones Hoy</div>
                <div class="stat-trend trend-warning">
                    <i class="fas fa-calendar-day me-1"></i>
                    Hoy
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.4s">
                <div class="stat-icon info">
                    <i class="fas fa-plane"></i>
                </div>
                <div class="stat-number"><?php echo $stats['vuelos_hoy']; ?></div>
                <div class="stat-label">Vuelos Hoy</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-plane-departure me-1"></i>
                    Operaciones
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.5s">
                <div class="stat-icon warning">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-number"><?php echo $stats['colaciones_activas']; ?></div>
                <div class="stat-label">Colaciones Activas</div>
                <div class="stat-trend trend-warning">
                    <i class="fas fa-clock me-1"></i>
                    En curso
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.6s">
                <div class="stat-icon <?php echo $stats['notificaciones_pendientes'] > 0 ? 'danger' : 'success'; ?>">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="stat-number"><?php echo $stats['notificaciones_pendientes']; ?></div>
                <div class="stat-label">Notificaciones Pendientes</div>
                <div class="stat-trend <?php echo $stats['notificaciones_pendientes'] > 0 ? 'trend-down' : 'trend-success'; ?>">
                    <i class="fas fa-<?php echo $stats['notificaciones_pendientes'] > 0 ? 'exclamation-triangle' : 'check-circle'; ?> me-1"></i>
                    <?php echo $stats['notificaciones_pendientes'] > 0 ? 'Pendientes' : 'Al día'; ?>
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.7s">
                <div class="stat-icon <?php echo $stats['informativos_sin_leer'] > 0 ? 'warning' : 'success'; ?>">
                    <i class="fas fa-file-pdf"></i>
                </div>
                <div class="stat-number"><?php echo $stats['informativos_nuevos']; ?></div>
                <div class="stat-label">Informativos Nuevos</div>
                <div class="stat-trend <?php echo $stats['informativos_sin_leer'] > 0 ? 'trend-warning' : 'trend-success'; ?>">
                    <i class="fas fa-<?php echo $stats['informativos_sin_leer'] > 0 ? 'exclamation-circle' : 'check-circle'; ?> me-1"></i>
                    <?php echo $stats['informativos_sin_leer'] > 0 ? $stats['informativos_sin_leer'] . ' sin leer' : 'Todo leído'; ?>
                </div>
            </div>

            <div class="admin-stat-card slide-in-left" style="animation-delay: 0.8s">
                <div class="stat-icon primary">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number"><?php echo $stats_sistema['vuelos_semana']; ?></div>
                <div class="stat-label">Vuelos Esta Semana</div>
                <div class="stat-trend trend-up">
                    <i class="fas fa-calendar-week me-1"></i>
                    7 días
                </div>
            </div>
        </div>

        <!-- Admin Cards Grid -->
        <div class="admin-cards-grid">
            <!-- Card 1: Gestión de Agentes -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Gestión de Agentes</h5>
                        <small class="text-muted">Administrar usuarios del sistema</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="admin/agentes.php" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        Listado de Agentes
                    </a>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAgentModal">
                        <i class="fas fa-cog me-2"></i>
                        Admin de Agentes
                    </button>
                </div>
            </div>

            <!-- Card 2: Gestión de Vuelos -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Gestión de Vuelos</h5>
                        <small class="text-muted">Control de operaciones aéreas</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="crec/vuelos.php" class="btn btn-outline-success">
                        <i class="fas fa-list me-2"></i>
                        Ver Vuelos
                    </a>
                    <a href="admin/reportes.php" class="btn btn-success">
                        <i class="fas fa-chart-bar me-2"></i>
                        Reportes
                    </a>
                </div>
            </div>

            <!-- Card 3: Configuración -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white;">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Configuración</h5>
                        <small class="text-muted">Ajustes del sistema</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="admin/configuracion.php" class="btn btn-outline-warning">
                        <i class="fas fa-sliders-h me-2"></i>
                        Configuración General
                    </a>
                    <a href="admin/backup.php" class="btn btn-warning">
                        <i class="fas fa-database me-2"></i>
                        Backup y Restauración
                    </a>
                </div>
            </div>

            <!-- Card 4: Notificaciones -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white;">
                        <i class="fas fa-bell"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Notificaciones</h5>
                        <small class="text-muted">Sistema de comunicaciones</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <a href="admin/notificaciones.php" class="btn btn-outline-info">
                        <i class="fas fa-list me-2"></i>
                        Gestionar Notificaciones
                    </a>
                    <a href="admin/enviar_masivo.php" class="btn btn-info">
                        <i class="fas fa-bullhorn me-2"></i>
                        Envío Masivo
                    </a>
                </div>
            </div>

            <!-- Card 5: Próximamente -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;">
                        <i class="fas fa-tools"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Próximamente</h5>
                        <small class="text-muted">Funcionalidad en desarrollo</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-clock me-2"></i>
                        En desarrollo
                    </button>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-hourglass-half me-2"></i>
                        Próximamente
                    </button>
                </div>
            </div>

            <!-- Card 6: Próximamente -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #9b59b6, #8e44ad); color: white;">
                        <i class="fas fa-puzzle-piece"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Próximamente</h5>
                        <small class="text-muted">Funcionalidad en desarrollo</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-clock me-2"></i>
                        En desarrollo
                    </button>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-hourglass-half me-2"></i>
                        Próximamente
                    </button>
                </div>
            </div>

            <!-- Card 7: Próximamente -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white;">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Próximamente</h5>
                        <small class="text-muted">Funcionalidad en desarrollo</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-clock me-2"></i>
                        En desarrollo
                    </button>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-hourglass-half me-2"></i>
                        Próximamente
                    </button>
                </div>
            </div>

            <!-- Card 8: Próximamente -->
            <div class="action-card">
                <div class="action-header">
                    <div class="action-icon" style="background: linear-gradient(135deg, #1abc9c, #16a085); color: white;">
                        <i class="fas fa-star"></i>
                    </div>
                    <div>
                        <h5 class="mb-1">Próximamente</h5>
                        <small class="text-muted">Funcionalidad en desarrollo</small>
                    </div>
                </div>
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="fas fa-clock me-2"></i>
                        En desarrollo
                    </button>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-hourglass-half me-2"></i>
                        Próximamente
                    </button>
                </div>
            </div>
        </div>

        <!-- Recent Activity and Agents -->
        <div class="row">
            <div class="col-lg-8 mb-4">
                <div class="activity-panel">
                    <div class="activity-header">
                        <h5><i class="fas fa-history me-2"></i>Actividad Reciente (24h)</h5>
                    </div>
                    <div class="p-0">
                        <?php if (empty($actividad_reciente)): ?>
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-moon"></i></div>
                                <h5>Todo tranquilo por aquí</h5>
                                <p>No se ha registrado nueva actividad en las últimas 24 horas.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($actividad_reciente as $actividad): ?>
                                <div class="activity-item">
                                    <div class="activity-icon <?php echo $actividad['tipo']; ?>">
                                        <i class="fas <?php
                                            echo match($actividad['tipo']) {
                                                'asignacion' => 'fa-tasks',
                                                'vuelo' => 'fa-plane',
                                                'agente' => 'fa-user-plus',
                                                default => 'fa-info'
                                            };
                                        ?>"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <p class="mb-1" style="font-weight: 500; color: #4a5568;"><?php echo htmlspecialchars($actividad['descripcion']); ?></p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted" style="font-weight: 500;"><?php echo timeAgo($actividad['fecha']); ?></small>
                                            <div class="activity-time">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo date('H:i:s', strtotime($actividad['fecha'])); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Paginación -->
                    <?php if ($total_records > $records_per_page): ?>
                        <div class="activity-pagination">
                            <div class="pagination-info">
                                Mostrando <?php echo (($page - 1) * $records_per_page) + 1; ?>-<?php echo min($page * $records_per_page, $total_records); ?> de <?php echo $total_records; ?> registros
                            </div>
                            <div class="pagination-controls">
                                <?php if ($page > 1): ?>
                                    <a href="?activity_page=<?php echo $page - 1; ?>" class="pagination-btn">
                                        <i class="fas fa-chevron-left"></i> Anterior
                                    </a>
                                <?php else: ?>
                                    <span class="pagination-btn disabled">
                                        <i class="fas fa-chevron-left"></i> Anterior
                                    </span>
                                <?php endif; ?>

                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                for ($i = $start_page; $i <= $end_page; $i++): ?>
                                    <a href="?activity_page=<?php echo $i; ?>" class="pagination-btn <?php echo $i == $page ? 'active' : ''; ?>">
                                        <?php echo $i; ?>
                                    </a>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <a href="?activity_page=<?php echo $page + 1; ?>" class="pagination-btn">
                                        Siguiente <i class="fas fa-chevron-right"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="pagination-btn disabled">
                                        Siguiente <i class="fas fa-chevron-right"></i>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="col-lg-4 mb-4">
                <div class="activity-panel">
                    <div class="activity-header" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                        <h5><i class="fas fa-user-friends me-2"></i>Agentes Recientes</h5>
                    </div>
                    <div class="p-0">
                        <?php if (empty($agentes_recientes)): ?>
                            <div class="empty-state">
                                <div class="empty-icon"><i class="fas fa-user-plus"></i></div>
                                <h5>No hay nuevos agentes</h5>
                                <p>No se han registrado nuevos agentes recientemente.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($agentes_recientes as $ag): ?>
                                <div class="activity-item">
                                    <img src="<?php echo getAvatarUrl($ag['foto_perfil']); ?>" alt="Avatar" class="user-avatar" style="width: 45px; height: 45px; border-width: 2px; margin-right: 15px;">
                                    <div class="flex-grow-1">
                                        <p class="mb-0" style="font-weight: 600; color: #2d3748;"><?php echo htmlspecialchars($ag['nombre']); ?></p>
                                        <small class="text-muted" style="font-weight: 500;">
                                            <?php echo htmlspecialchars($ag['grupo_nombre'] ?? 'Sin grupo'); ?> • <?php echo timeAgo($ag['created_at']); ?>
                                        </small>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Overlay para cerrar notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtros de categorías -->
        <div class="notifications-filters">
            <button class="filter-btn active" data-filter="all">
                📋 Todas
            </button>
            <button class="filter-btn" data-filter="operacional">
                ✈️ Operacional
            </button>
            <button class="filter-btn" data-filter="social">
                👥 Social
            </button>
            <button class="filter-btn" data-filter="urgente">
                🚨 Urgente
            </button>
            <button class="filter-btn" data-filter="informativa">
                ℹ️ Informativa
            </button>
            <button class="filter-btn" data-filter="general">
                ⚙️ General
            </button>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">📭</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativa' => 'ℹ️',
                                default => '⚙️'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                                <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                            </div>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>

                            <?php if (isset($notif['categoria']) && $notif['categoria'] == 'social' && strpos($notif['titulo'], 'solicitud de amistad') !== false): ?>
                                <div class="notification-actions">
                                    <button class="btn-notification-small btn-accept" onclick="acceptFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✓ Aceptar
                                    </button>
                                    <button class="btn-notification-small btn-reject" onclick="rejectFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✗ Rechazar
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal Crear Agente -->
    <div class="modal fade" id="createAgentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-user-plus me-2"></i>Crear Nuevo Agente
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <input type="hidden" name="action" value="crear_agente_admin">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="nombre" class="form-label">Nombre Completo *</label>
                                <input type="text" class="form-control" id="nombre" name="nombre" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="usuario_sabre" class="form-label">Usuario Sabre *</label>
                                <input type="text" class="form-control" id="usuario_sabre" name="usuario_sabre" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Contraseña *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="grupo_id" class="form-label">Grupo *</label>
                                <select class="form-select" id="grupo_id" name="grupo_id" required>
                                    <option value="">Seleccionar grupo</option>
                                    <?php foreach ($grupos as $grupo): ?>
                                        <option value="<?php echo $grupo['id']; ?>">
                                            <?php echo htmlspecialchars($grupo['nombre']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="telefono" class="form-label">WhatsApp</label>
                                <input type="text" class="form-control" id="telefono" name="telefono" placeholder="+56912345678">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Crear Agente
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        document.addEventListener('DOMContentLoaded', function() {
            const theme = localStorage.getItem('theme') || 'light';
            applyTheme(theme);
            initializeNotifications();

            // Asegurar que los botones de las tarjetas sean clickeables
            document.querySelectorAll('.admin-cards-grid .action-card .btn').forEach(function(btn) {
                // Forzar que el botón sea clickeable
                btn.style.pointerEvents = 'auto';
                btn.style.zIndex = '1000';
                btn.style.position = 'relative';

                // Agregar event listener para debug
                btn.addEventListener('click', function(e) {
                    console.log('Button clicked:', this.textContent.trim());

                    // Si es un enlace, permitir navegación
                    if (this.tagName === 'A' && this.href) {
                        console.log('Navigating to:', this.href);
                        return true;
                    }

                    // Si es un botón con modal
                    if (this.hasAttribute('data-bs-toggle')) {
                        console.log('Opening modal:', this.getAttribute('data-bs-target'));
                        return true;
                    }
                });
            });
        });

        function applyTheme(theme) {
            document.body.classList.remove('theme-light', 'theme-dark');
            document.body.classList.add(`theme-${theme}`);
        }

        function initializeNotifications() {
            setupNotificationFilters();
            setupNotificationClicks();
        }

        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');
            panel.classList.toggle('show');
            overlay.classList.toggle('show');
        }

        function closeNotifications() {
            document.getElementById('notificationsPanel').classList.remove('show');
            document.getElementById('notificationsOverlay').classList.remove('show');
        }

        function setupNotificationFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const notificationItems = document.querySelectorAll('.notification-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    const filter = this.dataset.filter;

                    notificationItems.forEach(item => {
                        item.style.display = (filter === 'all' || item.dataset.category === filter) ? 'flex' : 'none';
                    });
                });
            });
        }

        function setupNotificationClicks() {
            document.querySelectorAll('.notification-item').forEach(item => {
                item.addEventListener('click', function() {
                    markNotificationAsRead(this.dataset.id);
                    this.classList.remove('unread');
                    this.classList.add('read');
                    updateNotificationCounter();
                });
            });
        }

        function markAllAsRead() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                markNotificationAsRead(item.dataset.id);
            });
            updateNotificationCounter();
        }

        function refreshNotifications() {
            location.reload();
        }

        function markNotificationAsRead(notificationId) {
            fetch('ajax/mark_notification_read.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ notification_id: notificationId })
            });
        }

        function updateNotificationCounter() {
            const count = document.querySelectorAll('.notification-item.unread').length;
            const counter = document.querySelector('.notification-counter');
            if (counter) {
                if (count > 0) {
                    counter.textContent = count;
                    counter.style.display = 'flex';
                } else {
                    counter.style.display = 'none';
                }
            }
        }

        // Auto-dismiss alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Función para actualizar datos manualmente
        function refreshData() {
            const refreshBtn = document.getElementById('refreshBtn');
            const refreshIcon = refreshBtn.querySelector('i');

            // Mostrar animación de carga
            refreshIcon.classList.add('fa-spin');
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Actualizando...';

            // Simular carga y recargar página
            setTimeout(function() {
                location.reload();
            }, 1000);
        }
    </script>
</body>
</html>
