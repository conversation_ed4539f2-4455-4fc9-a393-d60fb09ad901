<?php
// Asegurar codificación UTF-8
header('Content-Type: text/html; charset=UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once __DIR__ . '/../config/functions.php';

// Verificar que el usuario esté logueado
requireLogin();

// Obtener datos del agente actual
$agente = getCurrentAgent();
if (!$agente) {
    header('Location: login.php');
    exit();
}

// Configurar zona horaria
date_default_timezone_set('America/Santiago');

// Obtener conexión a la base de datos
$database = new Database();
$conn = $database->getConnection();

// Función para obtener vuelos con filtros
function getVuelos($conn, $filtros = []) {
    $where_conditions = ["v.fecha_vuelo = CURDATE()"];
    $params = [];
    
    // Aplicar filtros
    if (!empty($filtros['fecha'])) {
        $where_conditions[] = "v.fecha_vuelo = ?";
        $params[] = $filtros['fecha'];
    }
    
    if (!empty($filtros['terminal'])) {
        $where_conditions[] = "t.codigo = ?";
        $params[] = $filtros['terminal'];
    }
    
    if (!empty($filtros['estado'])) {
        $where_conditions[] = "v.estado = ?";
        $params[] = $filtros['estado'];
    }
    
    if (!empty($filtros['tipo'])) {
        $where_conditions[] = "v.tipo = ?";
        $params[] = $filtros['tipo'];
    }
    
    $where_clause = implode(' AND ', $where_conditions);
    
    $query = "SELECT v.*, 
                     t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                     ag1.nombre as agente_p1_nombre, ag2.nombre as agente_p2_nombre,
                     CASE
                         WHEN v.cancelado = 1 THEN 'CANCELADO'
                         WHEN v.critico = 1 OR TIME(NOW()) > TIME(v.hora_cierre) THEN 'CRÍTICO'
                         WHEN v.sobreventa = 1 OR v.overbooking < 0 THEN 'SOBREVENTA'
                         WHEN v.charter = 1 THEN 'CHARTER'
                         ELSE 'NORMAL'
                     END as estado_vuelo
              FROM vuelos v
              LEFT JOIN terminales t ON v.terminal_id = t.id
              LEFT JOIN agentes ag1 ON v.agente_p1_id = ag1.id
              LEFT JOIN agentes ag2 ON v.agente_p2_id = ag2.id
              WHERE $where_clause
              ORDER BY v.hora_programada";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// Obtener estadísticas de vuelos
function getVuelosStats($conn) {
    $stats = [];
    
    // Total vuelos del día
    $query = "SELECT COUNT(*) as total FROM vuelos WHERE fecha_vuelo = CURDATE()";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['total_vuelos'] = $stmt->fetch()['total'];
    
    // Arribos
    $query = "SELECT COUNT(*) as total FROM vuelos WHERE fecha_vuelo = CURDATE() AND tipo = 'arribo'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['arribos'] = $stmt->fetch()['total'];
    
    // Salidas
    $query = "SELECT COUNT(*) as total FROM vuelos WHERE fecha_vuelo = CURDATE() AND tipo = 'salida'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['salidas'] = $stmt->fetch()['total'];
    
    // Vuelos críticos
    $query = "SELECT COUNT(*) as total FROM vuelos 
              WHERE fecha_vuelo = CURDATE() 
              AND (critico = 1 OR TIME(NOW()) > TIME(hora_cierre))";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['criticos'] = $stmt->fetch()['total'];
    
    // Salidas a tiempo
    $query = "SELECT COUNT(*) as total FROM vuelos 
              WHERE fecha_vuelo = CURDATE() 
              AND tipo = 'salida' 
              AND estado = 'completado'
              AND TIME(NOW()) <= TIME(hora_programada)";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['a_tiempo'] = $stmt->fetch()['total'];
    
    // Retrasados
    $query = "SELECT COUNT(*) as total FROM vuelos 
              WHERE fecha_vuelo = CURDATE() 
              AND estado = 'retrasado'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $stats['retrasados'] = $stmt->fetch()['total'];
    
    return $stats;
}

// Procesar filtros si se envían
$filtros = [];
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!empty($_POST['fecha'])) $filtros['fecha'] = $_POST['fecha'];
    if (!empty($_POST['terminal'])) $filtros['terminal'] = $_POST['terminal'];
    if (!empty($_POST['estado'])) $filtros['estado'] = $_POST['estado'];
    if (!empty($_POST['tipo'])) $filtros['tipo'] = $_POST['tipo'];
}

// Obtener datos
$vuelos = getVuelos($conn, $filtros);
$stats = getVuelosStats($conn);

// Separar vuelos por tipo
$salidas = array_filter($vuelos, fn($v) => $v['tipo'] === 'salida');
$arribos = array_filter($vuelos, fn($v) => $v['tipo'] === 'arribo');

// Función para obtener emoji de observaciones
function getObservacionEmoji($vuelo) {
    if ($vuelo['cancelado']) return '🚫';
    if ($vuelo['critico'] || (time() > strtotime($vuelo['hora_cierre']))) return '🚨';
    if ($vuelo['charter']) return '⛏';
    if ($vuelo['sobreventa'] || $vuelo['overbooking'] < 0) return '🟡';
    if ($vuelo['wchr_wchc_wchs']) return '👨‍🦽';
    if ($vuelo['depa_depu']) return '👮🏼‍♂️';
    if ($vuelo['inad']) return '🎟';
    if ($vuelo['zz_observaciones']) return '🔫';
    return '🧡';
}

// Función para obtener estado emoji
function getEstadoEmoji($estado_vuelo) {
    return match($estado_vuelo) {
        'CRÍTICO' => '🚨',
        'CANCELADO' => '🚫',
        'CHARTER' => '⛏',
        'SOBREVENTA' => '🟡',
        'RETRASADO' => '⏰',
        default => '✅'
    };
}

// Función para obtener observaciones detalladas
function getObservacionesDetalladas($vuelo) {
    $obs = [];
    if ($vuelo['charter']) $obs[] = '⛏CHARTER';
    if ($vuelo['critico']) $obs[] = '🚨CRITICO';
    if ($vuelo['sobreventa'] || $vuelo['overbooking'] < 0) $obs[] = '🟡SOBREVENTA';
    if ($vuelo['wchr_wchc_wchs']) $obs[] = '👨‍🦽WCHR/WCHS/WCHC';
    if ($vuelo['depa_depu']) $obs[] = '👮🏼‍♂️DEPU/DEPA';
    if ($vuelo['inad']) $obs[] = '🎟INAD';
    if ($vuelo['zz_observaciones']) $obs[] = '🔫ZZ';
    if (empty($obs)) $obs[] = '🧡SIN OBS';
    return $obs;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vuelos - SwissportAgents</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../css/theme-selector.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --sidebar-width: 280px;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%);
            --gradient-info: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-soft: 0 10px 40px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            background-attachment: fixed !important;
            min-height: 100vh;
            overflow-x: hidden;
            font-feature-settings: "liga" 1, "kern" 1;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            color: #2d3748;
        }

        /* Forzar background en todos los estados */
        html, body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            background-attachment: fixed !important;
        }



        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            color: #2d3748;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-soft);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            margin: 20px;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            filter: brightness(0) invert(1);
        }

        .user-info {
            padding: 25px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.05;
            z-index: 0;
        }

        .user-info .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            margin-bottom: 10px;
            object-fit: cover;
            position: relative;
            z-index: 1;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-disponible {
            background: rgba(72, 187, 120, 0.2);
            color: #2f855a;
            border: 1px solid rgba(72, 187, 120, 0.3);
        }

        .status-colacion {
            background: rgba(237, 137, 54, 0.2);
            color: #c05621;
            border: 1px solid rgba(237, 137, 54, 0.3);
        }

        .status-ocupado {
            background: rgba(245, 101, 101, 0.2);
            color: #c53030;
            border: 1px solid rgba(245, 101, 101, 0.3);
        }

        .status-fuera {
            background: rgba(113, 128, 150, 0.2);
            color: #4a5568;
            border: 1px solid rgba(113, 128, 150, 0.3);
        }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin: 8px 20px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            color: #4a5568;
            text-decoration: none;
            border-radius: 15px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            opacity: 1;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(5px);
            box-shadow: var(--shadow-soft);
        }

        .nav-link i {
            margin-right: 12px;
            font-size: 1.1rem;
            width: 20px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .top-bar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.03;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 35px 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .stat-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 0 auto 20px;
            position: relative;
            z-index: 1;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .stat-icon.primary {
            background: var(--gradient-primary);
            color: white;
        }

        .stat-icon.success {
            background: var(--gradient-success);
            color: white;
        }

        .stat-icon.warning {
            background: var(--gradient-warning);
            color: white;
        }

        .stat-icon.danger {
            background: var(--gradient-danger);
            color: white;
        }

        .stat-icon.info {
            background: var(--gradient-info);
            color: white;
        }

        .stat-number {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
            line-height: 1;
        }

        .stat-card:hover .stat-number {
            transform: scale(1.05);
        }

        .stat-label {
            color: #4a5568;
            font-size: 15px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
            margin: 0;
        }

        .card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .card:hover::before {
            opacity: 0.02;
        }

        .card-header {
            background: transparent;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 25px 30px;
            position: relative;
            z-index: 1;
        }

        .card-body {
            padding: 30px;
            position: relative;
            z-index: 1;
        }

        .search-form {
            background: rgba(248, 249, 250, 0.8);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .search-form .form-control,
        .search-form .form-select {
            border-radius: 10px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .search-form .form-control:focus,
        .search-form .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-search {
            background: var(--gradient-primary);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-reset {
            background: var(--gradient-warning);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.3);
            color: white;
        }

        .btn-export {
            background: var(--gradient-success);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
            color: white;
        }

        .table-responsive {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow-soft);
        }

        .table {
            margin-bottom: 0;
            background: white;
        }

        .table thead th {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 20px 15px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
        }

        .table tbody td {
            padding: 15px;
            border-color: rgba(0, 0, 0, 0.05);
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.05);
        }

        .vuelo-critico {
            background-color: rgba(255, 107, 107, 0.1) !important;
            border-left: 4px solid #ff6b6b;
        }

        .vuelo-critico:hover {
            background-color: rgba(255, 107, 107, 0.15) !important;
        }

        .badge-terminal-nac {
            background: var(--gradient-primary);
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge-terminal-inter {
            background: var(--gradient-success);
            color: white;
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge-estado {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .badge-critico {
            background: var(--gradient-danger);
            color: white;
        }

        .badge-normal {
            background: var(--gradient-success);
            color: white;
        }

        .badge-charter {
            background: var(--gradient-info);
            color: white;
        }

        .badge-sobreventa {
            background: var(--gradient-warning);
            color: white;
        }

        /* Estilos para OVBK negativo - ROJO Y NEGRITAS */
        .overbooking-negativo {
            color: #dc3545 !important;
            font-weight: bold !important;
            background-color: rgba(220, 53, 69, 0.1) !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            border: 1px solid rgba(220, 53, 69, 0.3) !important;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }

        .observaciones-list {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }

        .obs-badge {
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 4px;
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                z-index: 9999;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .top-bar {
                padding: 20px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .stat-card {
                padding: 25px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }

            .search-form {
                padding: 20px;
            }

            .table-responsive {
                font-size: 0.85rem;
            }
        }

        /* ===== SISTEMA DE TEMAS ===== */

        /* Tema Oscuro */
        .theme-dark {
            color-scheme: dark;
        }

        .theme-dark body,
        .theme-dark html {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
            background-attachment: fixed !important;
            color: #f7fafc !important;
        }



        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .sidebar-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .user-info {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #f7fafc !important;
        }

        .theme-dark .nav-link {
            color: #e2e8f0 !important;
        }

        .theme-dark .nav-link:hover,
        .theme-dark .nav-link.active {
            color: white !important;
            background: rgba(102, 126, 234, 0.2) !important;
        }

        .theme-dark .top-bar {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .card,
        .theme-dark .stat-card {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .card-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
            border-bottom: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-label {
            color: #a0aec0 !important;
        }

        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .theme-dark .modal-content {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .modal-header {
            border-bottom-color: #4a5568 !important;
        }

        .theme-dark .modal-footer {
            border-top-color: #4a5568 !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: var(--primary-color) !important;
            color: #f7fafc !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        }

        .theme-dark .form-check-input:checked {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
        }

        .theme-dark .btn-secondary {
            background: rgba(113, 128, 150, 0.8) !important;
            border-color: #718096 !important;
            color: #f7fafc !important;
        }

        .theme-dark .btn-primary {
            background: var(--gradient-primary) !important;
            border: none !important;
        }

        /* Estilos para botón de editar vuelo */
        .btn-edit-flight {
            background: var(--gradient-warning);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
        }

        .btn-edit-flight:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.4);
            color: white;
        }

        .btn-edit-flight:active {
            transform: translateY(0) scale(0.98);
        }

        .btn-edit-flight i {
            font-size: 1rem;
        }

        .theme-dark .btn-edit-flight {
            background: var(--gradient-warning) !important;
            color: white !important;
        }

        .theme-dark .btn-edit-flight:hover {
            color: white !important;
        }

        /* Estilos para botón de eliminar vuelo */
        .btn-delete-flight {
            background: var(--gradient-danger);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 36px;
            height: 36px;
        }

        .btn-delete-flight:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
            color: white;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .btn-delete-flight:active {
            transform: translateY(0) scale(0.98);
        }

        .btn-delete-flight i {
            font-size: 1rem;
        }

        .theme-dark .btn-delete-flight {
            background: var(--gradient-danger) !important;
            color: white !important;
        }

        .theme-dark .btn-delete-flight:hover {
            color: white !important;
        }

        /* OVBK negativo ya definido arriba */

        /* OVBK mantiene los mismos colores en tema oscuro */

        /* Badge para estado de sobreventa */
        .badge-sobreventa {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
            color: white !important;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.3);
        }

        .theme-dark .search-form {
            background: rgba(45, 55, 72, 0.8) !important;
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: var(--primary-color) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .table {
            background: rgba(26, 32, 44, 0.8) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .table thead th {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
            color: white !important;
        }

        .theme-dark .table tbody td {
            border-color: rgba(255, 255, 255, 0.1) !important;
        }

        .theme-dark .table tbody tr:hover {
            background-color: rgba(102, 126, 234, 0.1) !important;
        }

        /* Estilos específicos para tema oscuro en tablas de vuelos */
        .theme-dark .table tbody td {
            background: rgba(26, 32, 44, 0.6) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .table tbody tr {
            background: rgba(26, 32, 44, 0.4) !important;
        }

        .theme-dark .table tbody tr:nth-child(even) {
            background: rgba(45, 55, 72, 0.4) !important;
        }

        /* Badges de terminal en tema oscuro */
        .theme-dark .badge-terminal-nac {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
            color: white !important;
        }

        .theme-dark .badge-terminal-inter {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
            color: white !important;
        }

        /* Badges de observaciones en tema oscuro */
        .theme-dark .obs-badge {
            background: rgba(102, 126, 234, 0.2) !important;
            color: #a5b4fc !important;
            border: 1px solid rgba(102, 126, 234, 0.3) !important;
        }

        /* Badges de estado en tema oscuro */
        .theme-dark .badge-estado {
            color: white !important;
        }

        .theme-dark .badge-normal {
            background: linear-gradient(135deg, #38a169 0%, #2f855a 100%) !important;
        }

        .theme-dark .badge-critico {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
        }

        .theme-dark .badge-charter {
            background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%) !important;
        }

        .theme-dark .badge-sobreventa {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
        }

        /* Texto fuerte en tema oscuro */
        .theme-dark .table tbody td strong {
            color: #f7fafc !important;
        }

        /* Filas críticas en tema oscuro */
        .theme-dark .vuelo-critico {
            background-color: rgba(229, 62, 62, 0.1) !important;
            border-left: 4px solid #e53e3e !important;
        }

        .theme-dark .vuelo-critico:hover {
            background-color: rgba(229, 62, 62, 0.2) !important;
        }

        .theme-dark .dropdown-menu {
            background: rgba(45, 55, 72, 0.95) !important;
            border: 1px solid #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .dropdown-item {
            color: #e2e8f0 !important;
        }

        .theme-dark .dropdown-item:hover {
            background: rgba(102, 126, 234, 0.2) !important;
            color: white !important;
        }

        .theme-dark .badge {
            background: rgba(102, 126, 234, 0.8) !important;
            color: white !important;
        }

        /* Cards y paneles en tema oscuro */
        .theme-dark .card {
            background: rgba(26, 32, 44, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .card-header {
            background: rgba(45, 55, 72, 0.8) !important;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #f7fafc !important;
        }

        .theme-dark .card-body {
            background: rgba(26, 32, 44, 0.6) !important;
            color: #e2e8f0 !important;
        }

        /* Stat cards en tema oscuro */
        .theme-dark .stat-card {
            background: rgba(26, 32, 44, 0.9) !important;
            border: 1px solid rgba(255, 255, 255, 0.1) !important;
            color: #e2e8f0 !important;
        }

        .theme-dark .stat-card h3 {
            color: #f7fafc !important;
        }

        .theme-dark .stat-card p {
            color: #a0aec0 !important;
        }

        /* Tema Claro */
        .theme-light {
            color-scheme: light;
        }

        .theme-light .sidebar {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #2d3748 !important;
            border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        .theme-light .user-info {
            background: rgba(255, 255, 255, 0.8) !important;
            color: #2d3748 !important;
        }

        .theme-light body,
        .theme-light html {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
            background-attachment: fixed !important;
            color: #2d3748 !important;
        }



        /* Estilos para usuario y notificaciones */
        .user-info-topbar {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .user-name {
            font-weight: 600;
            color: var(--text-primary);
        }

        .notification-bell-container {
            position: relative;
            display: inline-block;
        }

        .notification-bell {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .notification-bell::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-bell:hover::before {
            opacity: 1;
        }

        .notification-bell:hover {
            transform: translateY(-2px) scale(1.05);
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .notification-bell:active {
            transform: translateY(0) scale(0.98);
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        @keyframes bellRing {
            0%, 100% { transform: rotate(0deg); }
            10%, 30%, 50%, 70%, 90% { transform: rotate(-10deg); }
            20%, 40%, 60%, 80% { transform: rotate(10deg); }
        }

        .notification-bell.ringing {
            animation: bellRing 0.6s ease-in-out;
        }

        /* Tema oscuro para notificaciones */
        .theme-dark .notification-bell {
            background: rgba(45, 55, 72, 0.9) !important;
            border-color: rgba(102, 126, 234, 0.3) !important;
            color: #f7fafc !important;
        }

        .theme-dark .notification-bell:hover {
            background: rgba(74, 85, 104, 0.9) !important;
            border-color: rgba(102, 126, 234, 0.6) !important;
        }

        .theme-selector {
            position: relative;
        }

        .theme-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            color: var(--text-primary);
            cursor: pointer;
            padding: 10px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }

        /* Estilos para botones de crear vuelos */
        .flight-buttons-container {
            display: flex;
            gap: 10px;
        }

        .btn-create-departure,
        .btn-create-arrival {
            background: var(--gradient-success);
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
            display: flex;
            align-items: center;
            text-transform: none;
            letter-spacing: 0.3px;
            position: relative;
            overflow: hidden;
        }

        .btn-create-departure {
            background: var(--gradient-info);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-create-arrival {
            background: var(--gradient-warning);
            box-shadow: 0 4px 15px rgba(250, 112, 154, 0.3);
        }

        .btn-create-departure::before,
        .btn-create-arrival::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .btn-create-departure:hover::before,
        .btn-create-arrival:hover::before {
            opacity: 1;
        }

        .btn-create-departure:hover,
        .btn-create-arrival:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-create-arrival:hover {
            box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
        }

        .btn-create-departure i,
        .btn-create-departure span,
        .btn-create-arrival i,
        .btn-create-arrival span {
            position: relative;
            z-index: 1;
        }

        .btn-create-departure i,
        .btn-create-arrival i {
            font-size: 1rem;
        }

        @media (max-width: 768px) {
            .flight-buttons-container {
                flex-direction: column;
                gap: 8px;
            }

            .btn-create-departure,
            .btn-create-arrival {
                padding: 10px 14px;
                font-size: 0.8rem;
            }

            .btn-create-departure span,
            .btn-create-arrival span {
                display: none;
            }
        }

        /* ===== PANEL DE NOTIFICACIONES ===== */
        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-panel {
            position: fixed;
            top: 0;
            right: -400px;
            width: 400px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 9999;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            flex-direction: column;
        }

        .notifications-panel.show {
            right: 0;
        }

        .notifications-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--gradient-primary);
            color: white;
        }

        .btn-close-notifications {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .btn-close-notifications:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .notifications-filters {
            padding: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: none;
            border: 1px solid rgba(102, 126, 234, 0.3);
            color: var(--primary-color);
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .notifications-body {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(-5px);
        }

        .notification-item.unread {
            border-left: 4px solid var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .notification-icon.operacional {
            background: rgba(79, 172, 254, 0.2);
            color: #4facfe;
        }

        .notification-icon.social {
            background: rgba(102, 126, 234, 0.2);
            color: #667eea;
        }

        .notification-icon.urgente {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
        }

        .notification-icon.sistema {
            background: rgba(113, 128, 150, 0.2);
            color: #718096;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--text-primary);
        }

        .notification-message {
            font-size: 0.8rem;
            color: var(--text-secondary);
            margin-bottom: 5px;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 0.7rem;
            color: var(--text-muted);
        }

        .notification-dot {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 8px;
            height: 8px;
            background: var(--primary-color);
            border-radius: 50%;
        }

        /* Tema oscuro para notificaciones */
        .theme-dark .notifications-panel {
            background: rgba(26, 32, 44, 0.95);
            border-left-color: rgba(255, 255, 255, 0.1);
        }

        .theme-dark .notifications-filters {
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        .theme-dark .notification-item {
            background: rgba(45, 55, 72, 0.8);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .theme-dark .notification-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body class="theme-light">
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <img src="../assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="../<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1" style="color: #2d3748; font-weight: 600; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small style="color: #718096; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>" style="position: relative; z-index: 1;">
                    <?php echo htmlspecialchars($agente['estado'] ?? '🏠Fuera de Turno'); ?>
                </span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="../index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="../asignaciones.php" class="nav-link">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="../colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="../lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Lobby</span>
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="vuelos.php" class="nav-link active">
                    <i class="fas fa-plane"></i>
                    <span>CREC</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="../perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Perfil</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="../informativos.php" class="nav-link">
                    <i class="fas fa-info-circle"></i>
                    <span>Informativos</span>
                </a>
            </div>
            <?php if (hasPermission('admin_access')): ?>
            <div class="nav-item">
                <a href="../admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="../logout.php" class="nav-link">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </nav>
    </nav>

    <!-- Panel de Notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="hideNotifications()"></div>
    <div class="notifications-panel" id="notificationsPanel">
        <div class="notifications-header">
            <h5 class="mb-0">
                <i class="fas fa-bell me-2"></i>
                Notificaciones
            </h5>
            <button class="btn-close-notifications" onclick="hideNotifications()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notifications-filters">
            <button class="filter-btn active" data-filter="all">Todas</button>
            <button class="filter-btn" data-filter="operacional">✈️ Operacional</button>
            <button class="filter-btn" data-filter="social">👥 Social</button>
            <button class="filter-btn" data-filter="urgente">🚨 Urgente</button>
        </div>
        <div class="notifications-body">
            <div id="notificationsList">
                <div class="text-center py-4">
                    <i class="fas fa-spinner fa-spin fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-0">Cargando notificaciones...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar fade-in">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                        ✈️ Gestión de Vuelos
                    </h2>
                    <p class="text-muted mb-0" style="font-weight: 500;">
                        Control y seguimiento de operaciones aéreas - <?php echo formatearFechaEspanol(date('Y-m-d')); ?>
                    </p>
                </div>
            </div>
            <div class="d-flex align-items-center gap-3">

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>

                <!-- Panel de Notificaciones -->
                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <span class="notification-counter" style="display: none;">0</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="stats-cards">
            <div class="stat-card fade-in" style="animation-delay: 0.1s">
                <div class="stat-icon primary">
                    <i class="fas fa-plane"></i>
                </div>
                <div class="stat-number"><?php echo $stats['total_vuelos']; ?></div>
                <div class="stat-label">Total Vuelos</div>
            </div>

            <div class="stat-card fade-in" style="animation-delay: 0.2s">
                <div class="stat-icon success">
                    <i class="fas fa-plane-arrival"></i>
                </div>
                <div class="stat-number"><?php echo $stats['arribos']; ?></div>
                <div class="stat-label">Arribos</div>
            </div>

            <div class="stat-card fade-in" style="animation-delay: 0.3s">
                <div class="stat-icon info">
                    <i class="fas fa-plane-departure"></i>
                </div>
                <div class="stat-number"><?php echo $stats['salidas']; ?></div>
                <div class="stat-label">Salidas</div>
            </div>

            <div class="stat-card fade-in" style="animation-delay: 0.4s">
                <div class="stat-icon danger">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['criticos']; ?></div>
                <div class="stat-label">Vuelos Críticos</div>
            </div>

            <div class="stat-card fade-in" style="animation-delay: 0.5s">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-number"><?php echo $stats['a_tiempo']; ?></div>
                <div class="stat-label">Salidas a Tiempo</div>
            </div>

            <div class="stat-card fade-in" style="animation-delay: 0.6s">
                <div class="stat-icon warning">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-number"><?php echo $stats['retrasados']; ?></div>
                <div class="stat-label">Retrasados</div>
            </div>
        </div>

        <!-- Buscador de Vuelos -->
        <div class="card fade-in" style="animation-delay: 0.7s">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    Buscador de Vuelos
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="search-form">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="fecha" class="form-label">Fecha</label>
                            <input type="date" class="form-control" id="fecha" name="fecha"
                                   value="<?php echo $filtros['fecha'] ?? date('Y-m-d'); ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="terminal" class="form-label">Terminal</label>
                            <select class="form-select" id="terminal" name="terminal">
                                <option value="">Todos</option>
                                <option value="NAC" <?php echo ($filtros['terminal'] ?? '') === 'NAC' ? 'selected' : ''; ?>>Nacional</option>
                                <option value="INTER" <?php echo ($filtros['terminal'] ?? '') === 'INTER' ? 'selected' : ''; ?>>Internacional</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="estado" class="form-label">Estado</label>
                            <select class="form-select" id="estado" name="estado">
                                <option value="">Todos</option>
                                <option value="programado" <?php echo ($filtros['estado'] ?? '') === 'programado' ? 'selected' : ''; ?>>Programado</option>
                                <option value="abierto" <?php echo ($filtros['estado'] ?? '') === 'abierto' ? 'selected' : ''; ?>>Abierto</option>
                                <option value="cerrado" <?php echo ($filtros['estado'] ?? '') === 'cerrado' ? 'selected' : ''; ?>>Cerrado</option>
                                <option value="retrasado" <?php echo ($filtros['estado'] ?? '') === 'retrasado' ? 'selected' : ''; ?>>Retrasado</option>
                                <option value="cancelado" <?php echo ($filtros['estado'] ?? '') === 'cancelado' ? 'selected' : ''; ?>>Cancelado</option>
                                <option value="completado" <?php echo ($filtros['estado'] ?? '') === 'completado' ? 'selected' : ''; ?>>Completado</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tipo" class="form-label">Tipo</label>
                            <select class="form-select" id="tipo" name="tipo">
                                <option value="">Todos</option>
                                <option value="arribo" <?php echo ($filtros['tipo'] ?? '') === 'arribo' ? 'selected' : ''; ?>>Arribo</option>
                                <option value="salida" <?php echo ($filtros['tipo'] ?? '') === 'salida' ? 'selected' : ''; ?>>Salida</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-search me-2">
                                <i class="fas fa-search me-1"></i>
                                Filtrar
                            </button>
                            <a href="vuelos.php" class="btn btn-reset me-2">
                                <i class="fas fa-undo me-1"></i>
                                Reset
                            </a>
                            <button type="button" class="btn btn-export" onclick="exportarExcel()">
                                <i class="fas fa-file-excel me-1"></i>
                                Exportar Excel
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Panel Lista de SALIDAS -->
        <div class="card fade-in mt-4" style="animation-delay: 0.8s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-plane-departure me-2"></i>
                        Lista de SALIDAS - <?php echo formatearFechaEspanol(date('Y-m-d')); ?>
                        <span class="badge bg-primary ms-2"><?php echo count($salidas); ?> vuelos</span>
                    </h5>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-danger btn-sm" id="deleteSelectedSalidas" onclick="deleteSelectedFlights('salidas')" style="display: none;">
                        <i class="fas fa-trash me-1"></i>
                        Eliminar Seleccionados (<span id="countSelectedSalidas">0</span>)
                    </button>
                    <button class="btn-create-departure" onclick="showCreateDepartureModal()" title="Crear Salida">
                        <i class="fas fa-plane-departure me-2"></i>
                        <span>Crear Salida</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAllSalidas" onchange="toggleAllCheckboxes('salidas', this.checked)">
                                </th>
                                <th>TERMINAL</th>
                                <th>VUELO</th>
                                <th>DESTINO</th>
                                <th>MATRÍCULA</th>
                                <th>ETD</th>
                                <th>CIERRE</th>
                                <th>RESERVAS</th>
                                <th>OVBK</th>
                                <th>AGENTE P1</th>
                                <th>AGENTE P2</th>
                                <th>OBSERVACIONES</th>
                                <th>ESTADO</th>
                                <th>ACCIONES</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($salidas)): ?>
                            <tr>
                                <td colspan="14" class="text-center py-4">
                                    <i class="fas fa-plane-slash fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No hay salidas programadas para los filtros seleccionados</p>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($salidas as $vuelo): ?>
                            <tr class="<?php echo $vuelo['estado_vuelo'] === 'CRÍTICO' ? 'vuelo-critico' : ''; ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input flight-checkbox" data-flight-id="<?php echo $vuelo['id']; ?>" data-flight-type="salidas" onchange="updateDeleteButton('salidas')">
                                </td>
                                <td>
                                    <span class="badge <?php echo $vuelo['terminal_codigo'] === 'NAC' ? 'badge-terminal-nac' : 'badge-terminal-inter'; ?>">
                                        <?php echo htmlspecialchars($vuelo['terminal_codigo']); ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($vuelo['numero_vuelo']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($vuelo['destino']); ?></td>
                                <td><?php echo htmlspecialchars($vuelo['matricula'] ?? 'N/A'); ?></td>
                                <td><?php echo date('H:i', strtotime($vuelo['hora_programada'])); ?></td>
                                <td><?php echo date('H:i', strtotime($vuelo['hora_cierre'])); ?></td>
                                <td><?php echo $vuelo['reservas'] ?? '0'; ?></td>
                                <td class="<?php echo ($vuelo['overbooking'] < 0) ? 'overbooking-negativo' : ''; ?>">
                                    <?php echo $vuelo['overbooking'] ?? '0'; ?>
                                </td>
                                <td><?php echo htmlspecialchars($vuelo['agente_p1_nombre'] ?? 'Sin asignar'); ?></td>
                                <td><?php echo htmlspecialchars($vuelo['agente_p2_nombre'] ?? 'Sin asignar'); ?></td>
                                <td>
                                    <div class="observaciones-list">
                                        <?php
                                        $observaciones = getObservacionesDetalladas($vuelo);
                                        foreach ($observaciones as $obs):
                                        ?>
                                        <span class="obs-badge"><?php echo $obs; ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge badge-estado <?php
                                        echo match($vuelo['estado_vuelo']) {
                                            'CRÍTICO' => 'badge-critico',
                                            'CHARTER' => 'badge-charter',
                                            'SOBREVENTA' => 'badge-sobreventa',
                                            default => 'badge-normal'
                                        };
                                    ?>">
                                        <?php echo getEstadoEmoji($vuelo['estado_vuelo']) . ' ' . $vuelo['estado_vuelo']; ?>
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-edit-flight me-2" onclick="editFlight(<?php echo $vuelo['id']; ?>, 'salida')" title="Editar vuelo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-delete-flight" onclick="deleteFlight(<?php echo $vuelo['id']; ?>, '<?php echo htmlspecialchars($vuelo['numero_vuelo']); ?>')" title="Eliminar vuelo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Panel Lista de ARRIBOS -->
        <div class="card fade-in mt-4" style="animation-delay: 0.9s">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-plane-arrival me-2"></i>
                        Lista de ARRIBOS - <?php echo formatearFechaEspanol(date('Y-m-d')); ?>
                        <span class="badge bg-success ms-2"><?php echo count($arribos); ?> vuelos</span>
                    </h5>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-danger btn-sm" id="deleteSelectedArribos" onclick="deleteSelectedFlights('arribos')" style="display: none;">
                        <i class="fas fa-trash me-1"></i>
                        Eliminar Seleccionados (<span id="countSelectedArribos">0</span>)
                    </button>
                    <button class="btn-create-arrival" onclick="showCreateArrivalModal()" title="Crear Arribo">
                        <i class="fas fa-plane-arrival me-2"></i>
                        <span>Crear Arribo</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input" id="selectAllArribos" onchange="toggleAllCheckboxes('arribos', this.checked)">
                                </th>
                                <th>TERMINAL</th>
                                <th>VUELO</th>
                                <th>ORIGEN</th>
                                <th>MATRÍCULA</th>
                                <th>ETA</th>
                                <th>AGT RECEPCIÓN</th>
                                <th>OBS</th>
                                <th>SSR</th>
                                <th>ACCIONES</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($arribos)): ?>
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-plane-slash fa-2x text-muted mb-2"></i>
                                    <p class="text-muted mb-0">No hay arribos programados para los filtros seleccionados</p>
                                </td>
                            </tr>
                            <?php else: ?>
                            <?php foreach ($arribos as $vuelo): ?>
                            <tr class="<?php echo $vuelo['estado_vuelo'] === 'CRÍTICO' ? 'vuelo-critico' : ''; ?>">
                                <td>
                                    <input type="checkbox" class="form-check-input flight-checkbox" data-flight-id="<?php echo $vuelo['id']; ?>" data-flight-type="arribos" onchange="updateDeleteButton('arribos')">
                                </td>
                                <td>
                                    <span class="badge <?php echo $vuelo['terminal_codigo'] === 'NAC' ? 'badge-terminal-nac' : 'badge-terminal-inter'; ?>">
                                        <?php echo htmlspecialchars($vuelo['terminal_codigo']); ?>
                                    </span>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($vuelo['numero_vuelo']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($vuelo['origen']); ?></td>
                                <td><?php echo htmlspecialchars($vuelo['matricula'] ?? 'N/A'); ?></td>
                                <td><?php echo date('H:i', strtotime($vuelo['hora_programada'])); ?></td>
                                <td><?php echo htmlspecialchars($vuelo['agente_p1_nombre'] ?? 'Sin asignar'); ?></td>
                                <td>
                                    <div class="observaciones-list">
                                        <?php
                                        $observaciones = getObservacionesDetalladas($vuelo);
                                        foreach ($observaciones as $obs):
                                        ?>
                                        <span class="obs-badge"><?php echo $obs; ?></span>
                                        <?php endforeach; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php if ($vuelo['wchr_wchc_wchs']): ?>
                                        <span class="obs-badge">👨‍🦽WCHR/WCHS/WCHC</span>
                                    <?php elseif ($vuelo['depa_depu']): ?>
                                        <span class="obs-badge">🚩DEPU/DEPA</span>
                                    <?php elseif ($vuelo['inad']): ?>
                                        <span class="obs-badge">🎟INAD</span>
                                    <?php elseif ($vuelo['zz_observaciones']): ?>
                                        <span class="obs-badge">🔫ZZ</span>
                                    <?php else: ?>
                                        <span class="obs-badge">NADA ESP</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-edit-flight me-2" onclick="editFlight(<?php echo $vuelo['id']; ?>, 'arribo')" title="Editar vuelo">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-delete-flight" onclick="deleteFlight(<?php echo $vuelo['id']; ?>, '<?php echo htmlspecialchars($vuelo['numero_vuelo']); ?>')" title="Eliminar vuelo">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Crear Salida -->
    <div class="modal fade" id="createDepartureModal" tabindex="-1" aria-labelledby="createDepartureModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: var(--gradient-info); color: white;">
                    <h5 class="modal-title" id="createDepartureModalLabel">
                        <i class="fas fa-plane-departure me-2"></i>
                        Crear Vuelo de Salida
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createDepartureForm" method="POST" action="add_flight.php">
                        <input type="hidden" name="tipo" value="salida">
                        <div class="row g-3">
                            <!-- Terminal -->
                            <div class="col-md-6">
                                <label for="dep_terminal_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Terminal *
                                </label>
                                <select class="form-select" id="dep_terminal_id" name="terminal_id" required>
                                    <option value="">Seleccionar terminal</option>
                                    <option value="1">Nacional</option>
                                    <option value="2">Internacional</option>
                                </select>
                            </div>

                            <!-- Número de Vuelo -->
                            <div class="col-md-6">
                                <label for="dep_numero_vuelo" class="form-label">
                                    <i class="fas fa-plane me-1"></i>
                                    Vuelo (Número de Vuelo) *
                                </label>
                                <input type="text" class="form-control" id="dep_numero_vuelo" name="numero_vuelo" required
                                       placeholder="Ej: LA801, AA1234">
                            </div>

                            <!-- Destino -->
                            <div class="col-md-6">
                                <label for="dep_destino" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    Destino *
                                </label>
                                <input type="text" class="form-control" id="dep_destino" name="destino" required
                                       placeholder="Ej: Lima, Santiago, Buenos Aires">
                            </div>

                            <!-- Matrícula -->
                            <div class="col-md-6">
                                <label for="dep_matricula" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    Matrícula
                                </label>
                                <input type="text" class="form-control" id="dep_matricula" name="matricula"
                                       placeholder="Ej: CC-BGF, N123AA">
                            </div>

                            <!-- ETD -->
                            <div class="col-md-6">
                                <label for="dep_etd" class="form-label">
                                    <i class="fas fa-clock me-1"></i>
                                    ETD (Hora Estimada de Salida) *
                                </label>
                                <input type="time" class="form-control" id="dep_etd" name="hora_programada" required>
                            </div>

                            <!-- Cierre -->
                            <div class="col-md-6">
                                <label for="dep_cierre" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Cierre (50 min antes para NAC)
                                </label>
                                <input type="time" class="form-control" id="dep_cierre" name="hora_cierre" readonly>
                            </div>

                            <!-- Reservas -->
                            <div class="col-md-4">
                                <label for="dep_reservas" class="form-label">
                                    <i class="fas fa-users me-1"></i>
                                    Reservas (PAX)
                                </label>
                                <input type="number" class="form-control" id="dep_reservas" name="reservas" min="0"
                                       placeholder="150">
                            </div>

                            <!-- OVBK -->
                            <div class="col-md-4">
                                <label for="dep_ovbk" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    OVBK (Overbooking)
                                </label>
                                <input type="number" class="form-control" id="dep_ovbk" name="overbooking"
                                       placeholder="0" onchange="updateOvbkStyle(this)">
                            </div>

                            <!-- Fecha -->
                            <div class="col-md-4">
                                <label for="dep_fecha" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Fecha del Vuelo *
                                </label>
                                <input type="date" class="form-control" id="dep_fecha" name="fecha_vuelo" required
                                       value="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <!-- Agente P1 -->
                            <div class="col-md-6">
                                <label for="dep_agente_p1" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Agente P1
                                </label>
                                <select class="form-select" id="dep_agente_p1" name="agente_p1_id">
                                    <option value="">Seleccionar agente</option>
                                    <!-- Se llenarán via JavaScript -->
                                </select>
                            </div>

                            <!-- Agente P2 -->
                            <div class="col-md-6">
                                <label for="dep_agente_p2" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Agente P2
                                </label>
                                <select class="form-select" id="dep_agente_p2" name="agente_p2_id">
                                    <option value="">Seleccionar agente</option>
                                    <!-- Se llenarán via JavaScript -->
                                </select>
                            </div>

                            <!-- Observaciones -->
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-flag me-1"></i>
                                    Observaciones
                                </label>
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_sin_obs" value="sin_obs" checked>
                                            <label class="form-check-label" for="dep_sin_obs">🧡 SIN OBS</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_charter" value="charter">
                                            <label class="form-check-label" for="dep_charter">⛏ CHARTER</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_critico" value="critico">
                                            <label class="form-check-label" for="dep_critico">🚨 CRITICO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_retorno" value="retorno">
                                            <label class="form-check-label" for="dep_retorno">🛑 RETORNO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_seleccion" value="seleccion">
                                            <label class="form-check-label" for="dep_seleccion">⚽ SELECCION</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_zz" value="zz">
                                            <label class="form-check-label" for="dep_zz">🔫 ZZ</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_wchr" value="wchr">
                                            <label class="form-check-label" for="dep_wchr">👨‍🦽 WCHR/WCHS/WCHC</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_inf" value="inf">
                                            <label class="form-check-label" for="dep_inf">👶🏼 INF</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_depu" value="depu">
                                            <label class="form-check-label" for="dep_depu">👮🏼‍♂️ DEPU/DEPA</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_inad" value="inad">
                                            <label class="form-check-label" for="dep_inad">🎟 INAD</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_observacion" id="dep_otros" value="otros">
                                            <label class="form-check-label" for="dep_otros">📮 OTROS</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Estado -->
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-traffic-light me-1"></i>
                                    Estado
                                </label>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_estado" id="dep_normal" value="normal" checked>
                                            <label class="form-check-label" for="dep_normal">✅ NORMAL</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="dep_estado" id="dep_estado_critico" value="critico">
                                            <label class="form-check-label" for="dep_estado_critico">🚨 CRITICO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Observaciones adicionales -->
                            <div class="col-12">
                                <label for="dep_observaciones_adicionales" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    Observaciones Adicionales
                                </label>
                                <textarea class="form-control" id="dep_observaciones_adicionales" name="observaciones" rows="3"
                                          placeholder="Información adicional sobre el vuelo..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="submit" form="createDepartureForm" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Crear Salida
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Crear Arribo -->
    <div class="modal fade" id="createArrivalModal" tabindex="-1" aria-labelledby="createArrivalModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: var(--gradient-warning); color: white;">
                    <h5 class="modal-title" id="createArrivalModalLabel">
                        <i class="fas fa-plane-arrival me-2"></i>
                        Crear Vuelo de Arribo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="createArrivalForm" method="POST" action="add_flight.php">
                        <input type="hidden" name="tipo" value="arribo">
                        <div class="row g-3">
                            <!-- Terminal -->
                            <div class="col-md-6">
                                <label for="arr_terminal_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Terminal *
                                </label>
                                <select class="form-select" id="arr_terminal_id" name="terminal_id" required>
                                    <option value="">Seleccionar terminal</option>
                                    <option value="1">Nacional</option>
                                    <option value="2">Internacional</option>
                                </select>
                            </div>

                            <!-- Número de Vuelo -->
                            <div class="col-md-6">
                                <label for="arr_numero_vuelo" class="form-label">
                                    <i class="fas fa-plane me-1"></i>
                                    Vuelo (Número de Vuelo) *
                                </label>
                                <input type="text" class="form-control" id="arr_numero_vuelo" name="numero_vuelo" required
                                       placeholder="Ej: LA801, AA1234">
                            </div>

                            <!-- Origen -->
                            <div class="col-md-6">
                                <label for="arr_origen" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    Origen *
                                </label>
                                <input type="text" class="form-control" id="arr_origen" name="origen" required
                                       placeholder="Ej: Lima, Santiago, Buenos Aires">
                            </div>

                            <!-- Matrícula -->
                            <div class="col-md-6">
                                <label for="arr_matricula" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    Matrícula
                                </label>
                                <input type="text" class="form-control" id="arr_matricula" name="matricula"
                                       placeholder="Ej: CC-BGF, N123AA">
                            </div>

                            <!-- ETA -->
                            <div class="col-md-6">
                                <label for="arr_eta" class="form-label">
                                    <i class="fas fa-clock me-1"></i>
                                    ETA (Hora Estimada de Arribo) *
                                </label>
                                <input type="time" class="form-control" id="arr_eta" name="hora_programada" required>
                            </div>

                            <!-- Fecha -->
                            <div class="col-md-6">
                                <label for="arr_fecha" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Fecha del Vuelo *
                                </label>
                                <input type="date" class="form-control" id="arr_fecha" name="fecha_vuelo" required
                                       value="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <!-- Agente Recepción -->
                            <div class="col-md-12">
                                <label for="arr_agente_recepcion" class="form-label">
                                    <i class="fas fa-user-check me-1"></i>
                                    AGT Recepción (Agente que recibe el vuelo)
                                </label>
                                <select class="form-select" id="arr_agente_recepcion" name="agente_p1_id">
                                    <option value="">Seleccionar agente</option>
                                    <!-- Se llenarán via JavaScript -->
                                </select>
                            </div>

                            <!-- OBS (Observaciones) -->
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-flag me-1"></i>
                                    OBS (Observaciones)
                                </label>
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_sin_obs" value="sin_obs" checked>
                                            <label class="form-check-label" for="arr_sin_obs">🧡 SIN OBS</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_charter" value="charter">
                                            <label class="form-check-label" for="arr_charter">⛏ CHARTER</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_critico" value="critico">
                                            <label class="form-check-label" for="arr_critico">🚨 CRITICO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_aog" value="aog">
                                            <label class="form-check-label" for="arr_aog">✈ AOG</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_retorno" value="retorno">
                                            <label class="form-check-label" for="arr_retorno">🛑 RETORNO</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_seleccion" value="seleccion">
                                            <label class="form-check-label" for="arr_seleccion">⚽ SELECCION</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_zz" value="zz">
                                            <label class="form-check-label" for="arr_zz">🔫 ZZ</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_wchr" value="wchr">
                                            <label class="form-check-label" for="arr_wchr">👨‍🦽 WCHR/WCHS/WCHC</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_inf" value="inf">
                                            <label class="form-check-label" for="arr_inf">👶🏼 INF</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_depu" value="depu">
                                            <label class="form-check-label" for="arr_depu">🚩 DEPU/DEPA</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_inad" value="inad">
                                            <label class="form-check-label" for="arr_inad">🎟 INAD</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_observacion" id="arr_otros" value="otros">
                                            <label class="form-check-label" for="arr_otros">📮 OTROS</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- SSR -->
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-tags me-1"></i>
                                    SSR (Special Service Request)
                                </label>
                                <div class="row g-2">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_nada_esp" value="nada_esp" checked>
                                            <label class="form-check-label" for="arr_nada_esp">NADA ESP</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_zz" value="zz">
                                            <label class="form-check-label" for="arr_ssr_zz">🔫 ZZ</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_wchr" value="wchr">
                                            <label class="form-check-label" for="arr_ssr_wchr">👨‍🦽 WCHR/WCHS/WCHC</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_inf" value="inf">
                                            <label class="form-check-label" for="arr_ssr_inf">👶🏼 INF</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_depu" value="depu">
                                            <label class="form-check-label" for="arr_ssr_depu">🚩 DEPU/DEPA</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_inad" value="inad">
                                            <label class="form-check-label" for="arr_ssr_inad">🎟 INAD</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="arr_ssr" id="arr_ssr_otros" value="otros">
                                            <label class="form-check-label" for="arr_ssr_otros">📮 OTROS</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Observaciones adicionales -->
                            <div class="col-12">
                                <label for="arr_observaciones_adicionales" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    Observaciones Adicionales
                                </label>
                                <textarea class="form-control" id="arr_observaciones_adicionales" name="observaciones" rows="3"
                                          placeholder="Información adicional sobre el vuelo..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="submit" form="createArrivalForm" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Crear Arribo
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Editar Vuelo -->
    <div class="modal fade" id="editFlightModal" tabindex="-1" aria-labelledby="editFlightModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: var(--gradient-warning); color: white;">
                    <h5 class="modal-title" id="editFlightModalLabel">
                        <i class="fas fa-edit me-2"></i>
                        Editar Vuelo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editFlightForm" method="POST" action="update_flight.php">
                        <input type="hidden" id="edit_flight_id" name="flight_id">
                        <input type="hidden" id="edit_flight_tipo" name="tipo">

                        <div class="row g-3">
                            <!-- Terminal -->
                            <div class="col-md-6">
                                <label for="edit_terminal_id" class="form-label">
                                    <i class="fas fa-building me-1"></i>
                                    Terminal *
                                </label>
                                <select class="form-select" id="edit_terminal_id" name="terminal_id" required>
                                    <option value="">Seleccionar terminal</option>
                                    <option value="1">Nacional</option>
                                    <option value="2">Internacional</option>
                                </select>
                            </div>

                            <!-- Número de Vuelo -->
                            <div class="col-md-6">
                                <label for="edit_numero_vuelo" class="form-label">
                                    <i class="fas fa-plane me-1"></i>
                                    Vuelo (Número de Vuelo) *
                                </label>
                                <input type="text" class="form-control" id="edit_numero_vuelo" name="numero_vuelo" required>
                            </div>

                            <!-- Destino/Origen (dinámico según tipo) -->
                            <div class="col-md-6">
                                <label for="edit_destino_origen" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <span id="edit_destino_origen_label">Destino/Origen</span> *
                                </label>
                                <input type="text" class="form-control" id="edit_destino_origen" name="destino_origen" required>
                            </div>

                            <!-- Matrícula -->
                            <div class="col-md-6">
                                <label for="edit_matricula" class="form-label">
                                    <i class="fas fa-id-card me-1"></i>
                                    Matrícula
                                </label>
                                <input type="text" class="form-control" id="edit_matricula" name="matricula">
                            </div>

                            <!-- Hora Programada -->
                            <div class="col-md-6">
                                <label for="edit_hora_programada" class="form-label">
                                    <i class="fas fa-clock me-1"></i>
                                    <span id="edit_hora_label">Hora Programada</span> *
                                </label>
                                <input type="time" class="form-control" id="edit_hora_programada" name="hora_programada" required>
                            </div>

                            <!-- Hora de Cierre (solo para salidas) -->
                            <div class="col-md-6" id="edit_cierre_container">
                                <label for="edit_hora_cierre" class="form-label">
                                    <i class="fas fa-lock me-1"></i>
                                    Cierre
                                </label>
                                <input type="time" class="form-control" id="edit_hora_cierre" name="hora_cierre">
                            </div>

                            <!-- Fecha -->
                            <div class="col-md-6">
                                <label for="edit_fecha_vuelo" class="form-label">
                                    <i class="fas fa-calendar me-1"></i>
                                    Fecha del Vuelo *
                                </label>
                                <input type="date" class="form-control" id="edit_fecha_vuelo" name="fecha_vuelo" required>
                            </div>

                            <!-- Reservas (solo para salidas) -->
                            <div class="col-md-4" id="edit_reservas_container">
                                <label for="edit_reservas" class="form-label">
                                    <i class="fas fa-users me-1"></i>
                                    Reservas (PAX)
                                </label>
                                <input type="number" class="form-control" id="edit_reservas" name="reservas" min="0">
                            </div>

                            <!-- OVBK (solo para salidas) -->
                            <div class="col-md-4" id="edit_ovbk_container">
                                <label for="edit_overbooking" class="form-label">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    OVBK (Overbooking)
                                </label>
                                <input type="number" class="form-control" id="edit_overbooking" name="overbooking" onchange="updateOvbkStyle(this)">
                            </div>

                            <!-- Agente P1 -->
                            <div class="col-md-6">
                                <label for="edit_agente_p1" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    <span id="edit_agente_p1_label">Agente P1</span>
                                </label>
                                <select class="form-select" id="edit_agente_p1" name="agente_p1_id">
                                    <option value="">Seleccionar agente</option>
                                </select>
                            </div>

                            <!-- Agente P2 (solo para salidas) -->
                            <div class="col-md-6" id="edit_agente_p2_container">
                                <label for="edit_agente_p2" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    Agente P2
                                </label>
                                <select class="form-select" id="edit_agente_p2" name="agente_p2_id">
                                    <option value="">Seleccionar agente</option>
                                </select>
                            </div>

                            <!-- Observaciones -->
                            <div class="col-12">
                                <label class="form-label">
                                    <i class="fas fa-flag me-1"></i>
                                    Observaciones
                                </label>
                                <div class="row g-2" id="edit_observaciones_container">
                                    <!-- Se llenarán dinámicamente según el tipo -->
                                </div>
                            </div>

                            <!-- Estado (solo para salidas) -->
                            <div class="col-12" id="edit_estado_container">
                                <label class="form-label">
                                    <i class="fas fa-traffic-light me-1"></i>
                                    Estado
                                </label>
                                <div class="row g-2">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="edit_estado" id="edit_normal" value="normal">
                                            <label class="form-check-label" for="edit_normal">✅ NORMAL</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="edit_estado" id="edit_estado_critico" value="critico">
                                            <label class="form-check-label" for="edit_estado_critico">🚨 CRITICO</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Observaciones adicionales -->
                            <div class="col-12">
                                <label for="edit_observaciones_adicionales" class="form-label">
                                    <i class="fas fa-comment me-1"></i>
                                    Observaciones Adicionales
                                </label>
                                <textarea class="form-control" id="edit_observaciones_adicionales" name="observaciones" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancelar
                    </button>
                    <button type="submit" form="editFlightForm" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>
                        Guardar Cambios
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Función para alternar sidebar en móvil
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');

            // Agregar efecto de blur al contenido principal cuando el sidebar está abierto en móvil
            const mainContent = document.querySelector('.main-content');
            if (window.innerWidth <= 768) {
                if (sidebar.classList.contains('show')) {
                    mainContent.style.filter = 'blur(3px)';
                } else {
                    mainContent.style.filter = 'none';
                }
            }
        }

        // Función para alternar tema
        function toggleTheme() {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            applyTheme(currentTheme);
            localStorage.setItem('theme', currentTheme);
        }

        // Función para aplicar tema
        function applyTheme(theme) {
            const root = document.documentElement;
            const themeIcon = document.querySelector('.theme-icon');
            const body = document.body;

            // Remover clases de tema anteriores
            body.classList.remove('theme-light', 'theme-dark');

            if (theme === 'dark') {
                // Tema Oscuro
                body.classList.add('theme-dark');
                root.style.setProperty('--primary-color', '#667eea');
                root.style.setProperty('--secondary-color', '#764ba2');
                root.style.setProperty('--bg-primary', '#1a202c');
                root.style.setProperty('--bg-secondary', '#2d3748');
                root.style.setProperty('--bg-tertiary', '#4a5568');
                root.style.setProperty('--text-primary', '#f7fafc');
                root.style.setProperty('--text-secondary', '#e2e8f0');
                root.style.setProperty('--text-muted', '#a0aec0');
                root.style.setProperty('--border-color', '#4a5568');
                root.style.setProperty('--card-bg', 'rgba(45, 55, 72, 0.9)');
                root.style.setProperty('--sidebar-bg', 'rgba(26, 32, 44, 0.95)');

                document.body.style.setProperty('background', 'linear-gradient(135deg, #1a202c 0%, #2d3748 100%)', 'important');
                document.body.style.setProperty('background-attachment', 'fixed', 'important');

                // Actualizar icono del tema
                if (themeIcon) {
                    themeIcon.className = 'fas fa-sun theme-icon';
                    themeIcon.style.color = '#ffd700';
                }
            } else {
                // Tema Claro
                body.classList.add('theme-light');
                root.style.setProperty('--primary-color', '#667eea');
                root.style.setProperty('--secondary-color', '#764ba2');
                root.style.setProperty('--bg-primary', '#f8f9fa');
                root.style.setProperty('--bg-secondary', '#ffffff');
                root.style.setProperty('--bg-tertiary', '#e9ecef');
                root.style.setProperty('--text-primary', '#2d3748');
                root.style.setProperty('--text-secondary', '#4a5568');
                root.style.setProperty('--text-muted', '#718096');
                root.style.setProperty('--border-color', '#e2e8f0');
                root.style.setProperty('--card-bg', 'rgba(255, 255, 255, 0.9)');
                root.style.setProperty('--sidebar-bg', 'rgba(255, 255, 255, 0.95)');

                document.body.style.setProperty('background', 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)', 'important');
                document.body.style.setProperty('background-attachment', 'fixed', 'important');

                // Actualizar icono del tema
                if (themeIcon) {
                    themeIcon.className = 'fas fa-moon theme-icon';
                    themeIcon.style.color = '#4a5568';
                }
            }
        }

        // Función para exportar a Excel
        function exportarExcel() {
            // Crear tabla temporal con los datos visibles
            const table = document.querySelector('.table');
            if (!table) {
                alert('No hay datos para exportar');
                return;
            }

            // Crear un nuevo libro de trabajo
            const wb = XLSX.utils.table_to_book(table, {sheet: "Vuelos"});

            // Generar nombre de archivo con fecha
            const fecha = new Date().toISOString().split('T')[0];
            const filename = `vuelos_${fecha}.xlsx`;

            // Descargar archivo
            XLSX.writeFile(wb, filename);
        }

        // ===== FUNCIONES PARA CREAR VUELOS =====

        // Función para mostrar modal de crear salida
        function showCreateDepartureModal() {
            const modal = new bootstrap.Modal(document.getElementById('createDepartureModal'));
            loadAgents('dep_agente_p1');
            loadAgents('dep_agente_p2');
            modal.show();
        }

        // Función para mostrar modal de crear arribo
        function showCreateArrivalModal() {
            const modal = new bootstrap.Modal(document.getElementById('createArrivalModal'));
            loadAgents('arr_agente_recepcion');
            modal.show();
        }

        // Función para cargar agentes en los selects
        function loadAgents(selectId) {
            fetch('../ajax/get_agents.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const select = document.getElementById(selectId);
                        if (select) {
                            // Limpiar opciones existentes excepto la primera
                            select.innerHTML = '<option value="">Seleccionar agente</option>';

                            // Agregar agentes
                            data.agents.forEach(agent => {
                                const option = document.createElement('option');
                                option.value = agent.id;
                                option.textContent = agent.nombre;
                                select.appendChild(option);
                            });
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading agents:', error);
                    // Agregar opciones de ejemplo si falla la carga
                    const select = document.getElementById(selectId);
                    if (select) {
                        select.innerHTML = `
                            <option value="">Seleccionar agente</option>
                            <option value="1">Agente Ejemplo 1</option>
                            <option value="2">Agente Ejemplo 2</option>
                        `;
                    }
                });
        }

        // Función para calcular hora de cierre automáticamente (50 min para NAC)
        function calculateDepartureCloseTime() {
            const etdInput = document.getElementById('dep_etd');
            const cierreInput = document.getElementById('dep_cierre');
            const terminalSelect = document.getElementById('dep_terminal_id');

            if (etdInput.value && terminalSelect.value) {
                const etd = new Date('2000-01-01 ' + etdInput.value);
                let minutesToSubtract = 50; // Por defecto 50 min para NAC

                if (terminalSelect.value === '2') { // Internacional
                    minutesToSubtract = 45; // 45 min para Internacional
                }

                const closeTime = new Date(etd.getTime() - (minutesToSubtract * 60 * 1000));
                const hours = closeTime.getHours().toString().padStart(2, '0');
                const minutes = closeTime.getMinutes().toString().padStart(2, '0');

                cierreInput.value = `${hours}:${minutes}`;
            }
        }

        // Función para actualizar estilo del OVBK según el valor
        function updateOvbkStyle(input) {
            const value = parseInt(input.value) || 0;

            if (value < 0) {
                input.style.fontWeight = 'bold';
                input.style.color = '#dc3545'; // Rojo
                input.style.backgroundColor = '#f8d7da';
            } else {
                input.style.fontWeight = 'normal';
                input.style.color = '';
                input.style.backgroundColor = '';
            }
        }

        // Función para enviar formulario de salida
        function submitDepartureForm(event) {
            event.preventDefault();

            const form = document.getElementById('createDepartureForm');
            const formData = new FormData(form);

            // Procesar observaciones y estado
            const observacion = document.querySelector('input[name="dep_observacion"]:checked')?.value;
            const estado = document.querySelector('input[name="dep_estado"]:checked')?.value;

            // Inicializar todos los campos como false
            formData.set('critico', '0');
            formData.set('charter', '0');
            formData.set('sobreventa', '0');
            formData.set('wchr_wchc_wchs', '0');
            formData.set('depa_depu', '0');
            formData.set('inad', '0');
            formData.set('zz_observaciones', '0');

            // Mapear observaciones a campos de base de datos
            if (observacion) {
                switch(observacion) {
                    case 'charter':
                        formData.set('charter', '1');
                        break;
                    case 'critico':
                        formData.set('critico', '1');
                        break;
                    case 'wchr':
                        formData.set('wchr_wchc_wchs', '1');
                        break;
                    case 'depu':
                        formData.set('depa_depu', '1');
                        break;
                    case 'inad':
                        formData.set('inad', '1');
                        break;
                    case 'zz':
                        formData.set('zz_observaciones', '1');
                        break;
                    // sin_obs y otros no marcan ningún flag especial
                }
            }

            // Solo marcar como crítico si el ESTADO es crítico (no la observación)
            if (estado === 'critico') {
                formData.set('critico', '1');
            }

            // Mostrar loading
            const submitBtn = document.querySelector('#createDepartureModal button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creando...';
            submitBtn.disabled = true;

            // Enviar datos (usando versión simplificada)
            fetch('add_flight_simple.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                console.log('Parsed data:', data);
                if (data.success) {
                    showAlert(`✅ Vuelo de salida ${data.numero_vuelo} creado exitosamente!`, 'success');

                    // Cerrar modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createDepartureModal'));
                    modal.hide();

                    // Limpiar formulario
                    form.reset();

                    // Recargar página
                    setTimeout(() => location.reload(), 1500);
                } else {
                    let errorMessage = data.message;
                    if (data.errors && data.errors.length > 0) {
                        errorMessage += ':\n• ' + data.errors.join('\n• ');
                    }
                    showAlert(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error de conexión. Por favor, intente nuevamente.', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        // Función para enviar formulario de arribo
        function submitArrivalForm(event) {
            event.preventDefault();

            const form = document.getElementById('createArrivalForm');
            const formData = new FormData(form);

            // Procesar observaciones y SSR
            const observacion = document.querySelector('input[name="arr_observacion"]:checked')?.value;
            const ssr = document.querySelector('input[name="arr_ssr"]:checked')?.value;

            // Inicializar todos los campos como false
            formData.set('critico', '0');
            formData.set('charter', '0');
            formData.set('sobreventa', '0');
            formData.set('wchr_wchc_wchs', '0');
            formData.set('depa_depu', '0');
            formData.set('inad', '0');
            formData.set('zz_observaciones', '0');

            // Mapear observaciones a campos de base de datos
            if (observacion) {
                switch(observacion) {
                    case 'charter':
                        formData.set('charter', '1');
                        break;
                    case 'critico':
                        formData.set('critico', '1');
                        break;
                    case 'wchr':
                        formData.set('wchr_wchc_wchs', '1');
                        break;
                    case 'depu':
                        formData.set('depa_depu', '1');
                        break;
                    case 'inad':
                        formData.set('inad', '1');
                        break;
                    case 'zz':
                        formData.set('zz_observaciones', '1');
                        break;
                    // sin_obs, aog y otros no marcan ningún flag especial
                }
            }

            // Procesar SSR adicional
            if (ssr && ssr !== 'nada_esp') {
                // Agregar información de SSR a observaciones
                const currentObs = formData.get('observaciones') || '';
                formData.set('observaciones', currentObs + (currentObs ? '\n' : '') + `SSR: ${ssr.toUpperCase()}`);
            }

            // Mostrar loading
            const submitBtn = document.querySelector('#createArrivalModal button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creando...';
            submitBtn.disabled = true;

            // Enviar datos (usando versión simplificada)
            fetch('add_flight_simple.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.text().then(text => {
                    console.log('Raw response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                console.log('Parsed data:', data);
                if (data.success) {
                    showAlert(`✅ Vuelo de arribo ${data.numero_vuelo} creado exitosamente!`, 'success');

                    // Cerrar modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createArrivalModal'));
                    modal.hide();

                    // Limpiar formulario
                    form.reset();

                    // Recargar página
                    setTimeout(() => location.reload(), 1500);
                } else {
                    let errorMessage = data.message;
                    if (data.errors && data.errors.length > 0) {
                        errorMessage += ':\n• ' + data.errors.join('\n• ');
                    }
                    showAlert(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error de conexión. Por favor, intente nuevamente.', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        // Función para mostrar alertas estilizadas
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
                max-width: 500px;
                box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
                border-radius: 10px;
            `;

            const icon = type === 'error' ? 'fa-exclamation-circle' :
                        type === 'success' ? 'fa-check-circle' : 'fa-info-circle';

            alertDiv.innerHTML = `
                <i class="fas ${icon} me-2"></i>
                ${message.replace(/\n/g, '<br>')}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 5000);
        }

        // ===== FUNCIONES PARA EDITAR VUELOS =====

        // Función para abrir modal de edición
        function editFlight(flightId, tipo) {
            console.log('Editando vuelo:', flightId, tipo);

            // Cargar datos del vuelo
            fetch(`get_flight_simple.php?id=${flightId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditModal(data.flight, tipo);
                        const modal = new bootstrap.Modal(document.getElementById('editFlightModal'));
                        modal.show();
                    } else {
                        showAlert('Error al cargar datos del vuelo: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error de conexión al cargar el vuelo', 'error');
                });
        }

        // Función para poblar el modal de edición
        function populateEditModal(flight, tipo) {
            // Datos básicos
            document.getElementById('edit_flight_id').value = flight.id;
            document.getElementById('edit_flight_tipo').value = tipo;
            document.getElementById('edit_numero_vuelo').value = flight.numero_vuelo || '';
            document.getElementById('edit_terminal_id').value = flight.terminal_id || '';
            document.getElementById('edit_matricula').value = flight.matricula || '';
            document.getElementById('edit_hora_programada').value = flight.hora_programada || '';
            document.getElementById('edit_fecha_vuelo').value = flight.fecha_vuelo || '';
            document.getElementById('edit_observaciones_adicionales').value = flight.observaciones || '';

            // Configurar campos según tipo
            if (tipo === 'salida') {
                // Configurar para salida
                document.getElementById('edit_destino_origen_label').textContent = 'Destino';
                document.getElementById('edit_destino_origen').name = 'destino';
                document.getElementById('edit_destino_origen').value = flight.destino || '';
                document.getElementById('edit_hora_label').textContent = 'ETD (Hora Estimada de Salida)';
                document.getElementById('edit_agente_p1_label').textContent = 'Agente P1';

                // Mostrar campos específicos de salida
                document.getElementById('edit_cierre_container').style.display = 'block';
                document.getElementById('edit_reservas_container').style.display = 'block';
                document.getElementById('edit_ovbk_container').style.display = 'block';
                document.getElementById('edit_agente_p2_container').style.display = 'block';
                document.getElementById('edit_estado_container').style.display = 'block';

                // Llenar campos específicos
                document.getElementById('edit_hora_cierre').value = flight.hora_cierre || '';
                document.getElementById('edit_reservas').value = flight.reservas || '';
                document.getElementById('edit_overbooking').value = flight.overbooking || '';

                // Configurar observaciones para salida
                setupEditObservations('salida');

            } else {
                // Configurar para arribo
                document.getElementById('edit_destino_origen_label').textContent = 'Origen';
                document.getElementById('edit_destino_origen').name = 'origen';
                document.getElementById('edit_destino_origen').value = flight.origen || '';
                document.getElementById('edit_hora_label').textContent = 'ETA (Hora Estimada de Arribo)';
                document.getElementById('edit_agente_p1_label').textContent = 'AGT Recepción';

                // Ocultar campos específicos de salida
                document.getElementById('edit_cierre_container').style.display = 'none';
                document.getElementById('edit_reservas_container').style.display = 'none';
                document.getElementById('edit_ovbk_container').style.display = 'none';
                document.getElementById('edit_agente_p2_container').style.display = 'none';
                document.getElementById('edit_estado_container').style.display = 'none';

                // Configurar observaciones para arribo
                setupEditObservations('arribo');
            }

            // Cargar agentes
            loadAgents('edit_agente_p1');
            if (tipo === 'salida') {
                loadAgents('edit_agente_p2');
            }

            // Seleccionar agentes actuales
            setTimeout(() => {
                if (flight.agente_p1_id) {
                    document.getElementById('edit_agente_p1').value = flight.agente_p1_id;
                }
                if (flight.agente_p2_id && tipo === 'salida') {
                    document.getElementById('edit_agente_p2').value = flight.agente_p2_id;
                }
            }, 500);

            // Marcar observaciones actuales
            markCurrentObservations(flight);
        }

        // Función para configurar observaciones según tipo
        function setupEditObservations(tipo) {
            const container = document.getElementById('edit_observaciones_container');

            if (tipo === 'salida') {
                container.innerHTML = `
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_sin_obs" value="sin_obs" checked>
                            <label class="form-check-label" for="edit_sin_obs">🧡 SIN OBS</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_charter" value="charter">
                            <label class="form-check-label" for="edit_charter">⛏ CHARTER</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_critico" value="critico">
                            <label class="form-check-label" for="edit_critico">🚨 CRITICO</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_retorno" value="retorno">
                            <label class="form-check-label" for="edit_retorno">🛑 RETORNO</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_seleccion" value="seleccion">
                            <label class="form-check-label" for="edit_seleccion">⚽ SELECCION</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_zz" value="zz">
                            <label class="form-check-label" for="edit_zz">🔫 ZZ</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_wchr" value="wchr">
                            <label class="form-check-label" for="edit_wchr">👨‍🦽 WCHR/WCHS/WCHC</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_inf" value="inf">
                            <label class="form-check-label" for="edit_inf">👶🏼 INF</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_depu" value="depu">
                            <label class="form-check-label" for="edit_depu">👮🏼‍♂️ DEPU/DEPA</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_inad" value="inad">
                            <label class="form-check-label" for="edit_inad">🎟 INAD</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_otros" value="otros">
                            <label class="form-check-label" for="edit_otros">📮 OTROS</label>
                        </div>
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_sin_obs" value="sin_obs" checked>
                            <label class="form-check-label" for="edit_sin_obs">🧡 SIN OBS</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_charter" value="charter">
                            <label class="form-check-label" for="edit_charter">⛏ CHARTER</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_critico" value="critico">
                            <label class="form-check-label" for="edit_critico">🚨 CRITICO</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_aog" value="aog">
                            <label class="form-check-label" for="edit_aog">✈ AOG</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_retorno" value="retorno">
                            <label class="form-check-label" for="edit_retorno">🛑 RETORNO</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_seleccion" value="seleccion">
                            <label class="form-check-label" for="edit_seleccion">⚽ SELECCION</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_zz" value="zz">
                            <label class="form-check-label" for="edit_zz">🔫 ZZ</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_wchr" value="wchr">
                            <label class="form-check-label" for="edit_wchr">👨‍🦽 WCHR/WCHS/WCHC</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_inf" value="inf">
                            <label class="form-check-label" for="edit_inf">👶🏼 INF</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_depu" value="depu">
                            <label class="form-check-label" for="edit_depu">🚩 DEPU/DEPA</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_inad" value="inad">
                            <label class="form-check-label" for="edit_inad">🎟 INAD</label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="edit_observacion" id="edit_otros" value="otros">
                            <label class="form-check-label" for="edit_otros">📮 OTROS</label>
                        </div>
                    </div>
                `;
            }
        }

        // Función para marcar observaciones actuales
        function markCurrentObservations(flight) {
            // Determinar qué observación está activa
            let activeObservation = 'sin_obs'; // Por defecto

            if (flight.critico == 1) activeObservation = 'critico';
            else if (flight.charter == 1) activeObservation = 'charter';
            else if (flight.wchr_wchc_wchs == 1) activeObservation = 'wchr';
            else if (flight.depa_depu == 1) activeObservation = 'depu';
            else if (flight.inad == 1) activeObservation = 'inad';
            else if (flight.zz_observaciones == 1) activeObservation = 'zz';

            // Marcar la observación correspondiente
            setTimeout(() => {
                const radioButton = document.querySelector(`input[name="edit_observacion"][value="${activeObservation}"]`);
                if (radioButton) {
                    radioButton.checked = true;
                }

                // Para salidas, también marcar el estado
                if (document.getElementById('edit_flight_tipo').value === 'salida') {
                    const estadoRadio = flight.critico == 1 ? 'critico' : 'normal';
                    const estadoButton = document.querySelector(`input[name="edit_estado"][value="${estadoRadio}"]`);
                    if (estadoButton) {
                        estadoButton.checked = true;
                    }
                }
            }, 100);
        }

        // Función para enviar formulario de edición
        function submitEditFlightForm(event) {
            event.preventDefault();

            const form = document.getElementById('editFlightForm');
            const formData = new FormData(form);
            const tipo = document.getElementById('edit_flight_tipo').value;

            // Inicializar todos los campos como false
            formData.set('critico', '0');
            formData.set('charter', '0');
            formData.set('sobreventa', '0');
            formData.set('wchr_wchc_wchs', '0');
            formData.set('depa_depu', '0');
            formData.set('inad', '0');
            formData.set('zz_observaciones', '0');

            // Procesar observaciones
            const observacion = document.querySelector('input[name="edit_observacion"]:checked')?.value;
            if (observacion) {
                switch(observacion) {
                    case 'charter':
                        formData.set('charter', '1');
                        break;
                    case 'critico':
                        formData.set('critico', '1');
                        break;
                    case 'wchr':
                        formData.set('wchr_wchc_wchs', '1');
                        break;
                    case 'depu':
                        formData.set('depa_depu', '1');
                        break;
                    case 'inad':
                        formData.set('inad', '1');
                        break;
                    case 'zz':
                        formData.set('zz_observaciones', '1');
                        break;
                }
            }

            // Para salidas, procesar estado
            if (tipo === 'salida') {
                const estado = document.querySelector('input[name="edit_estado"]:checked')?.value;
                if (estado === 'critico') {
                    formData.set('critico', '1');
                }
            }

            // Mostrar loading
            const submitBtn = document.querySelector('#editFlightModal button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Guardando...';
            submitBtn.disabled = true;

            // Enviar datos
            fetch('update_flight_real.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    showAlert(`✅ Vuelo ${data.numero_vuelo} actualizado exitosamente!`, 'success');

                    // Cerrar modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editFlightModal'));
                    modal.hide();

                    // Recargar página
                    setTimeout(() => location.reload(), 1500);
                } else {
                    let errorMessage = data.message;
                    if (data.errors && data.errors.length > 0) {
                        errorMessage += ':\n• ' + data.errors.join('\n• ');
                    }
                    showAlert(errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Error de conexión. Por favor, intente nuevamente.', 'error');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        // ===== FUNCIONES PARA SELECCIÓN MÚLTIPLE =====

        // Función para alternar todos los checkboxes
        function toggleAllCheckboxes(type, checked) {
            const checkboxes = document.querySelectorAll(`input[data-flight-type="${type}"]`);
            checkboxes.forEach(checkbox => {
                checkbox.checked = checked;
            });
            updateDeleteButton(type);
        }

        // Función para actualizar el botón de eliminar
        function updateDeleteButton(type) {
            const checkboxes = document.querySelectorAll(`input[data-flight-type="${type}"]:checked`);
            const deleteButton = document.getElementById(`deleteSelected${type.charAt(0).toUpperCase() + type.slice(1)}`);
            const countSpan = document.getElementById(`countSelected${type.charAt(0).toUpperCase() + type.slice(1)}`);
            const selectAllCheckbox = document.getElementById(`selectAll${type.charAt(0).toUpperCase() + type.slice(1)}`);

            const selectedCount = checkboxes.length;

            if (selectedCount > 0) {
                deleteButton.style.display = 'block';
                countSpan.textContent = selectedCount;
            } else {
                deleteButton.style.display = 'none';
            }

            // Actualizar estado del checkbox "Seleccionar todo"
            const allCheckboxes = document.querySelectorAll(`input[data-flight-type="${type}"]`);
            if (selectedCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (selectedCount === allCheckboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
            }
        }

        // Función para eliminar vuelos seleccionados
        function deleteSelectedFlights(type) {
            const checkboxes = document.querySelectorAll(`input[data-flight-type="${type}"]:checked`);
            const selectedIds = Array.from(checkboxes).map(cb => cb.getAttribute('data-flight-id'));

            if (selectedIds.length === 0) {
                showAlert('No hay vuelos seleccionados', 'warning');
                return;
            }

            const flightType = type === 'salidas' ? 'salidas' : 'arribos';
            const confirmMessage = `¿Estás seguro de que deseas eliminar ${selectedIds.length} ${flightType} seleccionados?\n\nEsta acción no se puede deshacer.`;

            if (confirm(confirmMessage)) {
                const deleteButton = document.getElementById(`deleteSelected${type.charAt(0).toUpperCase() + type.slice(1)}`);
                const originalText = deleteButton.innerHTML;
                deleteButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Eliminando...';
                deleteButton.disabled = true;

                // Eliminar vuelos uno por uno
                deleteFlightsSequentially(selectedIds, 0, type, deleteButton, originalText);
            }
        }

        // Función para eliminar vuelos secuencialmente
        function deleteFlightsSequentially(flightIds, index, type, deleteButton, originalText) {
            if (index >= flightIds.length) {
                // Todos los vuelos han sido procesados
                showAlert(`✅ ${index} vuelos eliminados exitosamente!`, 'success');
                setTimeout(() => location.reload(), 1500);
                return;
            }

            const flightId = flightIds[index];

            fetch('delete_flight_improved.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `flight_id=${flightId}`
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text();
            })
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    if (data.success) {
                        // Continuar con el siguiente vuelo
                        deleteFlightsSequentially(flightIds, index + 1, type, deleteButton, originalText);
                    } else {
                        showAlert(`❌ Error eliminando vuelo ID ${flightId}: ${data.message}`, 'error');
                        console.error('Delete error details:', data.debug || 'No debug info');
                        deleteButton.innerHTML = originalText;
                        deleteButton.disabled = false;
                    }
                } catch (e) {
                    showAlert(`❌ Error de respuesta eliminando vuelo ID ${flightId}: Respuesta no válida`, 'error');
                    console.error('JSON parse error:', e, 'Response:', text);
                    deleteButton.innerHTML = originalText;
                    deleteButton.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert(`❌ Error de conexión eliminando vuelo ID ${flightId}: ${error.message}`, 'error');
                deleteButton.innerHTML = originalText;
                deleteButton.disabled = false;
            });
        }

        // ===== FUNCIONES PARA ELIMINAR VUELOS =====

        // Función para eliminar vuelo
        function deleteFlight(flightId, flightNumber) {
            // Mostrar confirmación
            if (confirm(`¿Estás seguro de que deseas eliminar el vuelo ${flightNumber}?\n\nEsta acción no se puede deshacer.`)) {

                // Mostrar loading
                const deleteButtons = document.querySelectorAll(`button[onclick*="deleteFlight(${flightId}"]`);
                deleteButtons.forEach(btn => {
                    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    btn.disabled = true;
                });

                // Enviar solicitud de eliminación
                fetch('delete_flight_improved.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `flight_id=${flightId}`
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text().then(text => {
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            throw new Error('Invalid JSON response: ' + text.substring(0, 100));
                        }
                    });
                })
                .then(data => {
                    if (data.success) {
                        showAlert(`✅ Vuelo ${flightNumber} eliminado exitosamente!`, 'success');

                        // Recargar página después de un breve delay
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showAlert(`❌ Error al eliminar vuelo: ${data.message}`, 'error');

                        // Restaurar botones
                        deleteButtons.forEach(btn => {
                            btn.innerHTML = '<i class="fas fa-trash"></i>';
                            btn.disabled = false;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('❌ Error de conexión. Por favor, intente nuevamente.', 'error');

                    // Restaurar botones
                    deleteButtons.forEach(btn => {
                        btn.innerHTML = '<i class="fas fa-trash"></i>';
                        btn.disabled = false;
                    });
                });
            }
        }

        // ===== FUNCIONES DE NOTIFICACIONES =====

        function initializeNotifications() {
            // Configurar filtros
            setupNotificationFilters();

            // Configurar clicks en notificaciones
            setupNotificationClicks();

            // Solicitar permisos de notificación
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }
        }

        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');
            const bell = document.querySelector('.notification-bell-icon');

            // Animar campana
            if (bell) {
                bell.classList.add('ringing');
                setTimeout(() => bell.classList.remove('ringing'), 600);
            }

            // Reproducir sonido
            playNotificationSound();

            // Toggle panel y overlay
            const isShowing = panel.classList.contains('show');

            if (isShowing) {
                hideNotifications();
            } else {
                showNotifications();
            }
        }

        function showNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            if (panel && overlay) {
                overlay.classList.add('show');
                setTimeout(() => {
                    panel.classList.add('show');
                }, 50);

                // Cargar notificaciones
                loadNotifications();
            }
        }

        function hideNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            if (panel && overlay) {
                panel.classList.remove('show');
                setTimeout(() => {
                    overlay.classList.remove('show');
                }, 300);
            }
        }

        function loadNotifications() {
            fetch('../ajax/get_notifications.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationsList(data.notifications);
                        updateNotificationCount(data.unread_count);
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                });
        }

        function updateNotificationsList(notifications) {
            const container = document.getElementById('notificationsList');
            if (!container) return;

            if (notifications.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-bell-slash fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-0">No hay notificaciones</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = notifications.map(notification => `
                <div class="notification-item ${notification.leida ? '' : 'unread'}" data-id="${notification.id}">
                    <div class="notification-icon ${notification.tipo}">
                        <i class="fas ${getNotificationIcon(notification.tipo)}"></i>
                    </div>
                    <div class="notification-content">
                        <h6 class="notification-title">${notification.titulo}</h6>
                        <p class="notification-message">${notification.mensaje}</p>
                        <small class="notification-time">${timeAgo(notification.created_at)}</small>
                    </div>
                    ${!notification.leida ? '<div class="notification-dot"></div>' : ''}
                </div>
            `).join('');
        }

        function updateNotificationCount(count) {
            const counter = document.querySelector('.notification-count');
            if (counter) {
                counter.textContent = count;
                counter.style.display = count > 0 ? 'flex' : 'none';
            }
        }

        function getNotificationIcon(tipo) {
            const icons = {
                'operacional': 'fa-plane',
                'social': 'fa-users',
                'sistema': 'fa-cog',
                'urgente': 'fa-exclamation-triangle'
            };
            return icons[tipo] || 'fa-bell';
        }

        function setupNotificationFilters() {
            // Implementar filtros de notificaciones
        }

        function setupNotificationClicks() {
            // Implementar clicks en notificaciones
        }

        function playNotificationSound() {
            // Reproducir sonido de notificación
            try {
                const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT');
                audio.play().catch(() => {});
            } catch (e) {}
        }

        function checkNewNotifications() {
            fetch('../ajax/get_notifications.php?unread_only=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateNotificationCount(data.unread_count);
                    }
                })
                .catch(error => {
                    console.error('Error checking notifications:', error);
                });
        }

        function timeAgo(datetime) {
            const time = new Date().getTime() - new Date(datetime).getTime();
            const seconds = Math.floor(time / 1000);

            if (seconds < 60) return 'hace ' + seconds + ' seg';
            if (seconds < 3600) return 'hace ' + Math.floor(seconds/60) + ' min';
            if (seconds < 86400) return 'hace ' + Math.floor(seconds/3600) + ' h';
            if (seconds < 2592000) return 'hace ' + Math.floor(seconds/86400) + ' días';
            if (seconds < 31536000) return 'hace ' + Math.floor(seconds/2592000) + ' meses';
            return 'hace ' + Math.floor(seconds/31536000) + ' años';
        }

        // Inicializar cuando se carga la página
        document.addEventListener('DOMContentLoaded', function() {
            // Aplicar tema guardado
            applyTheme(currentTheme);

            // Inicializar notificaciones
            initializeNotifications();

            // Event listeners para formularios de vuelos
            const departureForm = document.getElementById('createDepartureForm');
            const arrivalForm = document.getElementById('createArrivalForm');
            const editForm = document.getElementById('editFlightForm');
            const depEtdInput = document.getElementById('dep_etd');
            const depTerminalSelect = document.getElementById('dep_terminal_id');

            if (departureForm) {
                departureForm.addEventListener('submit', submitDepartureForm);
            }

            if (arrivalForm) {
                arrivalForm.addEventListener('submit', submitArrivalForm);
            }

            if (editForm) {
                editForm.addEventListener('submit', submitEditFlightForm);
            }

            if (depEtdInput) {
                depEtdInput.addEventListener('change', calculateDepartureCloseTime);
            }

            if (depTerminalSelect) {
                depTerminalSelect.addEventListener('change', calculateDepartureCloseTime);
            }

            // Verificar notificaciones cada 30 segundos
            setInterval(checkNewNotifications, 30000);

            // Agregar efectos de hover a las tarjetas
            const cards = document.querySelectorAll('.stat-card, .card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // Auto-refresh cada 30 segundos para vuelos críticos
            setInterval(function() {
                // Solo refrescar si hay vuelos críticos
                const vuelosCriticos = document.querySelectorAll('.vuelo-critico');
                if (vuelosCriticos.length > 0) {
                    // Aquí podrías implementar una actualización AJAX
                    console.log('Checking for flight updates...');
                }
            }, 30000);

            // Resaltar vuelos críticos con animación
            const vuelosCriticos = document.querySelectorAll('.vuelo-critico');
            vuelosCriticos.forEach(vuelo => {
                vuelo.style.animation = 'pulse 2s infinite';
            });
        });

        // Cerrar sidebar al hacer clic fuera en móvil
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggleBtn = document.querySelector('[onclick="toggleSidebar()"]');

            if (window.innerWidth <= 768 &&
                sidebar.classList.contains('show') &&
                !sidebar.contains(event.target) &&
                !toggleBtn.contains(event.target)) {
                toggleSidebar();
            }
        });

        // Agregar animación de pulso para vuelos críticos
        const additionalStyles = `
            @keyframes pulse {
                0%, 100% {
                    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
                }
                50% {
                    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
                }
            }
        `;

        // Agregar estilos al documento
        const styleSheet = document.createElement('style');
        styleSheet.textContent = additionalStyles;
        document.head.appendChild(styleSheet);
    </script>

    <!-- Script para exportar Excel (opcional - requiere SheetJS) -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</body>
</html>
