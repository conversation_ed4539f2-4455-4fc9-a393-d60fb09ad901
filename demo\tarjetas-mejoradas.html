<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tarjetas Mejoradas - Informativos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            padding: 40px 20px;
        }
        
        .demo-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .demo-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        
        .view-toggle-group {
            background: #f1f5f9;
            border-radius: 16px;
            padding: 4px;
            display: inline-flex;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .view-toggle-btn {
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #64748b;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }
        
        .view-toggle-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }
        
        /* Vista Grid */
        .informativos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            padding: 20px 0;
        }
        
        .informativos-grid .informativo-card {
            display: flex;
            flex-direction: column;
            height: auto;
            min-height: 400px;
        }
        
        /* Vista Lista */
        .informativos-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            padding: 20px 0;
        }
        
        .informativos-list .informativo-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            min-height: auto;
            height: auto;
            padding: 20px;
        }
        
        .informativos-list .card-content {
            flex: 1;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 20px;
        }
        
        .informativos-list .file-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
            margin: 0;
            flex-shrink: 0;
        }
        
        /* Tarjetas base */
        .informativo-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }
        
        .informativo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .card-content {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .file-icon {
            width: 80px;
            height: 80px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin: 20px auto;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        
        .file-pdf { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; }
        .file-doc { background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%); color: white; }
        .file-img { background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; }
        
        .informativo-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.3;
            margin-bottom: 8px;
        }
        
        .tipo-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        .tipo-briefing { background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%); color: #1d4ed8; }
        .tipo-boletin { background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%); color: #166534; }
        
        .card-footer-actions {
            background: #f8fafc;
            padding: 20px 25px;
            border-top: 1px solid #f1f5f9;
        }
        
        .btn-view-document {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .secondary-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 10px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }
        
        @media (max-width: 768px) {
            .informativos-grid {
                grid-template-columns: 1fr;
            }
            
            .informativos-list .informativo-card {
                flex-direction: column;
            }
            
            .informativos-list .card-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- Header -->
        <div class="demo-header">
            <h1 class="display-4 mb-4">
                <i class="fas fa-id-card me-3" style="color: #667eea;"></i>
                Tarjetas de Informativos Mejoradas
            </h1>
            <p class="lead">Diseño completamente renovado con vistas Grid y Lista optimizadas</p>
            
            <!-- Toggle de vista -->
            <div class="view-toggle-group">
                <button class="view-toggle-btn active" onclick="toggleView('grid')">
                    <i class="fas fa-th-large"></i>
                    <span>Cuadrícula</span>
                </button>
                <button class="view-toggle-btn" onclick="toggleView('list')">
                    <i class="fas fa-list"></i>
                    <span>Lista</span>
                </button>
            </div>
        </div>

        <!-- Contenedor de tarjetas -->
        <div id="informativos-container" class="informativos-grid">
            <!-- Tarjeta 1: PDF -->
            <div class="informativo-card">
                <div class="card-content">
                    <div class="informativo-header">
                        <div class="flex-grow-1">
                            <div class="informativo-title">Briefing Operacional Enero 2024</div>
                            <div class="informativo-meta">
                                <span><i class="fas fa-calendar me-1"></i> 15/01/2024</span>
                                <span><i class="fas fa-user me-1"></i> Juan Supervisor</span>
                            </div>
                        </div>
                        <span class="tipo-badge tipo-briefing">📋 Briefing</span>
                    </div>
                    
                    <div class="text-center">
                        <div class="file-icon file-pdf">
                            <i class="fas fa-file-pdf"></i>
                        </div>
                    </div>
                    
                    <p class="text-muted text-center mb-3">
                        Información importante sobre procedimientos operacionales para el mes de enero.
                    </p>
                    
                    <div class="file-info-section">
                        <small class="file-details">
                            <i class="fas fa-file me-1"></i>
                            2.5 MB • PDF
                        </small>
                    </div>
                </div>
                
                <div class="card-footer-actions">
                    <a href="#" class="btn-view-document">
                        <i class="fas fa-eye me-2"></i>
                        Ver Documento
                    </a>
                    
                    <div class="secondary-actions">
                        <button class="action-btn">
                            <i class="fas fa-eye"></i>
                            <span>Marcar Leído</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-signature"></i>
                            <span>Firmar</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-download"></i>
                            <span>Descargar</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tarjeta 2: Word -->
            <div class="informativo-card">
                <div class="card-content">
                    <div class="informativo-header">
                        <div class="flex-grow-1">
                            <div class="informativo-title">Boletín Informativo Diciembre</div>
                            <div class="informativo-meta">
                                <span><i class="fas fa-calendar me-1"></i> 28/12/2023</span>
                                <span><i class="fas fa-user me-1"></i> María Admin</span>
                            </div>
                        </div>
                        <span class="tipo-badge tipo-boletin">📰 Boletín</span>
                    </div>
                    
                    <div class="text-center">
                        <div class="file-icon file-doc">
                            <i class="fas fa-file-word"></i>
                        </div>
                    </div>
                    
                    <p class="text-muted text-center mb-3">
                        Resumen de actividades y logros del equipo durante el mes de diciembre.
                    </p>
                    
                    <div class="file-info-section">
                        <small class="file-details">
                            <i class="fas fa-file me-1"></i>
                            1.8 MB • DOCX
                        </small>
                    </div>
                </div>
                
                <div class="card-footer-actions">
                    <a href="#" class="btn-view-document">
                        <i class="fas fa-eye me-2"></i>
                        Ver Documento
                    </a>
                    
                    <div class="secondary-actions">
                        <button class="action-btn">
                            <i class="fas fa-eye"></i>
                            <span>Marcar Leído</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-signature"></i>
                            <span>Firmar</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-download"></i>
                            <span>Descargar</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tarjeta 3: Imagen -->
            <div class="informativo-card">
                <div class="card-content">
                    <div class="informativo-header">
                        <div class="flex-grow-1">
                            <div class="informativo-title">Diagrama de Procesos Actualizado</div>
                            <div class="informativo-meta">
                                <span><i class="fas fa-calendar me-1"></i> 10/01/2024</span>
                                <span><i class="fas fa-user me-1"></i> Carlos Técnico</span>
                            </div>
                        </div>
                        <span class="tipo-badge tipo-briefing">📋 Briefing</span>
                    </div>
                    
                    <div class="text-center">
                        <div class="file-icon file-img">
                            <i class="fas fa-file-image"></i>
                        </div>
                    </div>
                    
                    <p class="text-muted text-center mb-3">
                        Nuevo diagrama de flujo de procesos operacionales actualizados.
                    </p>
                    
                    <div class="file-info-section">
                        <small class="file-details">
                            <i class="fas fa-file me-1"></i>
                            856 KB • PNG
                        </small>
                    </div>
                </div>
                
                <div class="card-footer-actions">
                    <a href="#" class="btn-view-document">
                        <i class="fas fa-eye me-2"></i>
                        Ver Documento
                    </a>
                    
                    <div class="secondary-actions">
                        <button class="action-btn">
                            <i class="fas fa-eye"></i>
                            <span>Marcar Leído</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-signature"></i>
                            <span>Firmar</span>
                        </button>
                        <button class="action-btn">
                            <i class="fas fa-download"></i>
                            <span>Descargar</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleView(viewType) {
            const container = document.getElementById('informativos-container');
            const gridBtn = document.querySelector('[onclick="toggleView(\'grid\')"]');
            const listBtn = document.querySelector('[onclick="toggleView(\'list\')"]');
            
            // Remover clases activas
            gridBtn.classList.remove('active');
            listBtn.classList.remove('active');
            
            // Remover clases de vista
            container.classList.remove('informativos-grid', 'informativos-list');
            
            if (viewType === 'grid') {
                container.classList.add('informativos-grid');
                gridBtn.classList.add('active');
            } else {
                container.classList.add('informativos-list');
                listBtn.classList.add('active');
            }
        }
    </script>
</body>
</html>
