<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Procesar subida de archivo (solo para supervisores)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'subir_archivo' && hasPermission('manage_admin')) {
        $titulo = cleanInput($_POST['titulo']);
        $descripcion = cleanInput($_POST['descripcion']);
        $tipo = $_POST['tipo'];
        $fecha_publicacion = $_POST['fecha_publicacion'];
        
        if ($titulo && $tipo && $fecha_publicacion && isset($_FILES['archivo'])) {
            $upload_result = uploadFile($_FILES['archivo'], 'uploads/informativos', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']);
            
            if ($upload_result['success']) {
                $query = "INSERT INTO informativos (titulo, descripcion, archivo, tipo, subido_por, fecha_publicacion) 
                          VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($query);
                
                if ($stmt->execute([$titulo, $descripcion, $upload_result['path'], $tipo, $agente['id'], $fecha_publicacion])) {
                    $message = 'Archivo subido exitosamente';
                    
                    // Notificar a todos los agentes
                    $agentes_query = "SELECT id FROM agentes WHERE activo = 1";
                    $agentes_stmt = $conn->prepare($agentes_query);
                    $agentes_stmt->execute();
                    $todos_agentes = $agentes_stmt->fetchAll();
                    
                    foreach ($todos_agentes as $ag) {
                        createNotification(
                            $ag['id'],
                            'sistema',
                            'Nuevo informativo disponible',
                            "Se ha publicado: $titulo",
                            'informativos.php'
                        );
                    }
                } else {
                    $error = 'Error al guardar el archivo en la base de datos';
                }
            } else {
                $error = $upload_result['message'];
            }
        } else {
            $error = 'Por favor complete todos los campos y seleccione un archivo';
        }
    }
}

// Filtros
$tipo_filtro = $_GET['tipo'] ?? '';
$fecha_desde = $_GET['fecha_desde'] ?? '';
$fecha_hasta = $_GET['fecha_hasta'] ?? '';

// Obtener informativos con filtros
$query = "SELECT i.*, a.nombre as subido_por_nombre 
          FROM informativos i 
          JOIN agentes a ON i.subido_por = a.id 
          WHERE i.activo = 1";

$params = [];

if ($tipo_filtro) {
    $query .= " AND i.tipo = ?";
    $params[] = $tipo_filtro;
}

if ($fecha_desde) {
    $query .= " AND i.fecha_publicacion >= ?";
    $params[] = $fecha_desde;
}

if ($fecha_hasta) {
    $query .= " AND i.fecha_publicacion <= ?";
    $params[] = $fecha_hasta;
}

$query .= " ORDER BY i.fecha_publicacion DESC, i.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$informativos = $stmt->fetchAll();

// Agrupar por tipo
$informativos_por_tipo = [];
foreach ($informativos as $informativo) {
    $informativos_por_tipo[$informativo['tipo']][] = $informativo;
}

// Obtener notificaciones para el agente actual
$notificaciones = [];
try {
    // Intentar obtener notificaciones si la función existe
    if (function_exists('getNotifications')) {
        $notificaciones = getNotifications($agente['id']);
    } else {
        // Fallback: obtener notificaciones directamente de la base de datos
        $notif_query = "SELECT * FROM notificaciones
                       WHERE agente_id = ? AND activo = 1
                       ORDER BY created_at DESC LIMIT 10";
        $notif_stmt = $conn->prepare($notif_query);
        $notif_stmt->execute([$agente['id']]);
        $notificaciones = $notif_stmt->fetchAll();
    }
} catch (Exception $e) {
    // En caso de error, usar array vacío
    $notificaciones = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informativos - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --sidebar-width: 280px;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.03;
        }

        .page-header h2 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            position: relative;
            z-index: 1;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-icon.briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .stat-icon.boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .stat-icon.circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .stat-icon.otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            line-height: 1;
        }

        .stat-label {
            color: #4a5568;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
            margin: 0;
        }

        .filters-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .filters-card .card-header {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 20px 25px;
        }

        .filters-card .card-body {
            padding: 25px;
        }

        .informativo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .informativo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .informativo-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .informativo-card:hover::before {
            opacity: 0.02;
        }

        .informativo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .informativo-title {
            font-size: 1.3rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .informativo-meta {
            font-size: 13px;
            color: #6c757d;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .tipo-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .tipo-briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .tipo-boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }
        .tipo-circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        .tipo-otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .file-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-right: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .informativo-card:hover .file-icon {
            transform: scale(1.05) rotate(3deg);
        }

        .file-pdf {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
            color: white;
        }
        .file-doc {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .file-img {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .download-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .empty-state i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .informativo-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .informativo-meta {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }

            .file-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
                margin-right: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }

        /* ===== TEMA DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }

        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .page-header,
        .theme-dark .stat-card,
        .theme-dark .filters-card,
        .theme-dark .informativo-card,
        .theme-dark .empty-state {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .filters-card .card-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background: rgba(26, 32, 44, 0.8) !important;
            border: 1px solid #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            color: #f7fafc !important;
        }

        .theme-dark .btn-outline-secondary {
            border-color: #4a5568 !important;
            color: #a0aec0 !important;
        }

        .theme-dark .btn-outline-secondary:hover {
            background: #4a5568 !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .badge {
            background: rgba(102, 126, 234, 0.8) !important;
            color: white !important;
        }

        /* Estilos para vista grid cuadrada */
        .informativos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
        }

        .informativos-grid .informativo-card {
            aspect-ratio: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 350px;
        }

        .informativos-list {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .informativos-list .informativo-card {
            aspect-ratio: unset;
            min-height: auto;
            padding: 20px;
        }

        .informativos-list .file-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
        }

        /* Botones de acción para lectura y firma */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-read {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-read:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
            color: white;
        }

        .btn-read.read {
            background: #6c757d;
            cursor: not-allowed;
        }

        .btn-sign {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-sign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(253, 203, 110, 0.3);
            color: white;
        }

        .btn-sign.signed {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            cursor: not-allowed;
        }

        .status-indicators {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .status-badge-small {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-read {
            background: rgba(0, 184, 148, 0.1);
            color: #00b894;
            border: 1px solid rgba(0, 184, 148, 0.3);
        }

        .status-signed {
            background: rgba(253, 203, 110, 0.1);
            color: #e17055;
            border: 1px solid rgba(253, 203, 110, 0.3);
        }

        .card-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-actions {
            margin-top: auto;
            padding-top: 15px;
        }

        .view-toggle .btn.active {
            background: var(--gradient-primary) !important;
            color: white !important;
            border-color: transparent !important;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: var(--sidebar-width);
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
        }

        .user-info {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            border: 3px solid var(--primary-color);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-disponible { background: #d4edda; color: #155724; }
        .status-colacion { background: #fff3cd; color: #856404; }
        .status-ocupado { background: #f8d7da; color: #721c24; }
        .status-fuera { background: #e2e3e5; color: #383d41; }

        .sidebar-nav {
            padding: 20px 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 0 25px 25px 0;
            margin-right: 20px;
        }

        .nav-link:hover {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            transform: translateX(5px);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: white;
        }

        .nav-link i {
            width: 20px;
            margin-right: 12px;
            font-size: 16px;
        }

        /* ===== TOP BAR STYLES ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* ===== NOTIFICATION STYLES ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            font-size: 24px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s ease;
        }

        .notification-bell:hover {
            transform: scale(1.1);
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .notifications-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .notifications-header {
            background: var(--gradient-primary);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notifications-actions {
            display: flex;
            gap: 10px;
        }

        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .notifications-filters {
            display: flex;
            padding: 15px 20px;
            gap: 10px;
            border-bottom: 1px solid #eee;
        }

        .filter-btn {
            background: #f8f9fa;
            border: none;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .notifications-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }

        .notification-item {
            display: flex;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.05);
            border-left: 4px solid var(--primary-color);
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .notification-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f8f9fa;
        }

        .empty-notifications {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        /* Animaciones */
        .slide-in-left {
            animation: slideInLeft 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1" style="color: #2d3748; font-weight: 600; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small style="color: #718096; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>" style="position: relative; z-index: 1;"><?php echo $agente['estado']; ?></span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    Lobby
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    CREC
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link active">
                    <i class="fas fa-file-pdf"></i>
                    Informativos
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    Mi Perfil
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    Buscar Agentes
                </a>
            </div>
            <?php if (hasPermission('all_permissions')): ?>
            <div class="nav-item">
                <a href="admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Administración
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Cerrar Sesión
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar fade-in">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                        <i class="fas fa-file-pdf me-2"></i>
                        Informativos
                    </h2>
                    <p class="text-muted mb-0" style="font-weight: 500;">
                        <i class="fas fa-calendar me-2"></i>
                        <?php
                        if (function_exists('formatearFechaEspanol')) {
                            echo formatearFechaEspanol(date('Y-m-d'));
                        } else {
                            echo date('d/m/Y');
                        }
                        ?>
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('H:i'); ?>
                        </span>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <i class="fas fa-upload me-2"></i>
                        Subir Archivo
                    </button>
                <?php endif; ?>

                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <?php
                        $unread_count = 0;
                        if (!empty($notificaciones)) {
                            foreach ($notificaciones as $notif) {
                                if (!$notif['leida']) {
                                    $unread_count++;
                                }
                            }
                        }
                        if ($unread_count > 0): ?>
                            <span class="notification-counter"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards">
            <?php
            // Calcular estadísticas por tipo
            $stats_tipos = [
                'briefing' => ['count' => 0, 'icon' => 'fa-clipboard-list', 'label' => 'Briefings'],
                'boletin' => ['count' => 0, 'icon' => 'fa-newspaper', 'label' => 'Boletines'],
                'circular' => ['count' => 0, 'icon' => 'fa-file-circle-check', 'label' => 'Circulares'],
                'otro' => ['count' => 0, 'icon' => 'fa-file-alt', 'label' => 'Otros']
            ];

            foreach ($informativos as $informativo) {
                if (isset($stats_tipos[$informativo['tipo']])) {
                    $stats_tipos[$informativo['tipo']]['count']++;
                }
            }
            ?>

            <?php foreach ($stats_tipos as $tipo => $data): ?>
                <div class="stat-card fade-in">
                    <div class="stat-icon <?php echo $tipo; ?>">
                        <i class="fas <?php echo $data['icon']; ?>"></i>
                    </div>
                    <div class="stat-number"><?php echo $data['count']; ?></div>
                    <div class="stat-label"><?php echo $data['label']; ?></div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-custom" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success alert-custom" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="filters-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Filtros de Búsqueda
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-4">
                    <div class="col-md-3">
                        <label for="tipo" class="form-label fw-semibold">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            Tipo de Documento
                        </label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="">📄 Todos los tipos</option>
                            <option value="briefing" <?php echo $tipo_filtro === 'briefing' ? 'selected' : ''; ?>>
                                📋 Briefing
                            </option>
                            <option value="boletin" <?php echo $tipo_filtro === 'boletin' ? 'selected' : ''; ?>>
                                📰 Boletín
                            </option>
                            <option value="circular" <?php echo $tipo_filtro === 'circular' ? 'selected' : ''; ?>>
                                📃 Circular
                            </option>
                            <option value="otro" <?php echo $tipo_filtro === 'otro' ? 'selected' : ''; ?>>
                                📝 Otro
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_desde" class="form-label fw-semibold">
                            <i class="fas fa-calendar-alt me-2 text-success"></i>
                            Fecha Desde
                        </label>
                        <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" value="<?php echo $fecha_desde; ?>">
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_hasta" class="form-label fw-semibold">
                            <i class="fas fa-calendar-check me-2 text-warning"></i>
                            Fecha Hasta
                        </label>
                        <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" value="<?php echo $fecha_hasta; ?>">
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary flex-grow-1">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                            <a href="informativos.php" class="btn btn-outline-secondary" title="Limpiar filtros">
                                <i class="fas fa-undo"></i>
                            </a>
                        </div>
                    </div>
                </form>

                <?php if ($tipo_filtro || $fecha_desde || $fecha_hasta): ?>
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Filtros activos:
                            </span>
                            <?php if ($tipo_filtro): ?>
                                <span class="badge bg-primary">
                                    Tipo: <?php echo ucfirst($tipo_filtro); ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_desde): ?>
                                <span class="badge bg-success">
                                    Desde: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_desde);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_desde));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_hasta): ?>
                                <span class="badge bg-warning">
                                    Hasta: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_hasta);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_hasta));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Lista de Informativos -->
        <?php if (empty($informativos)): ?>
            <div class="empty-state">
                <i class="fas fa-file-pdf fa-5x mb-4"></i>
                <h3 class="mb-3">No hay informativos disponibles</h3>
                <p class="text-muted mb-4">Los nuevos documentos aparecerán aquí cuando sean publicados por los supervisores</p>
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <i class="fas fa-upload me-2"></i>
                        Subir Primer Documento
                    </button>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list me-2 text-primary"></i>
                            Documentos Disponibles
                            <span class="badge bg-primary ms-2"><?php echo count($informativos); ?></span>
                        </h4>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('grid')" id="gridViewBtn">
                                <i class="fas fa-th-large me-1"></i>
                                Grid
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleView('list')" id="listViewBtn">
                                <i class="fas fa-list me-1"></i>
                                Lista
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="informativos-container" class="informativos-grid">
                <?php foreach ($informativos as $index => $informativo):
                    // Verificar si el agente ya leyó o firmó este documento
                    $database = new Database();
                    $conn = $database->getConnection();

                    $check_query = "SELECT leido, firmado FROM informativo_lecturas
                                   WHERE informativo_id = ? AND agente_id = ?";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->execute([$informativo['id'], $agente['id']]);
                    $lectura = $check_stmt->fetch();

                    $is_read = $lectura ? $lectura['leido'] : false;
                    $is_signed = $lectura ? $lectura['firmado'] : false;
                ?>
                    <div class="informativo-card slide-in-left" style="animation-delay: <?php echo $index * 0.1; ?>s;" data-id="<?php echo $informativo['id']; ?>">
                        <div class="card-content">
                            <div class="informativo-header">
                                <div class="flex-grow-1">
                                    <div class="informativo-title"><?php echo htmlspecialchars($informativo['titulo']); ?></div>
                                    <div class="informativo-meta">
                                        <span>
                                            <i class="fas fa-calendar me-1 text-primary"></i>
                                            <?php
                                            if (function_exists('formatDate')) {
                                                echo formatDate($informativo['fecha_publicacion']);
                                            } else {
                                                echo date('d/m/Y', strtotime($informativo['fecha_publicacion']));
                                            }
                                            ?>
                                        </span>
                                        <span>
                                            <i class="fas fa-user me-1 text-success"></i>
                                            <?php echo htmlspecialchars($informativo['subido_por_nombre']); ?>
                                        </span>
                                    </div>
                                </div>
                                <span class="tipo-badge tipo-<?php echo $informativo['tipo']; ?>">
                                    <?php
                                    $tipo_icons = [
                                        'briefing' => '📋',
                                        'boletin' => '📰',
                                        'circular' => '📃',
                                        'otro' => '📝'
                                    ];
                                    echo $tipo_icons[$informativo['tipo']] ?? '📄';
                                    ?>
                                    <?php echo ucfirst($informativo['tipo']); ?>
                                </span>
                            </div>

                            <div class="text-center my-3">
                                <div class="file-icon <?php
                                    $ext = strtolower(pathinfo($informativo['archivo'], PATHINFO_EXTENSION));
                                    if ($ext === 'pdf') echo 'file-pdf';
                                    elseif (in_array($ext, ['doc', 'docx'])) echo 'file-doc';
                                    elseif (in_array($ext, ['jpg', 'jpeg', 'png'])) echo 'file-img';
                                    else echo 'file-pdf';
                                ?>" style="margin: 0 auto;">
                                    <i class="fas <?php
                                        if ($ext === 'pdf') echo 'fa-file-pdf';
                                        elseif (in_array($ext, ['doc', 'docx'])) echo 'fa-file-word';
                                        elseif (in_array($ext, ['jpg', 'jpeg', 'png'])) echo 'fa-file-image';
                                        else echo 'fa-file';
                                    ?>"></i>
                                </div>
                            </div>

                            <?php if ($informativo['descripcion']): ?>
                                <p class="text-muted text-center mb-3" style="font-size: 14px; line-height: 1.4;">
                                    <?php echo htmlspecialchars($informativo['descripcion']); ?>
                                </p>
                            <?php endif; ?>

                            <!-- Indicadores de estado -->
                            <div class="status-indicators">
                                <?php if ($is_read): ?>
                                    <span class="status-badge-small status-read">
                                        <i class="fas fa-eye me-1"></i>Leído
                                    </span>
                                <?php endif; ?>
                                <?php if ($is_signed): ?>
                                    <span class="status-badge-small status-signed">
                                        <i class="fas fa-signature me-1"></i>Firmado
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card-actions">
                            <!-- Botones principales -->
                            <div class="d-flex gap-2 mb-3">
                                <a href="<?php echo htmlspecialchars($informativo['archivo']); ?>"
                                   class="download-btn flex-grow-1 text-center"
                                   target="_blank">
                                    <i class="fas fa-eye me-2"></i>
                                    Ver Documento
                                </a>
                                <a href="<?php echo htmlspecialchars($informativo['archivo']); ?>"
                                   class="btn btn-outline-primary"
                                   download
                                   title="Descargar">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>

                            <!-- Botones de acción -->
                            <div class="action-buttons">
                                <button class="btn-read <?php echo $is_read ? 'read' : ''; ?>"
                                        onclick="markAsRead(<?php echo $informativo['id']; ?>)"
                                        <?php echo $is_read ? 'disabled' : ''; ?>>
                                    <i class="fas fa-eye"></i>
                                    <?php echo $is_read ? 'Leído' : 'Marcar como Leído'; ?>
                                </button>

                                <button class="btn-sign <?php echo $is_signed ? 'signed' : ''; ?>"
                                        onclick="signDocument(<?php echo $informativo['id']; ?>)"
                                        <?php echo $is_signed ? 'disabled' : ''; ?>>
                                    <i class="fas fa-signature"></i>
                                    <?php echo $is_signed ? 'Firmado' : 'Firmar Digitalmente'; ?>
                                </button>

                                <?php if (hasPermission('manage_admin')): ?>
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="deleteInformativo(<?php echo $informativo['id']; ?>)"
                                            title="Eliminar documento">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>

                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="fas fa-file me-1"></i>
                                    <?php
                                    $file_size = file_exists($informativo['archivo']) ? filesize($informativo['archivo']) : 0;
                                    if ($file_size >= 1048576) {
                                        echo round($file_size / 1048576, 1) . ' MB';
                                    } elseif ($file_size >= 1024) {
                                        echo round($file_size / 1024, 1) . ' KB';
                                    } else {
                                        echo $file_size . ' bytes';
                                    }
                                    ?>
                                    • <?php echo strtoupper($ext); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Subir Archivo -->
    <?php if (hasPermission('manage_admin')): ?>
    <div class="modal fade" id="subirArchivoModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-upload me-2"></i>
                        Subir Nuevo Informativo
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="action" value="subir_archivo">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="titulo" class="form-label">Título *</label>
                            <input type="text" class="form-control" id="titulo" name="titulo" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="descripcion" class="form-label">Descripción</label>
                            <textarea class="form-control" id="descripcion" name="descripcion" rows="3"></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tipo" class="form-label">Tipo *</label>
                                    <select class="form-select" id="tipo" name="tipo" required>
                                        <option value="">Seleccionar</option>
                                        <option value="briefing">Briefing</option>
                                        <option value="boletin">Boletín</option>
                                        <option value="circular">Circular</option>
                                        <option value="otro">Otro</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fecha_publicacion" class="form-label">Fecha de Publicación *</label>
                                    <input type="date" class="form-control" id="fecha_publicacion" name="fecha_publicacion" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="archivo" class="form-label">Archivo *</label>
                            <input type="file" class="form-control" id="archivo" name="archivo" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                            <div class="form-text">Formatos permitidos: PDF, DOC, DOCX, JPG, PNG (máx. 10MB)</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-2"></i>
                            Subir Archivo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Overlay para cerrar notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtros de categorías -->
        <div class="notifications-filters">
            <button class="filter-btn active" data-filter="all">
                📋 Todas
            </button>
            <button class="filter-btn" data-filter="operacional">
                ✈️ Operacional
            </button>
            <button class="filter-btn" data-filter="social">
                👥 Social
            </button>
            <button class="filter-btn" data-filter="urgente">
                🚨 Urgente
            </button>
            <button class="filter-btn" data-filter="informativa">
                ℹ️ Informativa
            </button>
            <button class="filter-btn" data-filter="general">
                ⚙️ General
            </button>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">�</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativa' => 'ℹ️',
                                default => '⚙️'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                                <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                            </div>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>

                            <?php if (isset($notif['categoria']) && $notif['categoria'] == 'social' && strpos($notif['titulo'], 'solicitud de amistad') !== false): ?>
                                <div class="notification-actions">
                                    <button class="btn-notification-small btn-accept" onclick="acceptFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✓ Aceptar
                                    </button>
                                    <button class="btn-notification-small btn-reject" onclick="rejectFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✗ Rechazar
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Funciones de notificaciones
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            if (panel.classList.contains('show')) {
                closeNotifications();
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function closeNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            panel.classList.remove('show');
            overlay.classList.remove('show');
        }

        function setupNotificationFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const notificationItems = document.querySelectorAll('.notification-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remover clase active de todos los botones
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // Agregar clase active al botón clickeado
                    this.classList.add('active');

                    const filter = this.dataset.filter;

                    // Filtrar notificaciones
                    notificationItems.forEach((item, index) => {
                        const category = item.dataset.category;
                        const shouldShow = filter === 'all' || category === filter;

                        if (shouldShow) {
                            item.style.display = 'flex';
                            item.style.animation = `fadeInNotification 0.5s ease-out ${index * 0.05}s`;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }

        function setupNotificationClicks() {
            const notificationItems = document.querySelectorAll('.notification-item');

            notificationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // No procesar si se hizo clic en un botón
                    if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                        return;
                    }

                    // Marcar como leída
                    markNotificationAsRead(this.dataset.id);
                    this.classList.remove('unread');
                    this.classList.add('read');
                    updateNotificationCounter();
                });
            });
        }

        function markAllAsRead() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                markNotificationAsRead(item.dataset.id);
            });
            updateNotificationCounter();
        }

        function refreshNotifications() {
            location.reload();
        }

        function markNotificationAsRead(notificationId) {
            fetch('ajax/mark_notification_read.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ notification_id: notificationId })
            });
        }

        function updateNotificationCounter() {
            const count = document.querySelectorAll('.notification-item.unread').length;
            const counter = document.querySelector('.notification-counter');
            if (counter) {
                if (count > 0) {
                    counter.textContent = count;
                    counter.style.display = 'flex';
                } else {
                    counter.style.display = 'none';
                }
            }
        }

        // Inicializar notificaciones
        document.addEventListener('DOMContentLoaded', function() {
            setupNotificationFilters();
            setupNotificationClicks();
        });

        // Funciones para marcar como leído y firmar documentos
        function markAsRead(informativoId) {
            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: informativoId,
                    action: 'read'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Documento marcado como leído', 'success');
                    updateDocumentStatus(informativoId, 'read');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error de conexión', 'error');
            });
        }

        function signDocument(informativoId) {
            if (confirm('¿Estás seguro de que quieres firmar digitalmente este documento? Esta acción confirma que has leído y comprendido el contenido.')) {
                fetch('api/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        informativo_id: informativoId,
                        action: 'sign'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Documento firmado digitalmente', 'success');
                        updateDocumentStatus(informativoId, 'sign');
                    } else {
                        showNotification('Error: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión', 'error');
                });
            }
        }

        function updateDocumentStatus(informativoId, action) {
            const card = document.querySelector(`[data-id="${informativoId}"]`);
            if (!card) return;

            const statusIndicators = card.querySelector('.status-indicators');
            const readBtn = card.querySelector('.btn-read');
            const signBtn = card.querySelector('.btn-sign');

            if (action === 'read') {
                // Actualizar botón de lectura
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badge de leído si no existe
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }
            } else if (action === 'sign') {
                // Actualizar botón de firma
                signBtn.classList.add('signed');
                signBtn.disabled = true;
                signBtn.innerHTML = '<i class="fas fa-signature"></i> Firmado';

                // También marcar como leído automáticamente
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badges si no existen
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }

                if (!statusIndicators.querySelector('.status-signed')) {
                    const signBadge = document.createElement('span');
                    signBadge.className = 'status-badge-small status-signed';
                    signBadge.innerHTML = '<i class="fas fa-signature me-1"></i>Firmado';
                    statusIndicators.appendChild(signBadge);
                }
            }
        }

        function toggleView(viewType) {
            const container = document.getElementById('informativos-container');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            // Remover clases activas
            gridBtn.classList.remove('active');
            listBtn.classList.remove('active');

            // Remover clases de vista
            container.classList.remove('informativos-grid', 'informativos-list');

            if (viewType === 'grid') {
                container.classList.add('informativos-grid');
                gridBtn.classList.add('active');
            } else {
                container.classList.add('informativos-list');
                listBtn.classList.add('active');
            }

            // Guardar preferencia
            localStorage.setItem('informativos-view', viewType);
        }

        function deleteInformativo(id) {
            if (confirm('¿Estás seguro de que quieres eliminar este documento? Esta acción no se puede deshacer.')) {
                // Aquí iría la lógica para eliminar el documento
                fetch('api/delete_informativo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification('Documento eliminado exitosamente', 'success');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showNotification('Error al eliminar el documento: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión al eliminar el documento', 'error');
                });
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Inicializar vista guardada
        document.addEventListener('DOMContentLoaded', function() {
            const savedView = localStorage.getItem('informativos-view') || 'grid';
            toggleView(savedView);

            // Aplicar tema guardado
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.classList.add('theme-' + savedTheme);

            // Actualizar icono del tema
            const themeIcon = document.querySelector('.theme-icon');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'theme-icon fas fa-sun' : 'theme-icon fas fa-moon';
            }
        });

        // Función para alternar tema
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.querySelector('.theme-icon');

            if (body.classList.contains('theme-dark')) {
                body.classList.remove('theme-dark');
                body.classList.add('theme-light');
                themeIcon.className = 'theme-icon fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.remove('theme-light');
                body.classList.add('theme-dark');
                themeIcon.className = 'theme-icon fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Mejorar la experiencia de subida de archivos
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('archivo');
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const fileSize = file.size;
                        const maxSize = 10 * 1024 * 1024; // 10MB

                        if (fileSize > maxSize) {
                            showNotification('El archivo es demasiado grande. Máximo 10MB permitido.', 'error');
                            e.target.value = '';
                            return;
                        }

                        // Mostrar información del archivo
                        const fileName = file.name;
                        const fileSizeFormatted = fileSize >= 1048576 ?
                            (fileSize / 1048576).toFixed(1) + ' MB' :
                            (fileSize / 1024).toFixed(1) + ' KB';

                        showNotification(`Archivo seleccionado: ${fileName} (${fileSizeFormatted})`, 'info');
                    }
                });
            }
        });
    </script>
</body>
</html>
