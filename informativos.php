<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Procesar subida de archivo (solo para supervisores)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'subir_archivo' && hasPermission('manage_admin')) {
        $titulo = cleanInput($_POST['titulo']);
        $descripcion = cleanInput($_POST['descripcion']);
        $tipo = $_POST['tipo'];
        $fecha_publicacion = $_POST['fecha_publicacion'];
        $upload_method = $_POST['upload_method'] ?? 'local';

        if ($titulo && $tipo && $fecha_publicacion) {
            $archivo_path = '';
            $drive_file_id = '';
            $drive_file_name = '';
            $drive_file_url = '';

            if ($upload_method === 'local') {
                // Manejo de archivo local
                if (isset($_FILES['archivo']) && $_FILES['archivo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['archivo'], 'uploads/informativos', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']);

                    if ($upload_result['success']) {
                        $archivo_path = $upload_result['path'];
                    } else {
                        $error = $upload_result['message'];
                    }
                } else {
                    $error = 'Por favor seleccione un archivo local';
                }
            } elseif ($upload_method === 'drive') {
                // Manejo de archivo de Google Drive
                $drive_file_id = $_POST['drive_file_id'] ?? '';
                $drive_file_name = $_POST['drive_file_name'] ?? '';
                $drive_file_url = $_POST['drive_file_url'] ?? '';

                if ($drive_file_id && $drive_file_name && $drive_file_url) {
                    $archivo_path = "drive://" . $drive_file_id;
                } else {
                    $error = 'Por favor seleccione un archivo de Google Drive';
                }
            } else {
                $error = 'Método de subida no válido';
            }

            // Si no hay errores, guardar en la base de datos
            if (!isset($error)) {
                $query = "INSERT INTO informativos (titulo, descripcion, archivo, tipo, subido_por, fecha_publicacion, drive_file_id, drive_file_name, drive_file_url, upload_method)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($query);

                if ($stmt->execute([
                    $titulo,
                    $descripcion,
                    $archivo_path,
                    $tipo,
                    $agente['id'],
                    $fecha_publicacion,
                    $drive_file_id,
                    $drive_file_name,
                    $drive_file_url,
                    $upload_method
                ])) {
                    $message = $upload_method === 'drive' ?
                        'Informativo creado exitosamente desde Google Drive' :
                        'Archivo subido exitosamente';

                    // Notificar a todos los agentes
                    $agentes_query = "SELECT id FROM agentes WHERE activo = 1";
                    $agentes_stmt = $conn->prepare($agentes_query);
                    $agentes_stmt->execute();
                    $todos_agentes = $agentes_stmt->fetchAll();

                    foreach ($todos_agentes as $ag) {
                        createNotification(
                            $ag['id'],
                            'sistema',
                            'Nuevo informativo disponible',
                            "Se ha publicado: $titulo",
                            'informativos.php'
                        );
                    }
                } else {
                    $error = 'Error al guardar el informativo en la base de datos';
                }
            }
        } else {
            $error = 'Por favor complete todos los campos obligatorios';
        }
    }
}

// Filtros
$tipo_filtro = $_GET['tipo'] ?? '';
$fecha_desde = $_GET['fecha_desde'] ?? '';
$fecha_hasta = $_GET['fecha_hasta'] ?? '';

// Obtener informativos con filtros
$query = "SELECT i.*, a.nombre as subido_por_nombre 
          FROM informativos i 
          JOIN agentes a ON i.subido_por = a.id 
          WHERE i.activo = 1";

$params = [];

if ($tipo_filtro) {
    $query .= " AND i.tipo = ?";
    $params[] = $tipo_filtro;
}

if ($fecha_desde) {
    $query .= " AND i.fecha_publicacion >= ?";
    $params[] = $fecha_desde;
}

if ($fecha_hasta) {
    $query .= " AND i.fecha_publicacion <= ?";
    $params[] = $fecha_hasta;
}

$query .= " ORDER BY i.fecha_publicacion DESC, i.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$informativos = $stmt->fetchAll();

// Agrupar por tipo
$informativos_por_tipo = [];
foreach ($informativos as $informativo) {
    $informativos_por_tipo[$informativo['tipo']][] = $informativo;
}

// Obtener notificaciones para el agente actual
$notificaciones = [];
try {
    // Intentar obtener notificaciones si la función existe
    if (function_exists('getNotifications')) {
        $notificaciones = getNotifications($agente['id']);
    } else {
        // Fallback: obtener notificaciones directamente de la base de datos
        $notif_query = "SELECT * FROM notificaciones
                       WHERE agente_id = ? AND activo = 1
                       ORDER BY created_at DESC LIMIT 10";
        $notif_stmt = $conn->prepare($notif_query);
        $notif_stmt->execute([$agente['id']]);
        $notificaciones = $notif_stmt->fetchAll();
    }
} catch (Exception $e) {
    // En caso de error, usar array vacío
    $notificaciones = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informativos - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Drive API -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://accounts.google.com/gsi/client"></script>
    <script src="config/google-drive-config.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);

        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            transition: all 0.3s ease;
        }

        .main-content {
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.03;
        }

        .page-header h2 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            position: relative;
            z-index: 1;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-icon.briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .stat-icon.boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .stat-icon.circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .stat-icon.otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            line-height: 1;
        }

        .stat-label {
            color: #4a5568;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
            margin: 0;
        }

        .filters-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .filters-card .card-header {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 20px 25px;
        }

        .filters-card .card-body {
            padding: 25px;
        }

        .informativo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .informativo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .informativo-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .informativo-card:hover::before {
            opacity: 0.02;
        }

        .informativo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .informativo-title {
            font-size: 1.3rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .informativo-meta {
            font-size: 13px;
            color: #6c757d;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .tipo-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .tipo-briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .tipo-boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }
        .tipo-circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        .tipo-otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .file-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-right: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .informativo-card:hover .file-icon {
            transform: scale(1.05) rotate(3deg);
        }

        .file-pdf {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
            color: white;
        }
        .file-doc {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .file-img {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .download-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .empty-state i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .informativo-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .informativo-meta {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }

            .file-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
                margin-right: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }

        /* ===== TEMA DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }



        .theme-dark .page-header,
        .theme-dark .stat-card,
        .theme-dark .filters-card,
        .theme-dark .informativo-card,
        .theme-dark .empty-state {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .filters-card .card-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background: rgba(26, 32, 44, 0.8) !important;
            border: 1px solid #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            color: #f7fafc !important;
        }

        .theme-dark .btn-outline-secondary {
            border-color: #4a5568 !important;
            color: #a0aec0 !important;
        }

        .theme-dark .btn-outline-secondary:hover {
            background: #4a5568 !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .badge {
            background: rgba(102, 126, 234, 0.8) !important;
            color: white !important;
        }

        /* ===== VISTAS DE INFORMATIVOS ===== */

        /* Vista por defecto (Grid) */
        .informativos-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 25px;
            padding: 20px 0;
        }

        .informativos-grid .informativo-card {
            display: flex;
            flex-direction: column;
            height: auto;
            min-height: 400px;
            max-height: none;
        }

        .informativos-grid .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .informativos-grid .file-icon {
            width: 80px;
            height: 80px;
            font-size: 32px;
            margin: 20px auto;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .informativos-grid .file-icon:hover {
            transform: scale(1.1);
        }

        /* Vista Lista */
        .informativos-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            padding: 20px 0;
        }

        .informativos-list .informativo-card {
            display: flex;
            flex-direction: row;
            align-items: center;
            min-height: auto;
            height: auto;
            padding: 20px;
        }

        .informativos-list .card-content {
            flex: 1;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 20px;
        }

        .informativos-list .file-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
            margin: 0;
            flex-shrink: 0;
        }

        .informativos-list .informativo-header {
            flex: 1;
            margin-bottom: 0;
        }

        .informativos-list .informativo-meta {
            margin-top: 8px;
        }

        .informativos-list .status-indicators {
            margin: 0 20px 0 0;
        }

        .informativos-list .file-info-section {
            margin: 0;
            padding: 0;
            border: none;
        }

        .informativos-list .card-footer-actions {
            flex-shrink: 0;
            width: 300px;
            background: transparent;
            padding: 0;
            border: none;
        }

        .informativos-list .primary-action {
            margin-bottom: 10px;
        }

        .informativos-list .secondary-actions {
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        }

        .informativos-list .action-btn {
            padding: 8px 6px;
            font-size: 11px;
        }

        .informativos-list .action-btn span {
            font-size: 10px;
        }

        .informativos-list .status-indicators {
            justify-content: flex-start;
            margin-top: 8px;
        }

        .informativos-list .informativo-title {
            font-size: 16px;
            margin-bottom: 8px;
        }

        .informativos-list .informativo-meta {
            font-size: 13px;
        }

        /* ===== RESPONSIVE PARA VISTAS ===== */
        @media (max-width: 768px) {
            .informativos-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 15px 0;
            }

            .informativos-grid .informativo-card {
                min-height: 350px;
            }

            .informativos-list .informativo-card {
                flex-direction: column;
                align-items: stretch;
            }

            .informativos-list .card-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .informativos-list .file-icon {
                align-self: center;
            }

            .informativos-list .card-footer-actions {
                width: 100%;
                margin-top: 15px;
            }

            .informativos-list .secondary-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 480px) {
            .informativos-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 10px 0;
            }

            .informativos-grid .informativo-card {
                min-height: 320px;
            }

            .informativos-list .secondary-actions {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        /* Botones de acción para lectura y firma */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-read {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-read:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
            color: white;
        }

        .btn-read.read {
            background: #6c757d;
            cursor: not-allowed;
        }

        .btn-sign {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-sign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(253, 203, 110, 0.3);
            color: white;
        }

        .btn-sign.signed {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            cursor: not-allowed;
        }

        .status-indicators {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .status-badge-small {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-read {
            background: rgba(0, 184, 148, 0.1);
            color: #00b894;
            border: 1px solid rgba(0, 184, 148, 0.3);
        }

        .status-signed {
            background: rgba(253, 203, 110, 0.1);
            color: #e17055;
            border: 1px solid rgba(253, 203, 110, 0.3);
        }

        .card-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-actions {
            margin-top: auto;
            padding-top: 15px;
        }

        .view-toggle .btn.active {
            background: var(--gradient-primary) !important;
            color: white !important;
            border-color: transparent !important;
        }



        /* ===== TOP BAR STYLES ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* ===== NOTIFICATION STYLES ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            font-size: 24px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s ease;
        }

        .notification-bell:hover {
            transform: scale(1.1);
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .notifications-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .notifications-header {
            background: var(--gradient-primary);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notifications-actions {
            display: flex;
            gap: 10px;
        }

        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .notifications-filters {
            display: flex;
            padding: 15px 20px;
            gap: 10px;
            border-bottom: 1px solid #eee;
        }

        .filter-btn {
            background: #f8f9fa;
            border: none;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .notifications-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }

        .notification-item {
            display: flex;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.05);
            border-left: 4px solid var(--primary-color);
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .notification-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f8f9fa;
        }

        .empty-notifications {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        /* Animaciones */
        .slide-in-left {
            animation: slideInLeft 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ===== BOTÓN DE SUBIR MODERNO ===== */
        .btn-upload-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            padding: 12px 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-upload-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-upload-modern:hover::before {
            left: 100%;
        }

        .btn-upload-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-upload-modern .upload-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .btn-upload-modern .upload-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .btn-upload-modern .upload-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
        }

        .btn-upload-modern .upload-subtitle {
            font-size: 12px;
            opacity: 0.8;
            line-height: 1;
        }

        .btn-upload-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            padding: 16px 32px;
            color: white;
            display: flex;
            align-items: center;
            gap: 16px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-upload-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-upload-primary .upload-icon-large {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        /* ===== MODAL MODERNO ===== */
        .modern-modal {
            border: none;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border: none;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .modal-title-section {
            flex: 1;
        }

        .modal-title-modern {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            line-height: 1.2;
        }

        .modal-subtitle {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .btn-close-modern {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn-close-modern:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body-modern {
            padding: 40px;
        }

        /* ===== ZONA DE ARRASTRE ===== */
        .file-drop-zone {
            border: 3px dashed #e2e8f0;
            border-radius: 20px;
            padding: 40px 20px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            margin-bottom: 30px;
        }

        .file-drop-zone:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
            transform: translateY(-2px);
        }

        .file-drop-zone.dragover {
            border-color: #667eea;
            background: linear-gradient(135deg, #e6f0ff 0%, #dce7ff 100%);
            transform: scale(1.02);
        }

        .drop-zone-content {
            pointer-events: none;
        }

        .drop-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .file-drop-zone h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .file-drop-zone p {
            color: #718096;
            margin-bottom: 20px;
        }

        .file-input-hidden {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            pointer-events: auto;
        }

        .file-formats {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .format-badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .file-limit {
            color: #a0aec0;
            font-size: 12px;
        }

        /* ===== PREVIEW DE ARCHIVO ===== */
        .file-preview {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .file-icon {
            width: 50px;
            height: 50px;
            background: #667eea;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .file-size {
            display: block;
            font-size: 14px;
            color: #718096;
        }

        .btn-remove-file {
            background: #fed7d7;
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e53e3e;
            transition: all 0.3s ease;
        }

        .btn-remove-file:hover {
            background: #feb2b2;
            transform: scale(1.1);
        }

        /* ===== FORMULARIO MODERNO ===== */
        .form-section {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
        }

        .form-group-modern {
            margin-bottom: 25px;
        }

        .form-label-modern {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control-modern,
        .form-select-modern {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control-modern:focus,
        .form-select-modern:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* ===== FOOTER DEL MODAL ===== */
        .modal-footer-modern {
            padding: 30px 40px;
            background: #f8fafc;
            border: none;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .btn-cancel-modern {
            background: #e2e8f0;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: #4a5568;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-cancel-modern:hover {
            background: #cbd5e0;
            transform: translateY(-1px);
        }

        .btn-upload-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-upload-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* ===== SELECTOR DE MÉTODO ===== */
        .upload-method-selector {
            margin-bottom: 30px;
        }

        .method-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 16px;
            padding: 4px;
            gap: 4px;
        }

        .method-tab {
            flex: 1;
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .method-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .method-tab:hover:not(.active) {
            color: #475569;
            background: rgba(255, 255, 255, 0.5);
        }

        .upload-method {
            transition: all 0.3s ease;
        }

        /* ===== GOOGLE DRIVE STYLES ===== */
        .drive-selector {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
        }

        .drive-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .drive-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 75%, #ea4335 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
        }

        .drive-info h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .drive-info p {
            color: #718096;
            margin: 0;
        }

        .btn-drive-auth,
        .btn-drive-picker {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
            cursor: pointer;
        }

        .btn-drive-auth:hover,
        .btn-drive-picker:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
        }

        .drive-note {
            display: block;
            color: #a0aec0;
            font-size: 12px;
            margin-top: 10px;
        }

        .drive-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 12px;
            font-weight: 500;
        }

        .drive-status.success {
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }

        .drive-status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }

        .drive-file-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .drive-file-icon {
            width: 50px;
            height: 50px;
            background: #4285f4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .drive-file-details {
            flex: 1;
        }

        .drive-file-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .drive-file-meta {
            font-size: 14px;
            color: #718096;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .modal-header-modern {
                padding: 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .modal-body-modern {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .btn-upload-modern .upload-text {
                display: none;
            }

            .btn-upload-modern {
                padding: 12px;
            }

            .drive-header {
                flex-direction: column;
                gap: 15px;
            }

            .method-tabs {
                flex-direction: column;
            }
        }

        /* ===== BOTONES DE VISTA MODERNOS ===== */
        .view-toggle-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        .view-toggle-group {
            background: #f1f5f9;
            border-radius: 16px;
            padding: 4px;
            display: flex;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .view-toggle-btn {
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #64748b;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .view-toggle-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
        }

        .view-toggle-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        .view-toggle-btn.active::before {
            opacity: 0.1;
        }

        .view-toggle-btn:hover:not(.active) {
            color: #475569;
            background: rgba(255, 255, 255, 0.7);
            transform: translateY(-1px);
        }

        .view-toggle-btn i {
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .view-toggle-btn span {
            position: relative;
            z-index: 1;
        }

        @media (max-width: 768px) {
            .view-toggle-btn span {
                display: none;
            }

            .view-toggle-btn {
                padding: 10px 12px;
            }
        }

        /* ===== TARJETAS MEJORADAS ===== */
        .informativo-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .informativo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: #e2e8f0;
        }

        .card-content {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .file-info-section {
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid #f1f5f9;
        }

        .file-details {
            color: #64748b;
            font-size: 13px;
            font-weight: 500;
        }

        /* ===== FOOTER DE TARJETA ===== */
        .card-footer-actions {
            background: #f8fafc;
            padding: 20px 25px;
            border-top: 1px solid #f1f5f9;
        }

        .primary-action {
            margin-bottom: 15px;
        }

        .btn-view-document {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-view-document:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .secondary-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 10px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }

        .action-btn:hover:not(:disabled) {
            border-color: #cbd5e0;
            background: #f8fafc;
            transform: translateY(-1px);
        }

        .action-btn i {
            font-size: 16px;
        }

        .action-btn span {
            font-size: 11px;
            line-height: 1;
        }

        /* Estados específicos de botones */
        .btn-read:not(.read):hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }

        .btn-read.read {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }

        .btn-sign:not(.signed):hover {
            border-color: #10b981;
            color: #10b981;
        }

        .btn-sign.signed {
            background: #d1fae5;
            border-color: #10b981;
            color: #047857;
        }

        .btn-download:hover {
            border-color: #f59e0b;
            color: #f59e0b;
        }

        .btn-delete:hover {
            border-color: #ef4444;
            color: #ef4444;
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }

        .action-btn:not(:disabled) {
            cursor: pointer;
            pointer-events: auto;
        }

        .action-btn:not(:disabled):hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }

        .action-btn:not(:disabled):active {
            transform: translateY(0);
        }

        /* ===== BADGES DE ESTADO ===== */
        .status-indicators {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            justify-content: center;
        }

        .status-badge-small {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-read {
            background: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .status-signed {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #86efac;
        }

        /* ===== BADGES DE TIPO ===== */
        .tipo-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .tipo-briefing {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .tipo-boletin {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border: 1px solid #86efac;
        }

        .tipo-circular {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .tipo-otro {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            color: #7c3aed;
            border: 1px solid #c4b5fd;
        }

        /* ===== ICONOS DE ARCHIVO ===== */
        .file-icon {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .file-pdf {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .file-doc {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
        }

        .file-img {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .file-icon i {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        /* ===== HEADER DE INFORMATIVO ===== */
        .informativo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            gap: 15px;
        }

        .informativo-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .informativo-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #64748b;
        }

        .informativo-meta span {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* ===== RESPONSIVE PARA TARJETAS ===== */
        @media (max-width: 768px) {
            .card-content {
                padding: 20px;
            }

            .card-footer-actions {
                padding: 15px 20px;
            }

            .secondary-actions {
                grid-template-columns: repeat(2, 1fr);
            }

            .action-btn {
                padding: 8px 6px;
            }

            .action-btn span {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>


    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar fade-in">
            <div class="d-flex align-items-center">
                <div>
                    <h2 class="mb-1" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                        <i class="fas fa-file-pdf me-2"></i>
                        Informativos
                    </h2>
                    <p class="text-muted mb-0" style="font-weight: 500;">
                        <i class="fas fa-calendar me-2"></i>
                        <?php
                        if (function_exists('formatearFechaEspanol')) {
                            echo formatearFechaEspanol(date('Y-m-d'));
                        } else {
                            echo date('d/m/Y');
                        }
                        ?>
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('H:i'); ?>
                        </span>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn-upload-modern" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <span class="upload-title">Subir Documento</span>
                            <small class="upload-subtitle">Nuevo informativo</small>
                        </div>
                    </button>
                <?php endif; ?>

                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <?php
                        $unread_count = 0;
                        if (!empty($notificaciones)) {
                            foreach ($notificaciones as $notif) {
                                if (!$notif['leida']) {
                                    $unread_count++;
                                }
                            }
                        }
                        if ($unread_count > 0): ?>
                            <span class="notification-counter"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards">
            <?php
            // Calcular estadísticas por tipo
            $stats_tipos = [
                'briefing' => ['count' => 0, 'icon' => 'fa-clipboard-list', 'label' => 'Briefings'],
                'boletin' => ['count' => 0, 'icon' => 'fa-newspaper', 'label' => 'Boletines'],
                'circular' => ['count' => 0, 'icon' => 'fa-file-circle-check', 'label' => 'Circulares'],
                'otro' => ['count' => 0, 'icon' => 'fa-file-alt', 'label' => 'Otros']
            ];

            foreach ($informativos as $informativo) {
                if (isset($stats_tipos[$informativo['tipo']])) {
                    $stats_tipos[$informativo['tipo']]['count']++;
                }
            }
            ?>

            <?php foreach ($stats_tipos as $tipo => $data): ?>
                <div class="stat-card fade-in">
                    <div class="stat-icon <?php echo $tipo; ?>">
                        <i class="fas <?php echo $data['icon']; ?>"></i>
                    </div>
                    <div class="stat-number"><?php echo $data['count']; ?></div>
                    <div class="stat-label"><?php echo $data['label']; ?></div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-custom" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success alert-custom" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="filters-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Filtros de Búsqueda
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-4">
                    <div class="col-md-3">
                        <label for="tipo" class="form-label fw-semibold">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            Tipo de Documento
                        </label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="">📄 Todos los tipos</option>
                            <option value="briefing" <?php echo $tipo_filtro === 'briefing' ? 'selected' : ''; ?>>
                                📋 Briefing
                            </option>
                            <option value="boletin" <?php echo $tipo_filtro === 'boletin' ? 'selected' : ''; ?>>
                                📰 Boletín
                            </option>
                            <option value="circular" <?php echo $tipo_filtro === 'circular' ? 'selected' : ''; ?>>
                                📃 Circular
                            </option>
                            <option value="otro" <?php echo $tipo_filtro === 'otro' ? 'selected' : ''; ?>>
                                📝 Otro
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_desde" class="form-label fw-semibold">
                            <i class="fas fa-calendar-alt me-2 text-success"></i>
                            Fecha Desde
                        </label>
                        <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" value="<?php echo $fecha_desde; ?>">
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_hasta" class="form-label fw-semibold">
                            <i class="fas fa-calendar-check me-2 text-warning"></i>
                            Fecha Hasta
                        </label>
                        <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" value="<?php echo $fecha_hasta; ?>">
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary flex-grow-1">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                            <a href="informativos.php" class="btn btn-outline-secondary" title="Limpiar filtros">
                                <i class="fas fa-undo"></i>
                            </a>
                        </div>
                    </div>
                </form>

                <?php if ($tipo_filtro || $fecha_desde || $fecha_hasta): ?>
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Filtros activos:
                            </span>
                            <?php if ($tipo_filtro): ?>
                                <span class="badge bg-primary">
                                    Tipo: <?php echo ucfirst($tipo_filtro); ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_desde): ?>
                                <span class="badge bg-success">
                                    Desde: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_desde);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_desde));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_hasta): ?>
                                <span class="badge bg-warning">
                                    Hasta: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_hasta);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_hasta));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Lista de Informativos -->
        <?php if (empty($informativos)): ?>
            <div class="empty-state">
                <i class="fas fa-file-pdf fa-5x mb-4"></i>
                <h3 class="mb-3">No hay informativos disponibles</h3>
                <p class="text-muted mb-4">Los nuevos documentos aparecerán aquí cuando sean publicados por los supervisores</p>
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn-upload-primary" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <div class="upload-icon-large">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <span>Subir Primer Documento</span>
                    </button>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="mb-0">
                            <i class="fas fa-list me-2 text-primary"></i>
                            Documentos Disponibles
                            <span class="badge bg-primary ms-2"><?php echo count($informativos); ?></span>
                        </h4>
                        <div class="view-toggle-container">
                            <div class="view-toggle-group">
                                <button class="view-toggle-btn active" onclick="toggleView('grid')" id="gridViewBtn" data-view="grid">
                                    <i class="fas fa-th-large"></i>
                                    <span>Cuadrícula</span>
                                </button>
                                <button class="view-toggle-btn" onclick="toggleView('list')" id="listViewBtn" data-view="list">
                                    <i class="fas fa-list"></i>
                                    <span>Lista</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="informativos-container" class="informativos-grid">
                <?php foreach ($informativos as $index => $informativo):
                    // Verificar si el agente ya leyó o firmó este documento
                    $database = new Database();
                    $conn = $database->getConnection();

                    $check_query = "SELECT leido, firmado FROM informativo_lecturas
                                   WHERE informativo_id = ? AND agente_id = ?";
                    $check_stmt = $conn->prepare($check_query);
                    $check_stmt->execute([$informativo['id'], $agente['id']]);
                    $lectura = $check_stmt->fetch();

                    $is_read = $lectura ? $lectura['leido'] : false;
                    $is_signed = $lectura ? $lectura['firmado'] : false;
                ?>
                    <div class="informativo-card slide-in-left" style="animation-delay: <?php echo $index * 0.1; ?>s;" data-id="<?php echo $informativo['id']; ?>">
                        <div class="card-content">
                            <div class="informativo-header">
                                <div class="flex-grow-1">
                                    <div class="informativo-title"><?php echo htmlspecialchars($informativo['titulo']); ?></div>
                                    <div class="informativo-meta">
                                        <span>
                                            <i class="fas fa-calendar me-1 text-primary"></i>
                                            <?php
                                            if (function_exists('formatDate')) {
                                                echo formatDate($informativo['fecha_publicacion']);
                                            } else {
                                                echo date('d/m/Y', strtotime($informativo['fecha_publicacion']));
                                            }
                                            ?>
                                        </span>
                                        <span>
                                            <i class="fas fa-user me-1 text-success"></i>
                                            <?php echo htmlspecialchars($informativo['subido_por_nombre']); ?>
                                        </span>
                                    </div>
                                </div>
                                <span class="tipo-badge tipo-<?php echo $informativo['tipo']; ?>">
                                    <?php
                                    $tipo_icons = [
                                        'briefing' => '📋',
                                        'boletin' => '📰',
                                        'circular' => '📃',
                                        'otro' => '📝'
                                    ];
                                    echo $tipo_icons[$informativo['tipo']] ?? '📄';
                                    ?>
                                    <?php echo ucfirst($informativo['tipo']); ?>
                                </span>
                            </div>

                            <div class="text-center my-3">
                                <div class="file-icon <?php
                                    $ext = strtolower(pathinfo($informativo['archivo'], PATHINFO_EXTENSION));
                                    if ($ext === 'pdf') echo 'file-pdf';
                                    elseif (in_array($ext, ['doc', 'docx'])) echo 'file-doc';
                                    elseif (in_array($ext, ['jpg', 'jpeg', 'png'])) echo 'file-img';
                                    else echo 'file-pdf';
                                ?>" style="margin: 0 auto;">
                                    <i class="fas <?php
                                        if ($ext === 'pdf') echo 'fa-file-pdf';
                                        elseif (in_array($ext, ['doc', 'docx'])) echo 'fa-file-word';
                                        elseif (in_array($ext, ['jpg', 'jpeg', 'png'])) echo 'fa-file-image';
                                        else echo 'fa-file';
                                    ?>"></i>
                                </div>
                            </div>

                            <?php if ($informativo['descripcion']): ?>
                                <p class="text-muted text-center mb-3" style="font-size: 14px; line-height: 1.4;">
                                    <?php echo htmlspecialchars($informativo['descripcion']); ?>
                                </p>
                            <?php endif; ?>

                            <!-- Indicadores de estado -->
                            <div class="status-indicators">
                                <?php if ($is_read): ?>
                                    <span class="status-badge-small status-read">
                                        <i class="fas fa-eye me-1"></i>Leído
                                    </span>
                                <?php endif; ?>
                                <?php if ($is_signed): ?>
                                    <span class="status-badge-small status-signed">
                                        <i class="fas fa-signature me-1"></i>Firmado
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Información del archivo -->
                            <div class="file-info-section">
                                <small class="file-details">
                                    <i class="fas fa-file me-1"></i>
                                    <?php
                                    $file_size = file_exists($informativo['archivo']) ? filesize($informativo['archivo']) : 0;
                                    if ($file_size >= 1048576) {
                                        echo round($file_size / 1048576, 1) . ' MB';
                                    } elseif ($file_size >= 1024) {
                                        echo round($file_size / 1024, 1) . ' KB';
                                    } else {
                                        echo $file_size . ' bytes';
                                    }
                                    ?>
                                    • <?php echo strtoupper($ext); ?>
                                </small>
                            </div>
                        </div>

                        <!-- Botones de acción (footer de la tarjeta) -->
                        <div class="card-footer-actions">
                            <!-- Botón principal de ver documento -->
                            <div class="primary-action">
                                <a href="<?php echo htmlspecialchars($informativo['archivo']); ?>"
                                   class="btn-view-document"
                                   target="_blank">
                                    <i class="fas fa-eye me-2"></i>
                                    Ver Documento
                                </a>
                            </div>

                            <!-- Botones secundarios -->
                            <div class="secondary-actions">
                                <button class="action-btn btn-read <?php echo $is_read ? 'read' : ''; ?>"
                                        data-informativo-id="<?php echo $informativo['id']; ?>"
                                        data-action="read"
                                        <?php echo $is_read ? 'disabled' : ''; ?>
                                        title="<?php echo $is_read ? 'Ya leído' : 'Marcar como leído'; ?>">
                                    <i class="fas fa-eye"></i>
                                    <span><?php echo $is_read ? 'Leído' : 'Marcar Leído'; ?></span>
                                </button>

                                <button class="action-btn btn-sign <?php echo $is_signed ? 'signed' : ''; ?>"
                                        data-informativo-id="<?php echo $informativo['id']; ?>"
                                        data-action="sign"
                                        <?php echo $is_signed ? 'disabled' : ''; ?>
                                        title="<?php echo $is_signed ? 'Ya firmado' : 'Firmar digitalmente'; ?>">
                                    <i class="fas fa-signature"></i>
                                    <span><?php echo $is_signed ? 'Firmado' : 'Firmar'; ?></span>
                                </button>

                                <button class="action-btn btn-download"
                                        data-file-path="<?php echo htmlspecialchars($informativo['archivo']); ?>"
                                        data-action="download"
                                        title="Descargar documento">
                                    <i class="fas fa-download"></i>
                                    <span>Descargar</span>
                                </button>

                                <?php if (hasPermission('manage_admin')): ?>
                                    <button class="action-btn btn-delete"
                                            data-informativo-id="<?php echo $informativo['id']; ?>"
                                            data-action="delete"
                                            title="Eliminar documento">
                                        <i class="fas fa-trash"></i>
                                        <span>Eliminar</span>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Subir Archivo Moderno -->
    <?php if (hasPermission('manage_admin')): ?>
    <div class="modal fade" id="subirArchivoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modern-modal">
                <div class="modal-header-modern">
                    <div class="modal-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="modal-title-section">
                        <h4 class="modal-title-modern">Subir Nuevo Informativo</h4>
                        <p class="modal-subtitle">Comparte documentos importantes con tu equipo</p>
                    </div>
                    <button type="button" class="btn-close-modern" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form method="POST" enctype="multipart/form-data" class="upload-form">
                    <input type="hidden" name="action" value="subir_archivo">
                    <input type="hidden" name="upload_method" id="uploadMethod" value="local">
                    <input type="hidden" name="drive_file_id" id="driveFileId" value="">
                    <input type="hidden" name="drive_file_name" id="driveFileName" value="">
                    <input type="hidden" name="drive_file_url" id="driveFileUrl" value="">

                    <div class="modal-body-modern">
                        <!-- Selector de método de subida -->
                        <div class="upload-method-selector">
                            <div class="method-tabs">
                                <button type="button" class="method-tab active" data-method="local" onclick="switchUploadMethod('local')">
                                    <i class="fas fa-upload me-2"></i>
                                    Archivo Local
                                </button>
                                <button type="button" class="method-tab" data-method="drive" onclick="switchUploadMethod('drive')">
                                    <i class="fab fa-google-drive me-2"></i>
                                    Google Drive
                                </button>
                            </div>
                        </div>

                        <!-- Método 1: Subida local -->
                        <div class="upload-method" id="localUpload">
                            <!-- Zona de arrastre de archivos -->
                            <div class="file-drop-zone" id="fileDropZone">
                                <div class="drop-zone-content">
                                    <div class="drop-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h5>Arrastra tu archivo aquí</h5>
                                    <p>o haz clic para seleccionar</p>
                                    <input type="file" class="file-input-hidden" id="archivo" name="archivo" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <div class="file-formats">
                                        <span class="format-badge">PDF</span>
                                        <span class="format-badge">DOC</span>
                                        <span class="format-badge">DOCX</span>
                                        <span class="format-badge">JPG</span>
                                        <span class="format-badge">PNG</span>
                                    </div>
                                    <small class="file-limit">Tamaño máximo: 10MB</small>
                                </div>
                            </div>
                        </div>

                        <!-- Método 2: Google Drive -->
                        <div class="upload-method" id="driveUpload" style="display: none;">
                            <div class="drive-selector">
                                <div class="drive-header">
                                    <div class="drive-icon">
                                        <i class="fab fa-google-drive"></i>
                                    </div>
                                    <div class="drive-info">
                                        <h5>Seleccionar desde Google Drive</h5>
                                        <p>Elige un documento directamente desde tu Google Drive</p>
                                    </div>
                                </div>

                                <div class="drive-auth" id="driveAuth">
                                    <button type="button" class="btn-drive-auth" onclick="authenticateGoogleDrive()">
                                        <i class="fab fa-google me-2"></i>
                                        Conectar con Google Drive
                                    </button>
                                    <small class="drive-note">Se abrirá una ventana para autorizar el acceso a tu Google Drive</small>
                                </div>

                                <div class="drive-picker" id="drivePicker" style="display: none;">
                                    <button type="button" class="btn-drive-picker" onclick="openDrivePicker()">
                                        <i class="fab fa-google-drive me-2"></i>
                                        Seleccionar Archivo de Drive
                                    </button>
                                    <div class="drive-status" id="driveStatus"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview del archivo seleccionado -->
                        <div class="file-preview" id="filePreview" style="display: none;">
                            <div class="file-info">
                                <div class="file-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                <div class="file-details">
                                    <span class="file-name"></span>
                                    <span class="file-size"></span>
                                </div>
                                <button type="button" class="btn-remove-file" onclick="removeFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Formulario de detalles -->
                        <div class="form-section">
                            <div class="form-group-modern">
                                <label for="titulo" class="form-label-modern">
                                    <i class="fas fa-heading me-2"></i>
                                    Título del Documento *
                                </label>
                                <input type="text" class="form-control-modern" id="titulo" name="titulo" placeholder="Ej: Briefing Operacional Enero 2024" required>
                            </div>

                            <div class="form-group-modern">
                                <label for="descripcion" class="form-label-modern">
                                    <i class="fas fa-align-left me-2"></i>
                                    Descripción
                                </label>
                                <textarea class="form-control-modern" id="descripcion" name="descripcion" rows="3" placeholder="Describe brevemente el contenido del documento..."></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group-modern">
                                    <label for="tipo" class="form-label-modern">
                                        <i class="fas fa-tag me-2"></i>
                                        Tipo de Documento *
                                    </label>
                                    <select class="form-select-modern" id="tipo" name="tipo" required>
                                        <option value="">Seleccionar tipo</option>
                                        <option value="briefing">📋 Briefing</option>
                                        <option value="boletin">📰 Boletín</option>
                                        <option value="circular">📄 Circular</option>
                                        <option value="otro">📁 Otro</option>
                                    </select>
                                </div>

                                <div class="form-group-modern">
                                    <label for="fecha_publicacion" class="form-label-modern">
                                        <i class="fas fa-calendar me-2"></i>
                                        Fecha de Publicación *
                                    </label>
                                    <input type="date" class="form-control-modern" id="fecha_publicacion" name="fecha_publicacion" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer-modern">
                        <button type="button" class="btn-cancel-modern" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn-upload-submit">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            Subir Documento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Overlay para cerrar notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtros de categorías -->
        <div class="notifications-filters">
            <button class="filter-btn active" data-filter="all">
                📋 Todas
            </button>
            <button class="filter-btn" data-filter="operacional">
                ✈️ Operacional
            </button>
            <button class="filter-btn" data-filter="social">
                👥 Social
            </button>
            <button class="filter-btn" data-filter="urgente">
                🚨 Urgente
            </button>
            <button class="filter-btn" data-filter="informativa">
                ℹ️ Informativa
            </button>
            <button class="filter-btn" data-filter="general">
                ⚙️ General
            </button>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">�</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativa' => 'ℹ️',
                                default => '⚙️'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                                <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                            </div>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>

                            <?php if (isset($notif['categoria']) && $notif['categoria'] == 'social' && strpos($notif['titulo'], 'solicitud de amistad') !== false): ?>
                                <div class="notification-actions">
                                    <button class="btn-notification-small btn-accept" onclick="acceptFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✓ Aceptar
                                    </button>
                                    <button class="btn-notification-small btn-reject" onclick="rejectFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✗ Rechazar
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>


        // Funciones de notificaciones
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            if (panel.classList.contains('show')) {
                closeNotifications();
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function closeNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            panel.classList.remove('show');
            overlay.classList.remove('show');
        }

        function setupNotificationFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const notificationItems = document.querySelectorAll('.notification-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remover clase active de todos los botones
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // Agregar clase active al botón clickeado
                    this.classList.add('active');

                    const filter = this.dataset.filter;

                    // Filtrar notificaciones
                    notificationItems.forEach((item, index) => {
                        const category = item.dataset.category;
                        const shouldShow = filter === 'all' || category === filter;

                        if (shouldShow) {
                            item.style.display = 'flex';
                            item.style.animation = `fadeInNotification 0.5s ease-out ${index * 0.05}s`;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }

        function setupNotificationClicks() {
            const notificationItems = document.querySelectorAll('.notification-item');

            notificationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // No procesar si se hizo clic en un botón
                    if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                        return;
                    }

                    // Marcar como leída
                    markNotificationAsRead(this.dataset.id);
                    this.classList.remove('unread');
                    this.classList.add('read');
                    updateNotificationCounter();
                });
            });
        }

        function markAllAsRead() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                markNotificationAsRead(item.dataset.id);
            });
            updateNotificationCounter();
        }

        function refreshNotifications() {
            location.reload();
        }

        function markNotificationAsRead(notificationId) {
            fetch('ajax/mark_notification_read.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ notification_id: notificationId })
            });
        }

        function updateNotificationCounter() {
            const count = document.querySelectorAll('.notification-item.unread').length;
            const counter = document.querySelector('.notification-counter');
            if (counter) {
                if (count > 0) {
                    counter.textContent = count;
                    counter.style.display = 'flex';
                } else {
                    counter.style.display = 'none';
                }
            }
        }

        // Inicializar sistema
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded - Inicializando sistema...');

            // Configurar vista inicial
            const savedView = localStorage.getItem('informativos-view') || 'grid';
            toggleView(savedView);

            // Configurar notificaciones
            setupNotificationFilters();
            setupNotificationClicks();

            // Configurar event listeners para botones de acción
            setupActionButtons();

            console.log('✅ Sistema inicializado correctamente');
        });

        // Función para configurar event listeners de botones
        function setupActionButtons() {
            console.log('🔧 Configurando event listeners para botones...');

            // Buscar todos los botones de acción
            const actionButtons = document.querySelectorAll('.action-btn');
            console.log(`📊 Encontrados ${actionButtons.length} botones de acción`);

            // Event listener universal para todos los botones de acción
            actionButtons.forEach((button, index) => {
                console.log(`🔍 Configurando botón ${index + 1}:`, {
                    classes: button.className,
                    action: button.dataset.action,
                    informativoId: button.dataset.informativoId,
                    filePath: button.dataset.filePath,
                    disabled: button.disabled
                });
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Si el botón está deshabilitado, no hacer nada
                    if (this.disabled) {
                        console.log('🚫 Botón deshabilitado, ignorando click');
                        return;
                    }

                    const action = this.dataset.action;
                    const informativoId = this.dataset.informativoId;
                    const filePath = this.dataset.filePath;

                    console.log('🔍 Botón clickeado:', {
                        action: action,
                        informativoId: informativoId,
                        filePath: filePath,
                        button: this
                    });

                    // Ejecutar la acción correspondiente
                    switch (action) {
                        case 'read':
                            if (informativoId) {
                                console.log('📖 Ejecutando markAsRead con ID:', informativoId);
                                markAsRead(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción read');
                            }
                            break;

                        case 'sign':
                            if (informativoId) {
                                console.log('✍️ Ejecutando signDocument con ID:', informativoId);
                                signDocument(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción sign');
                            }
                            break;

                        case 'download':
                            if (filePath) {
                                console.log('💾 Ejecutando downloadDocument con path:', filePath);
                                downloadDocument(filePath);
                            } else {
                                console.error('❌ No se encontró filePath para acción download');
                            }
                            break;

                        case 'delete':
                            if (informativoId) {
                                console.log('🗑️ Ejecutando deleteInformativo con ID:', informativoId);
                                deleteInformativo(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción delete');
                            }
                            break;

                        default:
                            console.error('❌ Acción no reconocida:', action);
                            break;
                    }
                });
            });

            console.log('✅ Event listeners configurados correctamente');
        }

        // Funciones para marcar como leído y firmar documentos
        function markAsRead(informativoId) {
            console.log('markAsRead called with ID:', informativoId);

            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: informativoId,
                    action: 'read'
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showNotification('Documento marcado como leído', 'success');
                    updateDocumentStatus(informativoId, 'read');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error de conexión', 'error');
            });
        }

        function signDocument(informativoId) {
            console.log('signDocument called with ID:', informativoId);

            if (confirm('¿Estás seguro de que quieres firmar digitalmente este documento? Esta acción confirma que has leído y comprendido el contenido.')) {
                fetch('api/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        informativo_id: informativoId,
                        action: 'sign'
                    })
                })
                .then(response => {
                    console.log('Sign response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Sign response data:', data);
                    if (data.success) {
                        showNotification('Documento firmado digitalmente', 'success');
                        updateDocumentStatus(informativoId, 'signed');
                    } else {
                        showNotification('Error: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión', 'error');
                });
            }
        }

        function updateDocumentStatus(informativoId, action) {
            const card = document.querySelector(`[data-id="${informativoId}"]`);
            if (!card) return;

            const statusIndicators = card.querySelector('.status-indicators');
            const readBtn = card.querySelector('.btn-read');
            const signBtn = card.querySelector('.btn-sign');

            if (action === 'read') {
                // Actualizar botón de lectura
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badge de leído si no existe
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }
            } else if (action === 'sign') {
                // Actualizar botón de firma
                signBtn.classList.add('signed');
                signBtn.disabled = true;
                signBtn.innerHTML = '<i class="fas fa-signature"></i> Firmado';

                // También marcar como leído automáticamente
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badges si no existen
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }

                if (!statusIndicators.querySelector('.status-signed')) {
                    const signBadge = document.createElement('span');
                    signBadge.className = 'status-badge-small status-signed';
                    signBadge.innerHTML = '<i class="fas fa-signature me-1"></i>Firmado';
                    statusIndicators.appendChild(signBadge);
                }
            }
        }

        function toggleView(viewType) {
            const container = document.getElementById('informativos-container');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            // Remover clases activas
            gridBtn.classList.remove('active');
            listBtn.classList.remove('active');

            // Remover clases de vista
            container.classList.remove('informativos-grid', 'informativos-list');

            if (viewType === 'grid') {
                container.classList.add('informativos-grid');
                gridBtn.classList.add('active');
            } else {
                container.classList.add('informativos-list');
                listBtn.classList.add('active');
            }

            // Guardar preferencia
            localStorage.setItem('informativos-view', viewType);
        }

        function deleteInformativo(id) {
            console.log('deleteInformativo called with ID:', id);

            if (confirm('¿Estás seguro de que quieres eliminar este documento? Esta acción no se puede deshacer.')) {
                fetch('api/delete_informativo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => {
                    console.log('Delete response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Delete response data:', data);
                    if (data.success) {
                        showNotification('Documento eliminado exitosamente', 'success');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showNotification('Error al eliminar el documento: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión al eliminar el documento', 'error');
                });
            }
        }

        function downloadDocument(filePath) {
            console.log('downloadDocument called with path:', filePath);

            // Crear un enlace temporal para descargar
            const link = document.createElement('a');
            link.href = filePath;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Descarga iniciada', 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Inicializar vista guardada
        document.addEventListener('DOMContentLoaded', function() {
            const savedView = localStorage.getItem('informativos-view') || 'grid';
            toggleView(savedView);

            // Aplicar tema guardado
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.classList.add('theme-' + savedTheme);

            // Actualizar icono del tema
            const themeIcon = document.querySelector('.theme-icon');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'theme-icon fas fa-sun' : 'theme-icon fas fa-moon';
            }
        });

        // Función para alternar tema
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.querySelector('.theme-icon');

            if (body.classList.contains('theme-dark')) {
                body.classList.remove('theme-dark');
                body.classList.add('theme-light');
                themeIcon.className = 'theme-icon fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.remove('theme-light');
                body.classList.add('theme-dark');
                themeIcon.className = 'theme-icon fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Funcionalidad moderna de subida de archivos
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
        });

        function setupFileUpload() {
            const fileInput = document.getElementById('archivo');
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');

            if (!fileInput || !dropZone) return;

            // Manejar clic en la zona de arrastre
            dropZone.addEventListener('click', function() {
                fileInput.click();
            });

            // Manejar cambio de archivo
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFileSelection(file);
                }
            });

            // Manejar arrastrar y soltar
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    fileInput.files = files;
                    handleFileSelection(file);
                }
            });
        }

        function handleFileSelection(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];

            // Validar tamaño
            if (file.size > maxSize) {
                showNotification('El archivo es demasiado grande. Máximo 10MB permitido.', 'error');
                removeFile();
                return;
            }

            // Validar tipo
            if (!allowedTypes.includes(file.type)) {
                showNotification('Tipo de archivo no permitido. Solo PDF, DOC, DOCX, JPG, PNG.', 'error');
                removeFile();
                return;
            }

            // Mostrar preview
            showFilePreview(file);
            showNotification(`Archivo seleccionado: ${file.name}`, 'success');
        }

        function showFilePreview(file) {
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');
            const fileName = filePreview.querySelector('.file-name');
            const fileSize = filePreview.querySelector('.file-size');
            const fileIcon = filePreview.querySelector('.file-icon i');

            // Ocultar zona de arrastre y mostrar preview
            dropZone.style.display = 'none';
            filePreview.style.display = 'block';

            // Actualizar información del archivo
            fileName.textContent = file.name;

            const fileSizeFormatted = file.size >= 1048576 ?
                (file.size / 1048576).toFixed(1) + ' MB' :
                (file.size / 1024).toFixed(1) + ' KB';
            fileSize.textContent = fileSizeFormatted;

            // Actualizar icono según el tipo
            const ext = file.name.split('.').pop().toLowerCase();
            if (ext === 'pdf') {
                fileIcon.className = 'fas fa-file-pdf';
            } else if (['doc', 'docx'].includes(ext)) {
                fileIcon.className = 'fas fa-file-word';
            } else if (['jpg', 'jpeg', 'png'].includes(ext)) {
                fileIcon.className = 'fas fa-file-image';
            } else {
                fileIcon.className = 'fas fa-file';
            }
        }

        function removeFile() {
            const fileInput = document.getElementById('archivo');
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');

            // Limpiar input
            fileInput.value = '';

            // Mostrar zona de arrastre y ocultar preview
            dropZone.style.display = 'block';
            filePreview.style.display = 'none';
        }

        // ===== FUNCIONALIDAD GOOGLE DRIVE =====

        // Configuración de Google Drive API (desde archivo de configuración)
        const GOOGLE_CLIENT_ID = GOOGLE_DRIVE_CONFIG?.CLIENT_ID || 'TU_CLIENT_ID_AQUI';
        const GOOGLE_API_KEY = GOOGLE_DRIVE_CONFIG?.API_KEY || 'TU_API_KEY_AQUI';
        const DISCOVERY_DOC = GOOGLE_DRIVE_CONFIG?.DISCOVERY_DOC || 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest';
        const SCOPES = GOOGLE_DRIVE_CONFIG?.SCOPES || 'https://www.googleapis.com/auth/drive.readonly';

        let gapi_loaded = false;
        let gsi_loaded = false;
        let tokenClient;

        // Cargar APIs de Google
        function loadGoogleAPIs() {
            gapi.load('client', initializeGapi);
            google.accounts.id.initialize({
                client_id: GOOGLE_CLIENT_ID,
                callback: handleCredentialResponse
            });
            gsi_loaded = true;
        }

        async function initializeGapi() {
            await gapi.client.init({
                apiKey: GOOGLE_API_KEY,
                discoveryDocs: [DISCOVERY_DOC],
            });
            gapi_loaded = true;
        }

        function handleCredentialResponse(response) {
            console.log('Credential response:', response);
        }

        // Cambiar método de subida
        function switchUploadMethod(method) {
            const tabs = document.querySelectorAll('.method-tab');
            const methods = document.querySelectorAll('.upload-method');
            const uploadMethodInput = document.getElementById('uploadMethod');
            const fileInput = document.getElementById('archivo');

            // Actualizar tabs
            tabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.method === method) {
                    tab.classList.add('active');
                }
            });

            // Mostrar/ocultar métodos
            methods.forEach(methodDiv => {
                methodDiv.style.display = 'none';
            });

            if (method === 'local') {
                document.getElementById('localUpload').style.display = 'block';
                fileInput.required = true;
            } else if (method === 'drive') {
                document.getElementById('driveUpload').style.display = 'block';
                fileInput.required = false;
                if (!gapi_loaded || !gsi_loaded) {
                    loadGoogleAPIs();
                }
            }

            // Actualizar campo oculto
            uploadMethodInput.value = method;

            // Limpiar selecciones previas
            clearFileSelections();
        }

        // Autenticar con Google Drive
        function authenticateGoogleDrive() {
            if (!gapi_loaded) {
                showNotification('Cargando APIs de Google...', 'info');
                setTimeout(authenticateGoogleDrive, 1000);
                return;
            }

            tokenClient = google.accounts.oauth2.initTokenClient({
                client_id: GOOGLE_CLIENT_ID,
                scope: SCOPES,
                callback: (response) => {
                    if (response.error !== undefined) {
                        showNotification('Error de autenticación: ' + response.error, 'error');
                        return;
                    }

                    // Autenticación exitosa
                    document.getElementById('driveAuth').style.display = 'none';
                    document.getElementById('drivePicker').style.display = 'block';
                    showNotification('Conectado a Google Drive exitosamente', 'success');
                },
            });

            tokenClient.requestAccessToken();
        }

        // Abrir selector de archivos de Google Drive
        async function openDrivePicker() {
            try {
                const response = await gapi.client.drive.files.list({
                    pageSize: 20,
                    fields: 'nextPageToken, files(id, name, mimeType, size, webViewLink, thumbnailLink)',
                    q: "mimeType='application/pdf' or mimeType='application/msword' or mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType='image/jpeg' or mimeType='image/png'"
                });

                const files = response.result.files;
                if (files && files.length > 0) {
                    showDriveFileSelector(files);
                } else {
                    showNotification('No se encontraron archivos compatibles en tu Google Drive', 'info');
                }
            } catch (error) {
                console.error('Error al obtener archivos:', error);
                showNotification('Error al acceder a Google Drive: ' + error.message, 'error');
            }
        }

        // Mostrar selector de archivos
        function showDriveFileSelector(files) {
            const statusDiv = document.getElementById('driveStatus');

            let html = '<div class="drive-files-list">';
            html += '<h6 class="mb-3">Selecciona un archivo:</h6>';

            files.forEach(file => {
                const fileSize = file.size ? formatFileSize(parseInt(file.size)) : 'Tamaño desconocido';
                const fileIcon = getFileIcon(file.mimeType);

                html += `
                    <div class="drive-file-item" onclick="selectDriveFile('${file.id}', '${file.name}', '${file.webViewLink}', '${file.mimeType}')">
                        <div class="drive-file-icon">
                            <i class="${fileIcon}"></i>
                        </div>
                        <div class="drive-file-details">
                            <div class="drive-file-name">${file.name}</div>
                            <div class="drive-file-meta">${fileSize}</div>
                        </div>
                        <div class="drive-select-btn">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            statusDiv.innerHTML = html;
        }

        // Seleccionar archivo de Google Drive
        function selectDriveFile(fileId, fileName, fileUrl, mimeType) {
            // Actualizar campos ocultos
            document.getElementById('driveFileId').value = fileId;
            document.getElementById('driveFileName').value = fileName;
            document.getElementById('driveFileUrl').value = fileUrl;

            // Mostrar confirmación
            const statusDiv = document.getElementById('driveStatus');
            statusDiv.innerHTML = `
                <div class="drive-file-info">
                    <div class="drive-file-icon">
                        <i class="${getFileIcon(mimeType)}"></i>
                    </div>
                    <div class="drive-file-details">
                        <div class="drive-file-name">${fileName}</div>
                        <div class="drive-file-meta">Archivo seleccionado de Google Drive</div>
                    </div>
                    <button type="button" class="btn-remove-file" onclick="clearDriveSelection()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            statusDiv.className = 'drive-status success';

            showNotification(`Archivo seleccionado: ${fileName}`, 'success');
        }

        // Limpiar selección de Google Drive
        function clearDriveSelection() {
            document.getElementById('driveFileId').value = '';
            document.getElementById('driveFileName').value = '';
            document.getElementById('driveFileUrl').value = '';

            const statusDiv = document.getElementById('driveStatus');
            statusDiv.innerHTML = '';
            statusDiv.className = 'drive-status';
        }

        // Limpiar todas las selecciones de archivos
        function clearFileSelections() {
            // Limpiar archivo local
            removeFile();

            // Limpiar Google Drive
            clearDriveSelection();
        }

        // Obtener icono según tipo de archivo
        function getFileIcon(mimeType) {
            if (mimeType.includes('pdf')) {
                return 'fas fa-file-pdf';
            } else if (mimeType.includes('word') || mimeType.includes('document')) {
                return 'fas fa-file-word';
            } else if (mimeType.includes('image')) {
                return 'fas fa-file-image';
            } else {
                return 'fas fa-file';
            }
        }

        // Formatear tamaño de archivo
        function formatFileSize(bytes) {
            if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(1) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(1) + ' KB';
            } else {
                return bytes + ' bytes';
            }
        }

        // Validar formulario antes de enviar
        function validateForm() {
            const uploadMethod = document.getElementById('uploadMethod').value;
            const fileInput = document.getElementById('archivo');
            const driveFileId = document.getElementById('driveFileId').value;

            if (uploadMethod === 'local') {
                if (!fileInput.files || fileInput.files.length === 0) {
                    showNotification('Por favor selecciona un archivo local', 'error');
                    return false;
                }
            } else if (uploadMethod === 'drive') {
                if (!driveFileId) {
                    showNotification('Por favor selecciona un archivo de Google Drive', 'error');
                    return false;
                }
            }

            return true;
        }

        // Agregar validación al formulario
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.upload-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateForm()) {
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    </script>

    <style>
        /* ===== ESTILOS ADICIONALES PARA GOOGLE DRIVE ===== */
        .drive-files-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: white;
        }

        .drive-file-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .drive-file-item:hover {
            background: #f8fafc;
        }

        .drive-file-item:last-child {
            border-bottom: none;
        }

        .drive-select-btn {
            color: #667eea;
            font-size: 18px;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .drive-file-item:hover .drive-select-btn {
            opacity: 1;
        }
    </style>
</body>
</html>
