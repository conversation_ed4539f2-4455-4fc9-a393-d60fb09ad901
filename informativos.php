<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

$message = '';
$error = '';

// Procesar subida de archivo (solo para supervisores)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'subir_archivo' && hasPermission('manage_admin')) {
        $titulo = cleanInput($_POST['titulo']);
        $descripcion = cleanInput($_POST['descripcion']);
        $tipo = $_POST['tipo'];
        $fecha_publicacion = $_POST['fecha_publicacion'];
        $upload_method = $_POST['upload_method'] ?? 'local';

        if ($titulo && $tipo && $fecha_publicacion) {
            $archivo_path = '';
            $drive_file_id = '';
            $drive_file_name = '';
            $drive_file_url = '';

            if ($upload_method === 'local') {
                // Manejo de archivo local
                if (isset($_FILES['archivo']) && $_FILES['archivo']['error'] === UPLOAD_ERR_OK) {
                    $upload_result = uploadFile($_FILES['archivo'], 'uploads/informativos', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']);

                    if ($upload_result['success']) {
                        $archivo_path = $upload_result['path'];
                    } else {
                        $error = $upload_result['message'];
                    }
                } else {
                    $error = 'Por favor seleccione un archivo local';
                }
            } elseif ($upload_method === 'drive') {
                // Manejo de archivo de Google Drive
                $drive_file_id = $_POST['drive_file_id'] ?? '';
                $drive_file_name = $_POST['drive_file_name'] ?? '';
                $drive_file_url = $_POST['drive_file_url'] ?? '';

                if ($drive_file_id && $drive_file_name && $drive_file_url) {
                    $archivo_path = "drive://" . $drive_file_id;
                } else {
                    $error = 'Por favor seleccione un archivo de Google Drive';
                }
            } else {
                $error = 'Método de subida no válido';
            }

            // Si no hay errores, guardar en la base de datos
            if (!isset($error)) {
                $query = "INSERT INTO informativos (titulo, descripcion, archivo, tipo, subido_por, fecha_publicacion, drive_file_id, drive_file_name, drive_file_url, upload_method)
                          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($query);

                if ($stmt->execute([
                    $titulo,
                    $descripcion,
                    $archivo_path,
                    $tipo,
                    $agente['id'],
                    $fecha_publicacion,
                    $drive_file_id,
                    $drive_file_name,
                    $drive_file_url,
                    $upload_method
                ])) {
                    $message = $upload_method === 'drive' ?
                        'Informativo creado exitosamente desde Google Drive' :
                        'Archivo subido exitosamente';

                    // Notificar a todos los agentes
                    $agentes_query = "SELECT id FROM agentes WHERE activo = 1";
                    $agentes_stmt = $conn->prepare($agentes_query);
                    $agentes_stmt->execute();
                    $todos_agentes = $agentes_stmt->fetchAll();

                    foreach ($todos_agentes as $ag) {
                        createNotification(
                            $ag['id'],
                            'sistema',
                            'Nuevo informativo disponible',
                            "Se ha publicado: $titulo",
                            'informativos.php'
                        );
                    }
                } else {
                    $error = 'Error al guardar el informativo en la base de datos';
                }
            }
        } else {
            $error = 'Por favor complete todos los campos obligatorios';
        }
    }
}

// Filtros
$tipo_filtro = $_GET['tipo'] ?? '';
$fecha_desde = $_GET['fecha_desde'] ?? '';
$fecha_hasta = $_GET['fecha_hasta'] ?? '';

// Obtener informativos con filtros
$query = "SELECT i.*, a.nombre as subido_por_nombre
          FROM informativos i
          LEFT JOIN agentes a ON i.subido_por = a.id";

$params = [];

// Agregar filtros solo si existen
$where_conditions = [];

if ($tipo_filtro) {
    $where_conditions[] = "i.tipo = ?";
    $params[] = $tipo_filtro;
}

if ($fecha_desde) {
    $where_conditions[] = "i.fecha_publicacion >= ?";
    $params[] = $fecha_desde;
}

if ($fecha_hasta) {
    $where_conditions[] = "i.fecha_publicacion <= ?";
    $params[] = $fecha_hasta;
}

// Agregar WHERE si hay condiciones
if (!empty($where_conditions)) {
    $query .= " WHERE " . implode(" AND ", $where_conditions);
}

$query .= " ORDER BY i.fecha_publicacion DESC";

$stmt = $conn->prepare($query);
$stmt->execute($params);
$informativos = $stmt->fetchAll();

// Debug: verificar si hay informativos
error_log("Informativos encontrados: " . count($informativos));
if (count($informativos) > 0) {
    error_log("Primer informativo: " . print_r($informativos[0], true));
}

// Agrupar por tipo
$informativos_por_tipo = [];
foreach ($informativos as $informativo) {
    $informativos_por_tipo[$informativo['tipo']][] = $informativo;
}

// Obtener notificaciones para el agente actual
$notificaciones = [];
try {
    // Intentar obtener notificaciones si la función existe
    if (function_exists('getNotifications')) {
        $notificaciones = getNotifications($agente['id']);
    } else {
        // Fallback: obtener notificaciones directamente de la base de datos
        $notif_query = "SELECT * FROM notificaciones
                       WHERE agente_id = ? AND activo = 1
                       ORDER BY created_at DESC LIMIT 10";
        $notif_stmt = $conn->prepare($notif_query);
        $notif_stmt->execute([$agente['id']]);
        $notificaciones = $notif_stmt->fetchAll();
    }
} catch (Exception $e) {
    // En caso de error, usar array vacío
    $notificaciones = [];
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informativos - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="css/custom.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Google Drive API -->
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://accounts.google.com/gsi/client"></script>
    <script src="config/google-drive-config.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --sidebar-width: 280px;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);

        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            transition: all 0.3s ease;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .page-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .page-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.03;
        }

        .page-header h2 {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            margin: 0;
            position: relative;
            z-index: 1;
        }

        .stats-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin: 0 auto 15px;
            position: relative;
            z-index: 1;
            transition: all 0.4s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-card:hover .stat-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .stat-icon.briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .stat-icon.boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .stat-icon.circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .stat-icon.otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 8px;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
            line-height: 1;
        }

        .stat-label {
            color: #4a5568;
            font-size: 14px;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.5px;
            position: relative;
            z-index: 1;
            margin: 0;
        }

        .filters-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            overflow: hidden;
        }

        .filters-card .card-header {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 20px 25px;
        }

        .filters-card .card-body {
            padding: 25px;
        }

        .informativo-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: var(--shadow-soft);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .informativo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .informativo-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .informativo-card:hover::before {
            opacity: 0.02;
        }

        .informativo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .informativo-title {
            font-size: 1.3rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .informativo-meta {
            font-size: 13px;
            color: #6c757d;
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .tipo-badge {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .tipo-briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .tipo-boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }
        .tipo-circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }
        .tipo-otro {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .file-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            margin-right: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .informativo-card:hover .file-icon {
            transform: scale(1.05) rotate(3deg);
        }

        .file-pdf {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
            color: white;
        }
        .file-doc {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }
        .file-img {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .download-btn {
            background: var(--gradient-primary);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .download-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .download-btn:hover::before {
            left: 100%;
        }

        .download-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            border-radius: 25px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            border-color: transparent;
            color: white;
            transform: translateY(-2px);
        }

        .empty-state {
            text-align: center;
            padding: 80px 20px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .empty-state i {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-cards {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .informativo-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .informativo-meta {
                flex-direction: column;
                gap: 8px;
                align-items: flex-start;
            }

            .file-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
                margin-right: 15px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
        }

        /* ===== TEMA DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }



        .theme-dark .page-header,
        .theme-dark .stat-card,
        .theme-dark .filters-card,
        .theme-dark .informativo-card,
        .theme-dark .empty-state {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .filters-card .card-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .theme-dark .form-control,
        .theme-dark .form-select {
            background: rgba(26, 32, 44, 0.8) !important;
            border: 1px solid #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .form-control:focus,
        .theme-dark .form-select:focus {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: #667eea !important;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
            color: #f7fafc !important;
        }

        .theme-dark .btn-outline-secondary {
            border-color: #4a5568 !important;
            color: #a0aec0 !important;
        }

        .theme-dark .btn-outline-secondary:hover {
            background: #4a5568 !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .badge {
            background: rgba(102, 126, 234, 0.8) !important;
            color: white !important;
        }

        /* ===== VISTAS DE INFORMATIVOS MODERNAS ===== */

        /* Contenedor principal de documentos */
        .documents-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .documents-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .documents-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .documents-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .documents-title h4 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .documents-count {
            background: var(--gradient-primary);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        /* Botones de vista modernos */
        .view-toggle-container {
            position: relative;
            z-index: 1;
        }

        .view-toggle-group {
            display: flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 6px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .view-toggle-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .view-toggle-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .view-toggle-btn.active::before {
            opacity: 1;
        }

        .view-toggle-btn.active {
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .view-toggle-btn i,
        .view-toggle-btn span {
            position: relative;
            z-index: 1;
        }

        /* Grid Layout - 2 columnas, 6 archivos por página */
        .informativos-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            padding: 20px 0;
            position: relative;
            z-index: 1;
        }

        .informativos-grid .informativo-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-height: 320px;
            display: flex;
            flex-direction: column;
        }

        .informativos-grid .informativo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .informativos-grid .informativo-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .informativos-grid .informativo-card:hover::before {
            opacity: 0.03;
        }

        .informativos-grid .card-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 1;
        }

        .informativos-grid .file-icon {
            width: 80px;
            height: 80px;
            font-size: 32px;
            margin: 20px auto;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .informativos-grid .file-icon:hover {
            transform: scale(1.1) rotate(5deg);
        }

        /* List Layout - 2 columnas, 4 archivos por página */
        .informativos-list {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            padding: 20px 0;
            position: relative;
            z-index: 1;
        }

        .informativos-list .informativo-card {
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 120px;
            position: relative;
            overflow: hidden;
        }

        .informativos-list .informativo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .informativos-list .informativo-card:hover {
            transform: translateX(5px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.1);
        }

        .informativos-list .informativo-card:hover::before {
            opacity: 0.03;
        }

        .informativos-list .card-content {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .informativos-list .file-icon {
            width: 60px;
            height: 60px;
            font-size: 24px;
            margin: 0;
            flex-shrink: 0;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .informativos-list .informativo-header {
            flex: 1;
            margin-bottom: 0;
        }

        .informativos-list .informativo-title {
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .informativos-list .informativo-meta {
            font-size: 13px;
            margin-top: 8px;
        }

        .informativos-list .card-footer-actions {
            flex-shrink: 0;
            width: 280px;
            background: transparent;
            padding: 0;
            border: none;
            position: relative;
            z-index: 1;
        }

        .informativos-list .primary-action {
            margin-bottom: 10px;
        }

        .informativos-list .secondary-actions {
            grid-template-columns: repeat(4, 1fr);
            gap: 5px;
        }

        .informativos-list .action-btn {
            padding: 8px 6px;
            font-size: 11px;
        }

        .informativos-list .action-btn span {
            font-size: 10px;
        }

        /* ===== PAGINACIÓN MODERNA ===== */
        .pagination-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
        }

        .pagination-info {
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
            color: #4a5568;
        }

        .pagination-info .current-page {
            background: var(--gradient-primary);
            color: white;
            padding: 8px 16px;
            border-radius: 12px;
            font-weight: 700;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .pagination-info .total-pages {
            color: #718096;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 45px;
            height: 45px;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            color: #4a5568;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .pagination-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .pagination-btn:hover::before {
            opacity: 1;
        }

        .pagination-btn:hover {
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .pagination-btn:disabled:hover {
            color: #4a5568;
            transform: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .pagination-btn:disabled::before {
            opacity: 0;
        }

        .pagination-btn i {
            position: relative;
            z-index: 1;
        }

        .pagination-btn.first-page,
        .pagination-btn.last-page {
            width: auto;
            padding: 0 16px;
            gap: 8px;
        }

        .pagination-btn.first-page span,
        .pagination-btn.last-page span {
            position: relative;
            z-index: 1;
            font-size: 14px;
        }

        .page-numbers {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .page-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 10px;
            background: transparent;
            color: #6b7280;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .page-number::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .page-number.active::before {
            opacity: 1;
        }

        .page-number.active {
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .page-number:hover:not(.active)::before {
            opacity: 0.1;
        }

        .page-number:hover:not(.active) {
            color: var(--primary-color);
            transform: scale(1.05);
        }

        .page-number span {
            position: relative;
            z-index: 1;
        }

        .page-ellipsis {
            color: #9ca3af;
            font-weight: 600;
            padding: 0 8px;
        }

        /* Items per page selector */
        .items-per-page {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
            color: #4a5568;
        }

        .items-per-page select {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 8px 12px;
            font-weight: 600;
            color: #4a5568;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .items-per-page select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* ===== ANIMACIONES ===== */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
            100% {
                transform: scale(1);
            }
        }

        .informativo-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .pagination-container {
            animation: slideInLeft 0.8s ease-out;
        }

        .view-toggle-btn.active {
            animation: pulse 0.3s ease-out;
        }

        /* ===== NUEVA SECCIÓN DE DOCUMENTOS ===== */
        .documents-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .documents-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .documents-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .documents-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .documents-title h4 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .documents-count {
            background: var(--gradient-primary);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .view-controls {
            position: relative;
            z-index: 1;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 6px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .view-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 12px 20px;
            border: none;
            background: transparent;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            color: #6b7280;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .view-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .view-btn.active::before {
            opacity: 1;
        }

        .view-btn.active {
            color: white;
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .view-btn i,
        .view-btn span {
            position: relative;
            z-index: 1;
        }

        /* Contenedor de documentos */
        .documents-container {
            position: relative;
            z-index: 1;
        }

        /* Vista Cuadrícula - 2 filas x 4 columnas */
        .documents-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            transition: all 0.3s ease;
        }

        /* Vista Lista - 2 columnas x 4 filas */
        .documents-grid.list-view {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .document-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .document-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .document-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .document-card:hover::before {
            opacity: 0.03;
        }

        /* Vista Lista - Cards horizontales */
        .documents-grid.list-view .document-card {
            flex-direction: row;
            align-items: center;
            padding: 20px;
            height: auto;
            min-height: 120px;
        }

        .documents-grid.list-view .document-card:hover {
            transform: translateX(5px);
        }

        /* Header de documento */
        .document-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .file-icon-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .file-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .file-icon.file-pdf {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%);
        }

        .file-icon.file-word {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .file-icon.file-excel {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        }

        .file-icon.file-image {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
        }

        .file-icon.file-default {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
        }

        .document-card:hover .file-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .file-format {
            font-size: 12px;
            font-weight: 700;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .document-status {
            display: flex;
            gap: 8px;
        }

        .status-badge {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .status-badge.read {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        }

        .status-badge.signed {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
        }

        /* Contenido del documento */
        .document-content {
            flex: 1;
            position: relative;
            z-index: 1;
            margin-bottom: 20px;
        }

        .document-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .document-meta {
            display: grid;
            grid-template-columns: 1fr;
            gap: 8px;
        }

        .meta-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .meta-label {
            color: #6b7280;
            font-weight: 500;
        }

        .document-type {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .document-type.type-briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
        }

        .document-type.type-boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            color: white;
        }

        .document-type.type-circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: white;
        }

        .document-type.type-informativo {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
            color: white;
        }

        .document-type.type-otro {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
            color: white;
        }

        .file-size,
        .uploader,
        .upload-date {
            color: #4a5568;
            font-weight: 600;
        }

        /* Vista Lista - Ajustes específicos */
        .documents-grid.list-view .document-header {
            margin-bottom: 0;
            margin-right: 20px;
            flex-shrink: 0;
        }

        .documents-grid.list-view .file-icon {
            width: 50px;
            height: 50px;
            font-size: 20px;
        }

        .documents-grid.list-view .document-content {
            margin-bottom: 0;
            margin-right: 20px;
        }

        .documents-grid.list-view .document-meta {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        /* Botones de acción */
        .document-actions {
            display: flex;
            gap: 8px;
            position: relative;
            z-index: 1;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 12px 8px;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.8);
            color: #4a5568;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .action-btn:hover::before {
            opacity: 0.1;
        }

        .action-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
            color: var(--primary-color);
        }

        .action-btn.primary {
            background: var(--gradient-primary);
            color: white;
        }

        .action-btn.primary:hover {
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .action-btn.primary::before {
            background: rgba(255, 255, 255, 0.2);
        }

        .action-btn.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            pointer-events: none;
        }

        .action-btn i {
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .action-btn span {
            position: relative;
            z-index: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Vista Lista - Botones horizontales */
        .documents-grid.list-view .document-actions {
            flex-direction: row;
            flex-shrink: 0;
            width: 280px;
        }

        .documents-grid.list-view .action-btn {
            flex-direction: row;
            gap: 6px;
            padding: 10px 12px;
            font-size: 11px;
        }

        .documents-grid.list-view .action-btn i {
            font-size: 14px;
        }

        /* Paginación */
        .pagination-wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 1;
        }

        .pagination-info {
            color: #6b7280;
            font-weight: 500;
            font-size: 14px;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .page-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.8);
            color: #4a5568;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .page-btn:hover:not(:disabled) {
            background: var(--gradient-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .page-numbers {
            display: flex;
            gap: 5px;
        }

        .page-number {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 8px;
            background: transparent;
            color: #6b7280;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .page-number.active {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .page-number:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
        }

        /* ===== TEMAS DARK Y LIGHT PARA DOCUMENTOS ===== */

        /* Tema Dark */
        .theme-dark .documents-section {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .documents-section::before {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
        }

        .theme-dark .documents-title h4 {
            background: linear-gradient(135deg, #81e6d9 0%, #d6f5d6 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
        }

        .theme-dark .documents-count {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .theme-dark .view-toggle {
            background: rgba(45, 55, 72, 0.9) !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .view-btn {
            color: #cbd5e0 !important;
        }

        .theme-dark .view-btn:hover {
            color: #e2e8f0 !important;
        }

        .theme-dark .view-btn.active {
            color: white !important;
        }

        .theme-dark .document-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .document-card::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .theme-dark .document-card:hover {
            border-color: #667eea !important;
        }

        .theme-dark .document-title {
            color: #f7fafc !important;
        }

        .theme-dark .meta-label {
            color: #a0aec0 !important;
        }

        .theme-dark .file-size,
        .theme-dark .uploader,
        .theme-dark .upload-date {
            color: #e2e8f0 !important;
        }

        .theme-dark .file-format {
            color: #cbd5e0 !important;
        }

        /* Iconos de archivo en dark mode - mantener colores originales */
        .theme-dark .file-icon.file-pdf {
            background: linear-gradient(135deg, #ff7675 0%, #d63031 100%) !important;
            color: white !important;
        }

        .theme-dark .file-icon.file-word {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
            color: white !important;
        }

        .theme-dark .file-icon.file-excel {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%) !important;
            color: white !important;
        }

        .theme-dark .file-icon.file-image {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%) !important;
            color: white !important;
        }

        .theme-dark .file-icon.file-default {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%) !important;
            color: white !important;
        }

        /* Tipos de documento en dark mode - mantener colores originales */
        .theme-dark .document-type.type-briefing {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%) !important;
            color: white !important;
        }

        .theme-dark .document-type.type-boletin {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%) !important;
            color: white !important;
        }

        .theme-dark .document-type.type-circular {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%) !important;
            color: white !important;
        }

        .theme-dark .document-type.type-informativo {
            background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%) !important;
            color: white !important;
        }

        .theme-dark .document-type.type-otro {
            background: linear-gradient(135deg, #636e72 0%, #2d3436 100%) !important;
            color: white !important;
        }

        /* Status badges en dark mode - mantener colores originales */
        .theme-dark .status-badge.read {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%) !important;
            color: white !important;
        }

        .theme-dark .status-badge.signed {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%) !important;
            color: white !important;
        }

        .theme-dark .action-btn {
            background: rgba(26, 32, 44, 0.9) !important;
            color: #cbd5e0 !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .action-btn:hover:not(.disabled) {
            color: white !important;
            border-color: #667eea !important;
            background: rgba(45, 55, 72, 0.9) !important;
        }

        .theme-dark .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: 1px solid #667eea !important;
        }

        .theme-dark .action-btn.primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
            border-color: #5a67d8 !important;
        }

        .theme-dark .action-btn.disabled {
            background: rgba(26, 32, 44, 0.5) !important;
            color: #4a5568 !important;
            border-color: #2d3748 !important;
        }

        .theme-dark .pagination-wrapper {
            border-top: 1px solid #4a5568 !important;
        }

        .theme-dark .pagination-info {
            color: #cbd5e0 !important;
        }

        .theme-dark .page-btn {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #cbd5e0 !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .page-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-color: #667eea !important;
        }

        .theme-dark .page-btn:disabled {
            background: rgba(26, 32, 44, 0.5) !important;
            color: #4a5568 !important;
            border-color: #2d3748 !important;
        }

        .theme-dark .page-number {
            color: #cbd5e0 !important;
        }

        .theme-dark .page-number.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .theme-dark .page-number:hover:not(.active) {
            background: rgba(102, 126, 234, 0.3) !important;
            color: white !important;
        }

        /* Tema Light */
        .theme-light .documents-section {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #2d3748 !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .theme-light .documents-section::before {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%) !important;
        }

        .theme-light .documents-title h4 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            -webkit-background-clip: text !important;
            -webkit-text-fill-color: transparent !important;
            background-clip: text !important;
        }

        .theme-light .documents-count {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .theme-light .view-toggle {
            background: rgba(255, 255, 255, 0.8) !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .theme-light .view-btn {
            color: #6b7280 !important;
        }

        .theme-light .view-btn:hover {
            color: #4a5568 !important;
        }

        .theme-light .view-btn.active {
            color: white !important;
        }

        .theme-light .document-card {
            background: rgba(255, 255, 255, 0.9) !important;
            color: #2d3748 !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .theme-light .document-card:hover {
            border-color: #667eea !important;
        }

        .theme-light .document-title {
            color: #2d3748 !important;
        }

        .theme-light .meta-label {
            color: #6b7280 !important;
        }

        .theme-light .file-size,
        .theme-light .uploader,
        .theme-light .upload-date {
            color: #4a5568 !important;
        }

        .theme-light .file-format {
            color: #6b7280 !important;
        }

        .theme-light .action-btn {
            background: rgba(255, 255, 255, 0.8) !important;
            color: #4a5568 !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .theme-light .action-btn:hover:not(.disabled) {
            color: #667eea !important;
            border-color: #667eea !important;
            background: rgba(255, 255, 255, 0.9) !important;
        }

        .theme-light .action-btn.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border: 1px solid #667eea !important;
        }

        .theme-light .action-btn.primary:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
            border-color: #5a67d8 !important;
        }

        .theme-light .action-btn.disabled {
            background: rgba(255, 255, 255, 0.5) !important;
            color: #a0aec0 !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
        }

        .theme-light .pagination-wrapper {
            border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        .theme-light .pagination-info {
            color: #6b7280 !important;
        }

        .theme-light .page-btn {
            background: rgba(255, 255, 255, 0.8) !important;
            color: #4a5568 !important;
            border: 1px solid rgba(255, 255, 255, 0.3) !important;
        }

        .theme-light .page-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-color: #667eea !important;
        }

        .theme-light .page-btn:disabled {
            background: rgba(255, 255, 255, 0.5) !important;
            color: #a0aec0 !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
        }

        .theme-light .page-number {
            color: #6b7280 !important;
        }

        .theme-light .page-number.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }

        .theme-light .page-number:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1) !important;
            color: #667eea !important;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .documents-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .documents-grid.list-view {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .documents-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .documents-grid.list-view {
                grid-template-columns: 1fr;
            }

            .documents-header {
                flex-direction: column;
                gap: 20px;
                align-items: stretch;
            }

            .documents-grid.list-view .document-card {
                flex-direction: column;
                text-align: center;
            }

            .documents-grid.list-view .document-actions {
                width: 100%;
                justify-content: center;
            }

            .pagination-wrapper {
                flex-direction: column;
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .documents-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .document-actions {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }

            .action-btn span {
                display: none;
            }

            .action-btn {
                padding: 12px;
            }
        }

        /* ===== RESPONSIVE PARA VISTAS ===== */
        @media (max-width: 768px) {
            .informativos-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                padding: 15px 0;
            }

            .informativos-grid .informativo-card {
                min-height: 350px;
            }

            .informativos-list .informativo-card {
                flex-direction: column;
                align-items: stretch;
            }

            .informativos-list .card-content {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .informativos-list .file-icon {
                align-self: center;
            }

            .informativos-list .card-footer-actions {
                width: 100%;
                margin-top: 15px;
            }

            .informativos-list .secondary-actions {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                z-index: 9999;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        @media (max-width: 480px) {
            .informativos-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 10px 0;
            }

            .informativos-grid .informativo-card {
                min-height: 320px;
            }

            .informativos-list .secondary-actions {
                grid-template-columns: 1fr;
                gap: 8px;
            }
        }

        /* Botones de acción para lectura y firma */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-read {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-read:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
            color: white;
        }

        .btn-read.read {
            background: #6c757d;
            cursor: not-allowed;
        }

        .btn-sign {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .btn-sign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(253, 203, 110, 0.3);
            color: white;
        }

        .btn-sign.signed {
            background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
            cursor: not-allowed;
        }

        .status-indicators {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            flex-wrap: wrap;
        }

        .status-badge-small {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-read {
            background: rgba(0, 184, 148, 0.1);
            color: #00b894;
            border: 1px solid rgba(0, 184, 148, 0.3);
        }

        .status-signed {
            background: rgba(253, 203, 110, 0.1);
            color: #e17055;
            border: 1px solid rgba(253, 203, 110, 0.3);
        }

        .card-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
        }

        .card-actions {
            margin-top: auto;
            padding-top: 15px;
        }

        .view-toggle .btn.active {
            background: var(--gradient-primary) !important;
            color: white !important;
            border-color: transparent !important;
        }



        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            color: #2d3748;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-soft);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            margin: 20px;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            filter: brightness(0) invert(1);
        }

        .user-info {
            padding: 25px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-soft);
            position: relative;
            overflow: hidden;
        }

        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0.05;
            z-index: 0;
        }

        .user-avatar {
            width: 90px;
            height: 90px;
            border-radius: 50%;
            border: 4px solid rgba(102, 126, 234, 0.2);
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .sidebar-nav {
            padding: 0 20px 20px;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            color: #4a5568;
            padding: 15px 20px;
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 15px;
            font-weight: 500;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before {
            opacity: 0.1;
        }

        .nav-link.active::before {
            opacity: 1;
        }

        .nav-link:hover, .nav-link.active {
            color: white;
            text-decoration: none;
            transform: translateX(5px);
            box-shadow: var(--shadow-soft);
        }

        .nav-link i {
            margin-right: 12px;
            width: 20px;
            position: relative;
            z-index: 1;
            transition: transform 0.3s ease;
        }

        .nav-link:hover i {
            transform: scale(1.1);
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .status-badge {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-disponible { background: #d4edda; color: #155724; }
        .status-colacion { background: #fff3cd; color: #856404; }
        .status-ocupado { background: #f8d7da; color: #721c24; }
        .status-fuera { background: #e2e3e5; color: #383d41; }

        /* ===== TEMA DARK Y LIGHT ===== */

        /* Tema Oscuro */
        .theme-dark {
            color-scheme: dark;
        }

        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .sidebar-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .user-info {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #f7fafc !important;
        }

        .theme-dark .nav-link {
            color: #e2e8f0 !important;
        }

        .theme-dark .nav-link:hover,
        .theme-dark .nav-link.active {
            color: white !important;
            background: rgba(102, 126, 234, 0.2) !important;
        }

        .theme-dark .top-bar {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .informativo-card {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .card-footer-actions {
            background: rgba(26, 32, 44, 0.8) !important;
            border-top: 1px solid #4a5568 !important;
        }

        .theme-dark .action-btn {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #e2e8f0 !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .action-btn:hover {
            background: rgba(102, 126, 234, 0.2) !important;
            color: white !important;
            border-color: #667eea !important;
        }

        .theme-dark .btn-view-document {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .view-toggle-btn {
            color: #e2e8f0 !important;
        }

        .theme-dark .view-toggle-btn.active {
            background: rgba(102, 126, 234, 0.3) !important;
            color: white !important;
        }

        .theme-dark .text-muted {
            color: #a0aec0 !important;
        }

        .theme-dark .file-details {
            color: #a0aec0 !important;
        }

        /* Tema Claro */
        .theme-light {
            color-scheme: light;
        }

        .theme-light .sidebar {
            background: rgba(255, 255, 255, 0.95) !important;
            color: #2d3748 !important;
            border-right: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        .theme-light .user-info {
            background: rgba(255, 255, 255, 0.8) !important;
            color: #2d3748 !important;
        }

        .theme-light .nav-link {
            color: #4a5568 !important;
        }

        .theme-light .top-bar {
            background: rgba(255, 255, 255, 0.9) !important;
            color: #2d3748 !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .theme-light .informativo-card {
            background: rgba(255, 255, 255, 0.9) !important;
            color: #2d3748 !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
        }

        .theme-light .text-muted {
            color: #718096 !important;
        }

        /* ===== TOP BAR STYLES ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* ===== NOTIFICATION STYLES ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            font-size: 24px;
            cursor: pointer;
            position: relative;
            transition: transform 0.3s ease;
        }

        .notification-bell:hover {
            transform: scale(1.1);
        }

        .notification-counter {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9998;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-panel {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .notifications-panel.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .notifications-header {
            background: var(--gradient-primary);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notifications-actions {
            display: flex;
            gap: 10px;
        }

        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .notifications-filters {
            display: flex;
            padding: 15px 20px;
            gap: 10px;
            border-bottom: 1px solid #eee;
        }

        .filter-btn {
            background: #f8f9fa;
            border: none;
            padding: 8px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .notifications-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 20px;
        }

        .notification-item {
            display: flex;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.05);
            border-left: 4px solid var(--primary-color);
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .notification-icon {
            font-size: 24px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f8f9fa;
        }

        .empty-notifications {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        /* Animaciones */
        .slide-in-left {
            animation: slideInLeft 0.6s ease-out forwards;
            opacity: 0;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .fade-in {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* ===== BOTÓN DE SUBIR MODERNO ===== */
        .btn-upload-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            padding: 12px 20px;
            color: white;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 600;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn-upload-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-upload-modern:hover::before {
            left: 100%;
        }

        .btn-upload-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-upload-modern .upload-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .btn-upload-modern .upload-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .btn-upload-modern .upload-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
        }

        .btn-upload-modern .upload-subtitle {
            font-size: 12px;
            opacity: 0.8;
            line-height: 1;
        }

        .btn-upload-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 20px;
            padding: 16px 32px;
            color: white;
            display: flex;
            align-items: center;
            gap: 16px;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-upload-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .btn-upload-primary .upload-icon-large {
            width: 50px;
            height: 50px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        /* ===== MODAL MODERNO ===== */
        .modern-modal {
            border: none;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border: none;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .modal-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .modal-title-section {
            flex: 1;
        }

        .modal-title-modern {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            line-height: 1.2;
        }

        .modal-subtitle {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }

        .btn-close-modern {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 12px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .btn-close-modern:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body-modern {
            padding: 40px;
        }

        /* ===== ZONA DE ARRASTRE ===== */
        .file-drop-zone {
            border: 3px dashed #e2e8f0;
            border-radius: 20px;
            padding: 40px 20px;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            margin-bottom: 30px;
        }

        .file-drop-zone:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
            transform: translateY(-2px);
        }

        .file-drop-zone.dragover {
            border-color: #667eea;
            background: linear-gradient(135deg, #e6f0ff 0%, #dce7ff 100%);
            transform: scale(1.02);
        }

        .drop-zone-content {
            pointer-events: none;
        }

        .drop-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 32px;
            color: white;
        }

        .file-drop-zone h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .file-drop-zone p {
            color: #718096;
            margin-bottom: 20px;
        }

        .file-input-hidden {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            pointer-events: auto;
        }

        .file-formats {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .format-badge {
            background: #667eea;
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .file-limit {
            color: #a0aec0;
            font-size: 12px;
        }

        /* ===== PREVIEW DE ARCHIVO ===== */
        .file-preview {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .file-icon {
            width: 50px;
            height: 50px;
            background: #667eea;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            display: block;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .file-size {
            display: block;
            font-size: 14px;
            color: #718096;
        }

        .btn-remove-file {
            background: #fed7d7;
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e53e3e;
            transition: all 0.3s ease;
        }

        .btn-remove-file:hover {
            background: #feb2b2;
            transform: scale(1.1);
        }

        /* ===== FORMULARIO MODERNO ===== */
        .form-section {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
        }

        .form-group-modern {
            margin-bottom: 25px;
        }

        .form-label-modern {
            display: flex;
            align-items: center;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-control-modern,
        .form-select-modern {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control-modern:focus,
        .form-select-modern:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            outline: none;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        /* ===== FOOTER DEL MODAL ===== */
        .modal-footer-modern {
            padding: 30px 40px;
            background: #f8fafc;
            border: none;
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .btn-cancel-modern {
            background: #e2e8f0;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: #4a5568;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-cancel-modern:hover {
            background: #cbd5e0;
            transform: translateY(-1px);
        }

        .btn-upload-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-upload-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* ===== SELECTOR DE MÉTODO ===== */
        .upload-method-selector {
            margin-bottom: 30px;
        }

        .method-tabs {
            display: flex;
            background: #f1f5f9;
            border-radius: 16px;
            padding: 4px;
            gap: 4px;
        }

        .method-tab {
            flex: 1;
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .method-tab.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .method-tab:hover:not(.active) {
            color: #475569;
            background: rgba(255, 255, 255, 0.5);
        }

        .upload-method {
            transition: all 0.3s ease;
        }

        /* ===== GOOGLE DRIVE STYLES ===== */
        .drive-selector {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 40px 30px;
            text-align: center;
        }

        .drive-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .drive-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 75%, #ea4335 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
        }

        .drive-info h5 {
            color: #2d3748;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .drive-info p {
            color: #718096;
            margin: 0;
        }

        .btn-drive-auth,
        .btn-drive-picker {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            font-size: 16px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(66, 133, 244, 0.3);
            cursor: pointer;
        }

        .btn-drive-auth:hover,
        .btn-drive-picker:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(66, 133, 244, 0.4);
        }

        .drive-note {
            display: block;
            color: #a0aec0;
            font-size: 12px;
            margin-top: 10px;
        }

        .drive-status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 12px;
            font-weight: 500;
        }

        .drive-status.success {
            background: #f0fff4;
            color: #38a169;
            border: 1px solid #9ae6b4;
        }

        .drive-status.error {
            background: #fed7d7;
            color: #e53e3e;
            border: 1px solid #feb2b2;
        }

        .drive-file-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-top: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .drive-file-icon {
            width: 50px;
            height: 50px;
            background: #4285f4;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .drive-file-details {
            flex: 1;
        }

        .drive-file-name {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .drive-file-meta {
            font-size: 14px;
            color: #718096;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .modal-header-modern {
                padding: 20px;
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .modal-body-modern {
                padding: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .btn-upload-modern .upload-text {
                display: none;
            }

            .btn-upload-modern {
                padding: 12px;
            }

            .drive-header {
                flex-direction: column;
                gap: 15px;
            }

            .method-tabs {
                flex-direction: column;
            }
        }

        /* ===== BOTONES DE VISTA MODERNOS ===== */
        .view-toggle-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        .view-toggle-group {
            background: #f1f5f9;
            border-radius: 16px;
            padding: 4px;
            display: flex;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .view-toggle-btn {
            background: transparent;
            border: none;
            border-radius: 12px;
            padding: 10px 16px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #64748b;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .view-toggle-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
        }

        .view-toggle-btn.active {
            background: white;
            color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }

        .view-toggle-btn.active::before {
            opacity: 0.1;
        }

        .view-toggle-btn:hover:not(.active) {
            color: #475569;
            background: rgba(255, 255, 255, 0.7);
            transform: translateY(-1px);
        }

        .view-toggle-btn i {
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .view-toggle-btn span {
            position: relative;
            z-index: 1;
        }

        @media (max-width: 768px) {
            .view-toggle-btn span {
                display: none;
            }

            .view-toggle-btn {
                padding: 10px 12px;
            }
        }

        /* ===== TARJETAS MEJORADAS ===== */
        .informativo-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            border: 1px solid #f1f5f9;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .informativo-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: #e2e8f0;
        }

        .card-content {
            padding: 25px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .file-info-section {
            margin-top: auto;
            padding-top: 15px;
            border-top: 1px solid #f1f5f9;
        }

        .file-details {
            color: #64748b;
            font-size: 13px;
            font-weight: 500;
        }

        /* ===== FOOTER DE TARJETA ===== */
        .card-footer-actions {
            background: #f8fafc;
            padding: 20px 25px;
            border-top: 1px solid #f1f5f9;
        }

        .primary-action {
            margin-bottom: 15px;
        }

        .btn-view-document {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-view-document:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }

        .secondary-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }

        .action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 10px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            text-align: center;
        }

        .action-btn:hover:not(:disabled) {
            border-color: #cbd5e0;
            background: #f8fafc;
            transform: translateY(-1px);
        }

        .action-btn i {
            font-size: 16px;
        }

        .action-btn span {
            font-size: 11px;
            line-height: 1;
        }

        /* Estados específicos de botones */
        .btn-read:not(.read):hover {
            border-color: #3b82f6;
            color: #3b82f6;
        }

        .btn-read.read {
            background: #dbeafe;
            border-color: #3b82f6;
            color: #1d4ed8;
        }

        .btn-sign:not(.signed):hover {
            border-color: #10b981;
            color: #10b981;
        }

        .btn-sign.signed {
            background: #d1fae5;
            border-color: #10b981;
            color: #047857;
        }

        .btn-download:hover {
            border-color: #f59e0b;
            color: #f59e0b;
        }

        .btn-delete:hover {
            border-color: #ef4444;
            color: #ef4444;
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            pointer-events: none;
        }

        .action-btn:not(:disabled) {
            cursor: pointer;
            pointer-events: auto;
        }

        .action-btn:not(:disabled):hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }

        .action-btn:not(:disabled):active {
            transform: translateY(0);
        }

        /* ===== BADGES DE ESTADO ===== */
        .status-indicators {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            justify-content: center;
        }

        .status-badge-small {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .status-read {
            background: #dbeafe;
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .status-signed {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #86efac;
        }

        /* ===== BADGES DE TIPO ===== */
        .tipo-badge {
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            white-space: nowrap;
        }

        .tipo-briefing {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            color: #1d4ed8;
            border: 1px solid #93c5fd;
        }

        .tipo-boletin {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            color: #166534;
            border: 1px solid #86efac;
        }

        .tipo-circular {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            border: 1px solid #fbbf24;
        }

        .tipo-otro {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            color: #7c3aed;
            border: 1px solid #c4b5fd;
        }

        /* ===== ICONOS DE ARCHIVO ===== */
        .file-icon {
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .file-pdf {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .file-doc {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
        }

        .file-img {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .file-icon i {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        /* ===== HEADER DE INFORMATIVO ===== */
        .informativo-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            gap: 15px;
        }

        .informativo-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1.3;
            margin-bottom: 8px;
        }

        .informativo-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: 14px;
            color: #64748b;
        }

        .informativo-meta span {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* ===== RESPONSIVE PARA TARJETAS ===== */
        @media (max-width: 768px) {
            .card-content {
                padding: 20px;
            }

            .card-footer-actions {
                padding: 15px 20px;
            }

            .secondary-actions {
                grid-template-columns: repeat(2, 1fr);
            }

            .action-btn {
                padding: 8px 6px;
            }

            .action-btn span {
                font-size: 10px;
            }
        }
    </style>
</head>
<body>


    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1" style="color: #2d3748; font-weight: 600; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small style="color: #718096; position: relative; z-index: 1;"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>" style="position: relative; z-index: 1;"><?php echo $agente['estado']; ?></span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    Lobby
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    CREC
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link active">
                    <i class="fas fa-file-pdf"></i>
                    Informativos
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    Mi Perfil
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    Buscar Agentes
                </a>
            </div>
            <?php if (hasPermission('all_permissions')): ?>
            <div class="nav-item">
                <a href="admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Administración
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    Cerrar Sesión
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar fade-in">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-weight: 700;">
                        <i class="fas fa-file-pdf me-2"></i>
                        Informativos
                    </h2>
                    <p class="text-muted mb-0" style="font-weight: 500;">
                        <i class="fas fa-calendar me-2"></i>
                        <?php
                        if (function_exists('formatearFechaEspanol')) {
                            echo formatearFechaEspanol(date('Y-m-d'));
                        } else {
                            echo date('d/m/Y');
                        }
                        ?>
                        <span class="ms-3">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('H:i'); ?>
                        </span>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn-upload-modern" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <div class="upload-text">
                            <span class="upload-title">Subir Documento</span>
                            <small class="upload-subtitle">Nuevo informativo</small>
                        </div>
                    </button>
                <?php endif; ?>

                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <?php
                        $unread_count = 0;
                        if (!empty($notificaciones)) {
                            foreach ($notificaciones as $notif) {
                                if (!$notif['leida']) {
                                    $unread_count++;
                                }
                            }
                        }
                        if ($unread_count > 0): ?>
                            <span class="notification-counter"><?php echo $unread_count; ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-cards">
            <?php
            // Calcular estadísticas por tipo
            $stats_tipos = [
                'briefing' => ['count' => 0, 'icon' => 'fa-clipboard-list', 'label' => 'Briefings'],
                'boletin' => ['count' => 0, 'icon' => 'fa-newspaper', 'label' => 'Boletines'],
                'circular' => ['count' => 0, 'icon' => 'fa-file-circle-check', 'label' => 'Circulares'],
                'otro' => ['count' => 0, 'icon' => 'fa-file-alt', 'label' => 'Otros']
            ];

            foreach ($informativos as $informativo) {
                if (isset($stats_tipos[$informativo['tipo']])) {
                    $stats_tipos[$informativo['tipo']]['count']++;
                }
            }
            ?>

            <?php foreach ($stats_tipos as $tipo => $data): ?>
                <div class="stat-card fade-in">
                    <div class="stat-icon <?php echo $tipo; ?>">
                        <i class="fas <?php echo $data['icon']; ?>"></i>
                    </div>
                    <div class="stat-number"><?php echo $data['count']; ?></div>
                    <div class="stat-label"><?php echo $data['label']; ?></div>
                </div>
            <?php endforeach; ?>
        </div>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-custom" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo $error; ?>
            </div>
        <?php endif; ?>

        <?php if ($message): ?>
            <div class="alert alert-success alert-custom" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <!-- Filtros -->
        <div class="filters-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-filter me-2"></i>
                    Filtros de Búsqueda
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-4">
                    <div class="col-md-3">
                        <label for="tipo" class="form-label fw-semibold">
                            <i class="fas fa-tag me-2 text-primary"></i>
                            Tipo de Documento
                        </label>
                        <select class="form-select" id="tipo" name="tipo">
                            <option value="">📄 Todos los tipos</option>
                            <option value="briefing" <?php echo $tipo_filtro === 'briefing' ? 'selected' : ''; ?>>
                                📋 Briefing
                            </option>
                            <option value="boletin" <?php echo $tipo_filtro === 'boletin' ? 'selected' : ''; ?>>
                                📰 Boletín
                            </option>
                            <option value="circular" <?php echo $tipo_filtro === 'circular' ? 'selected' : ''; ?>>
                                📃 Circular
                            </option>
                            <option value="otro" <?php echo $tipo_filtro === 'otro' ? 'selected' : ''; ?>>
                                📝 Otro
                            </option>
                        </select>
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_desde" class="form-label fw-semibold">
                            <i class="fas fa-calendar-alt me-2 text-success"></i>
                            Fecha Desde
                        </label>
                        <input type="date" class="form-control" id="fecha_desde" name="fecha_desde" value="<?php echo $fecha_desde; ?>">
                    </div>

                    <div class="col-md-3">
                        <label for="fecha_hasta" class="form-label fw-semibold">
                            <i class="fas fa-calendar-check me-2 text-warning"></i>
                            Fecha Hasta
                        </label>
                        <input type="date" class="form-control" id="fecha_hasta" name="fecha_hasta" value="<?php echo $fecha_hasta; ?>">
                    </div>

                    <div class="col-md-3 d-flex align-items-end">
                        <div class="d-flex gap-2 w-100">
                            <button type="submit" class="btn btn-primary flex-grow-1">
                                <i class="fas fa-search me-2"></i>
                                Filtrar
                            </button>
                            <a href="informativos.php" class="btn btn-outline-secondary" title="Limpiar filtros">
                                <i class="fas fa-undo"></i>
                            </a>
                        </div>
                    </div>
                </form>

                <?php if ($tipo_filtro || $fecha_desde || $fecha_hasta): ?>
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex align-items-center gap-2">
                            <span class="text-muted">
                                <i class="fas fa-filter me-1"></i>
                                Filtros activos:
                            </span>
                            <?php if ($tipo_filtro): ?>
                                <span class="badge bg-primary">
                                    Tipo: <?php echo ucfirst($tipo_filtro); ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_desde): ?>
                                <span class="badge bg-success">
                                    Desde: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_desde);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_desde));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                            <?php if ($fecha_hasta): ?>
                                <span class="badge bg-warning">
                                    Hasta: <?php
                                    if (function_exists('formatDate')) {
                                        echo formatDate($fecha_hasta);
                                    } else {
                                        echo date('d/m/Y', strtotime($fecha_hasta));
                                    }
                                    ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Lista de Informativos -->
        <?php if (empty($informativos)): ?>
            <div class="empty-state">
                <i class="fas fa-file-pdf fa-5x mb-4"></i>
                <h3 class="mb-3">No hay informativos disponibles</h3>
                <p class="text-muted mb-4">Los nuevos documentos aparecerán aquí cuando sean publicados por los supervisores</p>
                <?php if (hasPermission('manage_admin')): ?>
                    <button class="btn-upload-primary" data-bs-toggle="modal" data-bs-target="#subirArchivoModal">
                        <div class="upload-icon-large">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <span>Subir Primer Documento</span>
                    </button>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Sección de Documentos Disponibles -->
            <div class="documents-section">
                <div class="documents-header">
                    <div class="documents-title">
                        <h4>
                            <i class="fas fa-folder-open me-2"></i>
                            Documentos Disponibles
                        </h4>
                        <span class="documents-count" id="documentsCount"><?php echo count($informativos); ?> documentos</span>
                    </div>

                    <div class="view-controls">
                        <div class="view-toggle">
                            <button class="view-btn active" data-view="grid" id="gridViewBtn">
                                <i class="fas fa-th-large"></i>
                                <span>Cuadrícula</span>
                            </button>
                            <button class="view-btn" data-view="list" id="listViewBtn">
                                <i class="fas fa-list"></i>
                                <span>Lista</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Contenedor de documentos -->
                <div class="documents-container" id="documentsContainer">
                    <div class="documents-grid" id="documentsGrid">
                        <?php foreach ($informativos as $index => $informativo):
                            // Verificar si el agente ya leyó o firmó este documento
                            try {
                                $read_query = "SELECT * FROM document_reads WHERE agente_id = ? AND documento_id = ?";
                                $read_stmt = $conn->prepare($read_query);
                                $read_stmt->execute([$agente['id'], $informativo['id']]);
                                $is_read = $read_stmt->fetch() ? true : false;
                            } catch (Exception $e) {
                                $is_read = false;
                            }

                            try {
                                $sign_query = "SELECT * FROM document_signatures WHERE agente_id = ? AND documento_id = ?";
                                $sign_stmt = $conn->prepare($sign_query);
                                $sign_stmt->execute([$agente['id'], $informativo['id']]);
                                $is_signed = $sign_stmt->fetch() ? true : false;
                            } catch (Exception $e) {
                                $is_signed = false;
                            }

                            // Información del archivo
                            $file_path = $informativo['archivo'];
                            $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
                            $file_size = file_exists($file_path) ? filesize($file_path) : 0;

                            // Formatear tamaño
                            if ($file_size >= 1048576) {
                                $formatted_size = round($file_size / 1048576, 1) . ' MB';
                            } elseif ($file_size >= 1024) {
                                $formatted_size = round($file_size / 1024, 1) . ' KB';
                            } else {
                                $formatted_size = $file_size . ' bytes';
                            }

                            // Icono según extensión
                            $file_icon = 'fas fa-file';
                            $file_color = 'file-default';

                            switch ($file_extension) {
                                case 'pdf':
                                    $file_icon = 'fas fa-file-pdf';
                                    $file_color = 'file-pdf';
                                    break;
                                case 'doc':
                                case 'docx':
                                    $file_icon = 'fas fa-file-word';
                                    $file_color = 'file-word';
                                    break;
                                case 'xls':
                                case 'xlsx':
                                    $file_icon = 'fas fa-file-excel';
                                    $file_color = 'file-excel';
                                    break;
                                case 'jpg':
                                case 'jpeg':
                                case 'png':
                                case 'gif':
                                    $file_icon = 'fas fa-file-image';
                                    $file_color = 'file-image';
                                    break;
                            }
                        ?>
                        <div class="document-card" data-id="<?php echo $informativo['id']; ?>">
                            <div class="document-header">
                                <div class="file-icon-container">
                                    <div class="file-icon <?php echo $file_color; ?>">
                                        <i class="<?php echo $file_icon; ?>"></i>
                                    </div>
                                    <div class="file-format"><?php echo strtoupper($file_extension); ?></div>
                                </div>

                                <div class="document-status">
                                    <?php if ($is_read): ?>
                                        <span class="status-badge read">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                    <?php endif; ?>

                                    <?php if ($is_signed): ?>
                                        <span class="status-badge signed">
                                            <i class="fas fa-signature"></i>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="document-content">
                                <h5 class="document-title"><?php echo htmlspecialchars($informativo['titulo']); ?></h5>

                                <div class="document-meta">
                                    <div class="meta-item">
                                        <span class="meta-label">Tipo:</span>
                                        <span class="document-type type-<?php echo $informativo['tipo']; ?>">
                                            <?php echo strtoupper($informativo['tipo']); ?>
                                        </span>
                                    </div>

                                    <div class="meta-item">
                                        <span class="meta-label">Tamaño:</span>
                                        <span class="file-size"><?php echo $formatted_size; ?></span>
                                    </div>

                                    <div class="meta-item">
                                        <span class="meta-label">Subido por:</span>
                                        <span class="uploader"><?php echo htmlspecialchars($informativo['subido_por_nombre']); ?></span>
                                    </div>

                                    <div class="meta-item">
                                        <span class="meta-label">Fecha:</span>
                                        <span class="upload-date"><?php echo date('d/m/Y', strtotime($informativo['fecha_publicacion'])); ?></span>
                                    </div>
                                </div>
                            </div>

                            <div class="document-actions">
                                <button class="action-btn primary"
                                        onclick="viewDocument(<?php echo $informativo['id']; ?>, '<?php echo $file_path; ?>')"
                                        title="Ver documento">
                                    <i class="fas fa-eye"></i>
                                    <span>Ver</span>
                                </button>

                                <button class="action-btn <?php echo $is_read ? 'disabled' : ''; ?>"
                                        onclick="markAsRead(<?php echo $informativo['id']; ?>)"
                                        <?php echo $is_read ? 'disabled' : ''; ?>
                                        title="<?php echo $is_read ? 'Ya leído' : 'Marcar como leído'; ?>">
                                    <i class="fas fa-check"></i>
                                    <span>Leído</span>
                                </button>

                                <button class="action-btn <?php echo $is_signed ? 'disabled' : ''; ?>"
                                        onclick="signDocument(<?php echo $informativo['id']; ?>)"
                                        <?php echo $is_signed ? 'disabled' : ''; ?>
                                        title="<?php echo $is_signed ? 'Ya firmado' : 'Firmar documento'; ?>">
                                    <i class="fas fa-signature"></i>
                                    <span>Firmar</span>
                                </button>

                                <button class="action-btn"
                                        onclick="downloadDocument('<?php echo $file_path; ?>', '<?php echo $informativo['titulo']; ?>')"
                                        title="Descargar documento">
                                    <i class="fas fa-download"></i>
                                    <span>Descargar</span>
                                </button>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Paginación -->
                <div class="pagination-wrapper" id="paginationWrapper">
                    <div class="pagination-info">
                        <span>Mostrando <span id="showingStart">1</span>-<span id="showingEnd">8</span> de <span id="totalDocs"><?php echo count($informativos); ?></span> documentos</span>
                    </div>

                    <div class="pagination-controls">
                        <button class="page-btn" id="prevBtn" onclick="changePage(-1)" disabled>
                            <i class="fas fa-chevron-left"></i>
                        </button>

                        <div class="page-numbers" id="pageNumbers">
                            <!-- Se generan dinámicamente -->
                        </div>

                        <button class="page-btn" id="nextBtn" onclick="changePage(1)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Modal Subir Archivo Moderno -->
    <?php if (hasPermission('manage_admin')): ?>
    <div class="modal fade" id="subirArchivoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content modern-modal">
                <div class="modal-header-modern">
                    <div class="modal-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="modal-title-section">
                        <h4 class="modal-title-modern">Subir Nuevo Informativo</h4>
                        <p class="modal-subtitle">Comparte documentos importantes con tu equipo</p>
                    </div>
                    <button type="button" class="btn-close-modern" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form method="POST" enctype="multipart/form-data" class="upload-form">
                    <input type="hidden" name="action" value="subir_archivo">
                    <input type="hidden" name="upload_method" id="uploadMethod" value="local">
                    <input type="hidden" name="drive_file_id" id="driveFileId" value="">
                    <input type="hidden" name="drive_file_name" id="driveFileName" value="">
                    <input type="hidden" name="drive_file_url" id="driveFileUrl" value="">

                    <div class="modal-body-modern">
                        <!-- Selector de método de subida -->
                        <div class="upload-method-selector">
                            <div class="method-tabs">
                                <button type="button" class="method-tab active" data-method="local" onclick="switchUploadMethod('local')">
                                    <i class="fas fa-upload me-2"></i>
                                    Archivo Local
                                </button>
                                <button type="button" class="method-tab" data-method="drive" onclick="switchUploadMethod('drive')">
                                    <i class="fab fa-google-drive me-2"></i>
                                    Google Drive
                                </button>
                            </div>
                        </div>

                        <!-- Método 1: Subida local -->
                        <div class="upload-method" id="localUpload">
                            <!-- Zona de arrastre de archivos -->
                            <div class="file-drop-zone" id="fileDropZone">
                                <div class="drop-zone-content">
                                    <div class="drop-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h5>Arrastra tu archivo aquí</h5>
                                    <p>o haz clic para seleccionar</p>
                                    <input type="file" class="file-input-hidden" id="archivo" name="archivo" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                    <div class="file-formats">
                                        <span class="format-badge">PDF</span>
                                        <span class="format-badge">DOC</span>
                                        <span class="format-badge">DOCX</span>
                                        <span class="format-badge">JPG</span>
                                        <span class="format-badge">PNG</span>
                                    </div>
                                    <small class="file-limit">Tamaño máximo: 10MB</small>
                                </div>
                            </div>
                        </div>

                        <!-- Método 2: Google Drive -->
                        <div class="upload-method" id="driveUpload" style="display: none;">
                            <div class="drive-selector">
                                <div class="drive-header">
                                    <div class="drive-icon">
                                        <i class="fab fa-google-drive"></i>
                                    </div>
                                    <div class="drive-info">
                                        <h5>Seleccionar desde Google Drive</h5>
                                        <p>Elige un documento directamente desde tu Google Drive</p>
                                    </div>
                                </div>

                                <div class="drive-auth" id="driveAuth">
                                    <button type="button" class="btn-drive-auth" onclick="authenticateGoogleDrive()">
                                        <i class="fab fa-google me-2"></i>
                                        Conectar con Google Drive
                                    </button>
                                    <small class="drive-note">Se abrirá una ventana para autorizar el acceso a tu Google Drive</small>
                                </div>

                                <div class="drive-picker" id="drivePicker" style="display: none;">
                                    <button type="button" class="btn-drive-picker" onclick="openDrivePicker()">
                                        <i class="fab fa-google-drive me-2"></i>
                                        Seleccionar Archivo de Drive
                                    </button>
                                    <div class="drive-status" id="driveStatus"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Preview del archivo seleccionado -->
                        <div class="file-preview" id="filePreview" style="display: none;">
                            <div class="file-info">
                                <div class="file-icon">
                                    <i class="fas fa-file"></i>
                                </div>
                                <div class="file-details">
                                    <span class="file-name"></span>
                                    <span class="file-size"></span>
                                </div>
                                <button type="button" class="btn-remove-file" onclick="removeFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Formulario de detalles -->
                        <div class="form-section">
                            <div class="form-group-modern">
                                <label for="titulo" class="form-label-modern">
                                    <i class="fas fa-heading me-2"></i>
                                    Título del Documento *
                                </label>
                                <input type="text" class="form-control-modern" id="titulo" name="titulo" placeholder="Ej: Briefing Operacional Enero 2024" required>
                            </div>

                            <div class="form-group-modern">
                                <label for="descripcion" class="form-label-modern">
                                    <i class="fas fa-align-left me-2"></i>
                                    Descripción
                                </label>
                                <textarea class="form-control-modern" id="descripcion" name="descripcion" rows="3" placeholder="Describe brevemente el contenido del documento..."></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group-modern">
                                    <label for="tipo" class="form-label-modern">
                                        <i class="fas fa-tag me-2"></i>
                                        Tipo de Documento *
                                    </label>
                                    <select class="form-select-modern" id="tipo" name="tipo" required>
                                        <option value="">Seleccionar tipo</option>
                                        <option value="briefing">📋 Briefing</option>
                                        <option value="boletin">📰 Boletín</option>
                                        <option value="circular">📄 Circular</option>
                                        <option value="otro">📁 Otro</option>
                                    </select>
                                </div>

                                <div class="form-group-modern">
                                    <label for="fecha_publicacion" class="form-label-modern">
                                        <i class="fas fa-calendar me-2"></i>
                                        Fecha de Publicación *
                                    </label>
                                    <input type="date" class="form-control-modern" id="fecha_publicacion" name="fecha_publicacion" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer-modern">
                        <button type="button" class="btn-cancel-modern" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn-upload-submit">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            Subir Documento
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Overlay para cerrar notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Filtros de categorías -->
        <div class="notifications-filters">
            <button class="filter-btn active" data-filter="all">
                📋 Todas
            </button>
            <button class="filter-btn" data-filter="operacional">
                ✈️ Operacional
            </button>
            <button class="filter-btn" data-filter="social">
                👥 Social
            </button>
            <button class="filter-btn" data-filter="urgente">
                🚨 Urgente
            </button>
            <button class="filter-btn" data-filter="informativa">
                ℹ️ Informativa
            </button>
            <button class="filter-btn" data-filter="general">
                ⚙️ General
            </button>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">�</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativa' => 'ℹ️',
                                default => '⚙️'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <div class="notification-header">
                                <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                                <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                            </div>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>

                            <?php if (isset($notif['categoria']) && $notif['categoria'] == 'social' && strpos($notif['titulo'], 'solicitud de amistad') !== false): ?>
                                <div class="notification-actions">
                                    <button class="btn-notification-small btn-accept" onclick="acceptFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✓ Aceptar
                                    </button>
                                    <button class="btn-notification-small btn-reject" onclick="rejectFriendRequest(<?php echo $notif['id']; ?>)">
                                        ✗ Rechazar
                                    </button>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>


        // Funciones de notificaciones
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            if (panel.classList.contains('show')) {
                closeNotifications();
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');
            }
        }

        function closeNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            panel.classList.remove('show');
            overlay.classList.remove('show');
        }

        function setupNotificationFilters() {
            const filterButtons = document.querySelectorAll('.filter-btn');
            const notificationItems = document.querySelectorAll('.notification-item');

            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remover clase active de todos los botones
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // Agregar clase active al botón clickeado
                    this.classList.add('active');

                    const filter = this.dataset.filter;

                    // Filtrar notificaciones
                    notificationItems.forEach((item, index) => {
                        const category = item.dataset.category;
                        const shouldShow = filter === 'all' || category === filter;

                        if (shouldShow) {
                            item.style.display = 'flex';
                            item.style.animation = `fadeInNotification 0.5s ease-out ${index * 0.05}s`;
                        } else {
                            item.style.display = 'none';
                        }
                    });
                });
            });
        }

        function setupNotificationClicks() {
            const notificationItems = document.querySelectorAll('.notification-item');

            notificationItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // No procesar si se hizo clic en un botón
                    if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
                        return;
                    }

                    // Marcar como leída
                    markNotificationAsRead(this.dataset.id);
                    this.classList.remove('unread');
                    this.classList.add('read');
                    updateNotificationCounter();
                });
            });
        }

        function markAllAsRead() {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                item.classList.add('read');
                markNotificationAsRead(item.dataset.id);
            });
            updateNotificationCounter();
        }

        function refreshNotifications() {
            location.reload();
        }

        function markNotificationAsRead(notificationId) {
            fetch('ajax/mark_notification_read.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ notification_id: notificationId })
            });
        }

        function updateNotificationCounter() {
            const count = document.querySelectorAll('.notification-item.unread').length;
            const counter = document.querySelector('.notification-counter');
            if (counter) {
                if (count > 0) {
                    counter.textContent = count;
                    counter.style.display = 'flex';
                } else {
                    counter.style.display = 'none';
                }
            }
        }

        // Variables globales para paginación
        let currentPage = 1;
        let itemsPerPage = 6; // Grid: 6 items (2 columnas x 3 filas), List: 4 items (2 columnas x 2 filas)
        let totalItems = 0;
        let allDocuments = [];
        let currentView = 'grid';

        // Inicializar sistema
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 DOM Content Loaded - Inicializando sistema...');

            // Configurar vista inicial
            const savedView = localStorage.getItem('informativos-view') || 'grid';
            currentView = savedView;

            // Inicializar paginación después de que el DOM esté listo
            setTimeout(() => {
                initializePagination();
            }, 500);

            toggleView(savedView);

            // Configurar notificaciones
            setupNotificationFilters();
            setupNotificationClicks();

            // Configurar event listeners para botones de acción
            setupActionButtons();

            console.log('✅ Sistema inicializado correctamente');

            // Inicializar nueva sección de documentos
            initializeDocumentsSection();
        });

        // ===== NUEVA FUNCIONALIDAD DE DOCUMENTOS =====

        let currentDocumentPage = 1;
        let documentsPerPage = 8; // 2 filas x 4 columnas en grid, 2 columnas x 4 filas en lista
        let currentDocumentView = 'grid';
        let allDocumentCards = [];

        function initializeDocumentsSection() {
            console.log('📄 Inicializando sección de documentos...');

            // Obtener todas las tarjetas de documentos
            const container = document.getElementById('documentsGrid');
            if (container) {
                allDocumentCards = Array.from(container.children);
                console.log(`📊 Total documentos encontrados: ${allDocumentCards.length}`);

                // Configurar vista inicial
                const savedView = localStorage.getItem('documents-view') || 'grid';
                setDocumentView(savedView);

                // Configurar paginación
                updateDocumentPagination();
                showDocumentPage(1);

                // Event listeners para botones de vista
                document.getElementById('gridViewBtn')?.addEventListener('click', () => setDocumentView('grid'));
                document.getElementById('listViewBtn')?.addEventListener('click', () => setDocumentView('list'));
            }
        }

        function setDocumentView(view) {
            console.log(`🔄 Cambiando vista a: ${view}`);

            const container = document.getElementById('documentsGrid');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            if (!container || !gridBtn || !listBtn) return;

            // Actualizar clases
            container.classList.remove('list-view');
            gridBtn.classList.remove('active');
            listBtn.classList.remove('active');

            if (view === 'list') {
                container.classList.add('list-view');
                listBtn.classList.add('active');
                documentsPerPage = 8; // 2 columnas x 4 filas
            } else {
                gridBtn.classList.add('active');
                documentsPerPage = 8; // 2 filas x 4 columnas
            }

            currentDocumentView = view;
            localStorage.setItem('documents-view', view);

            // Actualizar paginación
            updateDocumentPagination();
            showDocumentPage(1);
        }

        function showDocumentPage(page) {
            console.log(`📄 Mostrando página ${page} de documentos`);

            const startIndex = (page - 1) * documentsPerPage;
            const endIndex = startIndex + documentsPerPage;

            // Mostrar/ocultar documentos
            allDocumentCards.forEach((card, index) => {
                if (index >= startIndex && index < endIndex) {
                    card.style.display = '';
                    card.style.animation = `fadeInUp 0.6s ease-out ${(index - startIndex) * 0.1}s`;
                } else {
                    card.style.display = 'none';
                }
            });

            currentDocumentPage = page;
            updateDocumentPagination();
            updateDocumentInfo();
        }

        function updateDocumentPagination() {
            const totalPages = Math.ceil(allDocumentCards.length / documentsPerPage);
            const pageNumbers = document.getElementById('pageNumbers');
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            if (!pageNumbers || !prevBtn || !nextBtn) return;

            // Actualizar botones prev/next
            prevBtn.disabled = currentDocumentPage === 1;
            nextBtn.disabled = currentDocumentPage === totalPages;

            // Generar números de página
            pageNumbers.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `page-number ${i === currentDocumentPage ? 'active' : ''}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => showDocumentPage(i);
                pageNumbers.appendChild(pageBtn);
            }
        }

        function updateDocumentInfo() {
            const startIndex = (currentDocumentPage - 1) * documentsPerPage;
            const endIndex = Math.min(startIndex + documentsPerPage, allDocumentCards.length);

            const showingStart = document.getElementById('showingStart');
            const showingEnd = document.getElementById('showingEnd');
            const totalDocs = document.getElementById('totalDocs');

            if (showingStart) showingStart.textContent = startIndex + 1;
            if (showingEnd) showingEnd.textContent = endIndex;
            if (totalDocs) totalDocs.textContent = allDocumentCards.length;
        }

        function changePage(direction) {
            const totalPages = Math.ceil(allDocumentCards.length / documentsPerPage);
            const newPage = currentDocumentPage + direction;

            if (newPage >= 1 && newPage <= totalPages) {
                showDocumentPage(newPage);
            }
        }

        // Funciones para acciones de documentos
        function viewDocument(documentId, filePath) {
            console.log(`👁️ Viendo documento ${documentId}: ${filePath}`);

            // Registrar que el documento fue visto
            fetch('api/mark_document_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    documento_id: documentId,
                    action: 'view'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Documento marcado como visto');
                    // Actualizar UI para mostrar que fue visto
                    updateDocumentStatus(documentId, 'read');
                }
            })
            .catch(error => {
                console.error('❌ Error al marcar documento como visto:', error);
            });

            // Abrir documento en nueva pestaña
            window.open(filePath, '_blank');
        }

        function markAsRead(documentId) {
            console.log(`📖 Marcando documento ${documentId} como leído`);

            fetch('api/mark_document_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    documento_id: documentId,
                    action: 'read'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Documento marcado como leído');
                    updateDocumentStatus(documentId, 'read');
                    showNotification('Documento marcado como leído', 'success');
                } else {
                    showNotification('Error al marcar documento como leído', 'error');
                }
            })
            .catch(error => {
                console.error('❌ Error:', error);
                showNotification('Error de conexión', 'error');
            });
        }

        function signDocument(documentId) {
            console.log(`✍️ Firmando documento ${documentId}`);

            // Confirmar acción
            if (!confirm('¿Confirmas que has leído y comprendido este documento?')) {
                return;
            }

            fetch('api/sign_document.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    documento_id: documentId,
                    action: 'sign'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('✅ Documento firmado');
                    updateDocumentStatus(documentId, 'signed');
                    showNotification('Documento firmado correctamente', 'success');
                } else {
                    showNotification('Error al firmar documento', 'error');
                }
            })
            .catch(error => {
                console.error('❌ Error:', error);
                showNotification('Error de conexión', 'error');
            });
        }

        function downloadDocument(filePath, title) {
            console.log(`💾 Descargando documento: ${title}`);

            const link = document.createElement('a');
            link.href = filePath;
            link.download = title;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Descarga iniciada', 'info');
        }

        function updateDocumentStatus(documentId, status) {
            const card = document.querySelector(`[data-id="${documentId}"]`);
            if (!card) return;

            const statusContainer = card.querySelector('.document-status');
            if (!statusContainer) return;

            if (status === 'read') {
                // Agregar badge de leído si no existe
                if (!statusContainer.querySelector('.status-badge.read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge read';
                    readBadge.innerHTML = '<i class="fas fa-eye"></i>';
                    statusContainer.appendChild(readBadge);
                }

                // Deshabilitar botón de marcar como leído
                const readBtn = card.querySelector('[onclick*="markAsRead"]');
                if (readBtn) {
                    readBtn.classList.add('disabled');
                    readBtn.disabled = true;
                    readBtn.querySelector('span').textContent = 'Leído';
                }
            }

            if (status === 'signed') {
                // Agregar badge de firmado si no existe
                if (!statusContainer.querySelector('.status-badge.signed')) {
                    const signedBadge = document.createElement('span');
                    signedBadge.className = 'status-badge signed';
                    signedBadge.innerHTML = '<i class="fas fa-signature"></i>';
                    statusContainer.appendChild(signedBadge);
                }

                // Deshabilitar botón de firmar
                const signBtn = card.querySelector('[onclick*="signDocument"]');
                if (signBtn) {
                    signBtn.classList.add('disabled');
                    signBtn.disabled = true;
                    signBtn.querySelector('span').textContent = 'Firmado';
                }

                // También marcar como leído si no lo estaba
                updateDocumentStatus(documentId, 'read');
            }
        }

        // Función para inicializar la paginación
        function initializePagination() {
            console.log('📄 Inicializando paginación...');

            // Obtener todos los documentos
            const container = document.getElementById('informativos-container');
            if (!container) {
                console.error('❌ No se encontró el contenedor informativos-container');
                return;
            }

            // Filtrar solo elementos que son cards de informativos (no comentarios HTML)
            allDocuments = Array.from(container.children).filter(child =>
                child.nodeType === Node.ELEMENT_NODE &&
                child.classList.contains('informativo-card')
            );
            totalItems = allDocuments.length;

            console.log(`📊 Total de documentos: ${totalItems}`);
            console.log(`📊 Documentos encontrados:`, allDocuments);

            if (totalItems === 0) {
                console.warn('⚠️ No hay documentos para paginar');
                // Ocultar paginación si no hay documentos
                const paginationContainer = document.getElementById('paginationContainer');
                if (paginationContainer) {
                    paginationContainer.style.display = 'none';
                }
                return;
            }

            // Mostrar paginación
            const paginationContainer = document.getElementById('paginationContainer');
            if (paginationContainer) {
                paginationContainer.style.display = 'flex';
            }

            // Configurar items por página según la vista
            updateItemsPerPage();

            // Solo aplicar paginación si hay más documentos que el límite por página
            if (totalItems > itemsPerPage) {
                // Mostrar primera página
                showPage(1);
                // Actualizar controles de paginación
                updatePaginationControls();
            } else {
                // Si hay pocos documentos, mostrar todos y ocultar controles
                allDocuments.forEach(doc => {
                    doc.style.display = '';
                });
                currentPage = 1;
                updateDocumentsCount();
                // Ocultar controles de navegación si no son necesarios
                const controls = document.querySelector('.pagination-controls');
                if (controls) {
                    controls.style.display = 'none';
                }
            }
        }

        // Función para actualizar items por página según la vista
        function updateItemsPerPage() {
            if (currentView === 'grid') {
                itemsPerPage = parseInt(document.getElementById('itemsPerPage').value) || 6;
            } else {
                // En vista lista, ajustar a múltiplos de 4 (2 columnas x 2 filas)
                const selectedValue = parseInt(document.getElementById('itemsPerPage').value) || 6;
                itemsPerPage = Math.max(4, Math.floor(selectedValue / 4) * 4);
            }
            console.log(`📄 Items por página: ${itemsPerPage} (Vista: ${currentView})`);
        }

        // Función para mostrar una página específica
        function showPage(pageNumber) {
            console.log(`📄 Mostrando página ${pageNumber}`);
            console.log(`📊 Items por página: ${itemsPerPage}`);
            console.log(`📊 Total documentos: ${allDocuments.length}`);

            if (!allDocuments || allDocuments.length === 0) {
                console.warn('⚠️ No hay documentos para mostrar');
                return;
            }

            const startIndex = (pageNumber - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;

            console.log(`📊 Mostrando índices ${startIndex} a ${endIndex - 1}`);

            // Mostrar/ocultar documentos
            allDocuments.forEach((doc, index) => {
                if (index >= startIndex && index < endIndex) {
                    doc.style.display = '';
                    doc.style.animation = `fadeInUp 0.6s ease-out ${(index - startIndex) * 0.1}s`;
                    console.log(`✅ Mostrando documento ${index}:`, doc);
                } else {
                    doc.style.display = 'none';
                    console.log(`❌ Ocultando documento ${index}`);
                }
            });

            currentPage = pageNumber;
            updatePaginationControls();
            updateDocumentsCount();
        }

        // Función para actualizar el contador de documentos
        function updateDocumentsCount() {
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
            const countElement = document.getElementById('documents-count');
            if (countElement) {
                countElement.textContent = `${startIndex + 1}-${endIndex} de ${totalItems}`;
            }
        }

        // Función para actualizar los controles de paginación
        function updatePaginationControls() {
            const totalPages = Math.ceil(totalItems / itemsPerPage);

            // Actualizar información de página
            document.getElementById('currentPage').textContent = currentPage;
            document.getElementById('totalPages').textContent = totalPages;

            // Actualizar botones de navegación
            const firstBtn = document.getElementById('firstPageBtn');
            const prevBtn = document.getElementById('prevPageBtn');
            const nextBtn = document.getElementById('nextPageBtn');
            const lastBtn = document.getElementById('lastPageBtn');

            firstBtn.disabled = currentPage === 1;
            prevBtn.disabled = currentPage === 1;
            nextBtn.disabled = currentPage === totalPages;
            lastBtn.disabled = currentPage === totalPages;

            // Generar números de página
            generatePageNumbers(totalPages);
        }

        // Función para generar números de página
        function generatePageNumbers(totalPages) {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            pageNumbersContainer.innerHTML = '';

            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            // Ajustar si estamos cerca del final
            if (endPage - startPage < maxVisiblePages - 1) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            // Agregar primera página y ellipsis si es necesario
            if (startPage > 1) {
                addPageNumber(1);
                if (startPage > 2) {
                    addEllipsis();
                }
            }

            // Agregar páginas visibles
            for (let i = startPage; i <= endPage; i++) {
                addPageNumber(i);
            }

            // Agregar ellipsis y última página si es necesario
            if (endPage < totalPages) {
                if (endPage < totalPages - 1) {
                    addEllipsis();
                }
                addPageNumber(totalPages);
            }
        }

        // Función para agregar un número de página
        function addPageNumber(pageNum) {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            const button = document.createElement('button');
            button.className = `page-number ${pageNum === currentPage ? 'active' : ''}`;
            button.innerHTML = `<span>${pageNum}</span>`;
            button.onclick = () => goToPage(pageNum);
            pageNumbersContainer.appendChild(button);
        }

        // Función para agregar ellipsis
        function addEllipsis() {
            const pageNumbersContainer = document.getElementById('pageNumbers');
            const ellipsis = document.createElement('span');
            ellipsis.className = 'page-ellipsis';
            ellipsis.textContent = '...';
            pageNumbersContainer.appendChild(ellipsis);
        }

        // Funciones de navegación
        function goToPage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= Math.ceil(totalItems / itemsPerPage)) {
                showPage(pageNumber);
            }
        }

        function previousPage() {
            if (currentPage > 1) {
                showPage(currentPage - 1);
            }
        }

        function nextPage() {
            if (currentPage < Math.ceil(totalItems / itemsPerPage)) {
                showPage(currentPage + 1);
            }
        }

        function goToLastPage() {
            const lastPage = Math.ceil(totalItems / itemsPerPage);
            showPage(lastPage);
        }

        // Función para cambiar items por página
        function changeItemsPerPage() {
            updateItemsPerPage();
            showPage(1); // Volver a la primera página
        }

        // Función para configurar event listeners de botones
        function setupActionButtons() {
            console.log('🔧 Configurando event listeners para botones...');

            // Buscar todos los botones de acción
            const actionButtons = document.querySelectorAll('.action-btn');
            console.log(`📊 Encontrados ${actionButtons.length} botones de acción`);

            // Event listener universal para todos los botones de acción
            actionButtons.forEach((button, index) => {
                console.log(`🔍 Configurando botón ${index + 1}:`, {
                    classes: button.className,
                    action: button.dataset.action,
                    informativoId: button.dataset.informativoId,
                    filePath: button.dataset.filePath,
                    disabled: button.disabled
                });
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Si el botón está deshabilitado, no hacer nada
                    if (this.disabled) {
                        console.log('🚫 Botón deshabilitado, ignorando click');
                        return;
                    }

                    const action = this.dataset.action;
                    const informativoId = this.dataset.informativoId;
                    const filePath = this.dataset.filePath;

                    console.log('🔍 Botón clickeado:', {
                        action: action,
                        informativoId: informativoId,
                        filePath: filePath,
                        button: this
                    });

                    // Ejecutar la acción correspondiente
                    switch (action) {
                        case 'read':
                            if (informativoId) {
                                console.log('📖 Ejecutando markAsRead con ID:', informativoId);
                                markAsRead(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción read');
                            }
                            break;

                        case 'sign':
                            if (informativoId) {
                                console.log('✍️ Ejecutando signDocument con ID:', informativoId);
                                signDocument(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción sign');
                            }
                            break;

                        case 'download':
                            if (filePath) {
                                console.log('💾 Ejecutando downloadDocument con path:', filePath);
                                downloadDocument(filePath);
                            } else {
                                console.error('❌ No se encontró filePath para acción download');
                            }
                            break;

                        case 'delete':
                            if (informativoId) {
                                console.log('🗑️ Ejecutando deleteInformativo con ID:', informativoId);
                                deleteInformativo(parseInt(informativoId));
                            } else {
                                console.error('❌ No se encontró informativoId para acción delete');
                            }
                            break;

                        default:
                            console.error('❌ Acción no reconocida:', action);
                            break;
                    }
                });
            });

            console.log('✅ Event listeners configurados correctamente');
        }

        // Función para toggle del sidebar
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');

            // Agregar efecto de blur al contenido principal cuando el sidebar está abierto en móvil
            const mainContent = document.querySelector('.main-content');
            if (window.innerWidth <= 768) {
                if (sidebar.classList.contains('show')) {
                    mainContent.style.filter = 'blur(3px)';
                } else {
                    mainContent.style.filter = 'none';
                }
            }
        }

        // Funciones para marcar como leído y firmar documentos
        function markAsRead(informativoId) {
            console.log('markAsRead called with ID:', informativoId);

            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: informativoId,
                    action: 'read'
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    showNotification('Documento marcado como leído', 'success');
                    updateDocumentStatus(informativoId, 'read');
                } else {
                    showNotification('Error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error de conexión', 'error');
            });
        }

        function signDocument(informativoId) {
            console.log('signDocument called with ID:', informativoId);

            if (confirm('¿Estás seguro de que quieres firmar digitalmente este documento? Esta acción confirma que has leído y comprendido el contenido.')) {
                fetch('api/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        informativo_id: informativoId,
                        action: 'sign'
                    })
                })
                .then(response => {
                    console.log('Sign response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Sign response data:', data);
                    if (data.success) {
                        showNotification('Documento firmado digitalmente', 'success');
                        updateDocumentStatus(informativoId, 'signed');
                    } else {
                        showNotification('Error: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión', 'error');
                });
            }
        }

        function updateDocumentStatus(informativoId, action) {
            const card = document.querySelector(`[data-id="${informativoId}"]`);
            if (!card) return;

            const statusIndicators = card.querySelector('.status-indicators');
            const readBtn = card.querySelector('.btn-read');
            const signBtn = card.querySelector('.btn-sign');

            if (action === 'read') {
                // Actualizar botón de lectura
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badge de leído si no existe
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }
            } else if (action === 'sign') {
                // Actualizar botón de firma
                signBtn.classList.add('signed');
                signBtn.disabled = true;
                signBtn.innerHTML = '<i class="fas fa-signature"></i> Firmado';

                // También marcar como leído automáticamente
                readBtn.classList.add('read');
                readBtn.disabled = true;
                readBtn.innerHTML = '<i class="fas fa-eye"></i> Leído';

                // Agregar badges si no existen
                if (!statusIndicators.querySelector('.status-read')) {
                    const readBadge = document.createElement('span');
                    readBadge.className = 'status-badge-small status-read';
                    readBadge.innerHTML = '<i class="fas fa-eye me-1"></i>Leído';
                    statusIndicators.appendChild(readBadge);
                }

                if (!statusIndicators.querySelector('.status-signed')) {
                    const signBadge = document.createElement('span');
                    signBadge.className = 'status-badge-small status-signed';
                    signBadge.innerHTML = '<i class="fas fa-signature me-1"></i>Firmado';
                    statusIndicators.appendChild(signBadge);
                }
            }
        }

        function toggleView(viewType) {
            const container = document.getElementById('informativos-container');
            const gridBtn = document.getElementById('gridViewBtn');
            const listBtn = document.getElementById('listViewBtn');

            // Remover clases activas
            gridBtn.classList.remove('active');
            listBtn.classList.remove('active');

            // Remover clases de vista
            container.classList.remove('informativos-grid', 'informativos-list');

            // Actualizar vista actual
            currentView = viewType;

            if (viewType === 'grid') {
                container.classList.add('informativos-grid');
                gridBtn.classList.add('active');
            } else {
                container.classList.add('informativos-list');
                listBtn.classList.add('active');
            }

            // Actualizar items por página según la nueva vista
            updateItemsPerPage();

            // Volver a la primera página y actualizar paginación
            showPage(1);

            // Guardar preferencia
            localStorage.setItem('informativos-view', viewType);
        }

        function deleteInformativo(id) {
            console.log('deleteInformativo called with ID:', id);

            if (confirm('¿Estás seguro de que quieres eliminar este documento? Esta acción no se puede deshacer.')) {
                fetch('api/delete_informativo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => {
                    console.log('Delete response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Delete response data:', data);
                    if (data.success) {
                        showNotification('Documento eliminado exitosamente', 'success');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showNotification('Error al eliminar el documento: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('Error de conexión al eliminar el documento', 'error');
                });
            }
        }

        function downloadDocument(filePath) {
            console.log('downloadDocument called with path:', filePath);

            // Crear un enlace temporal para descargar
            const link = document.createElement('a');
            link.href = filePath;
            link.download = '';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Descarga iniciada', 'info');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            document.body.appendChild(notification);

            // Auto-remover después de 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Inicializar vista guardada
        document.addEventListener('DOMContentLoaded', function() {
            const savedView = localStorage.getItem('informativos-view') || 'grid';
            toggleView(savedView);

            // Aplicar tema guardado
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.body.classList.add('theme-' + savedTheme);

            // Actualizar icono del tema
            const themeIcon = document.querySelector('.theme-icon');
            if (themeIcon) {
                themeIcon.className = savedTheme === 'dark' ? 'theme-icon fas fa-sun' : 'theme-icon fas fa-moon';
            }
        });

        // Función para alternar tema
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.querySelector('.theme-icon');

            if (body.classList.contains('theme-dark')) {
                body.classList.remove('theme-dark');
                body.classList.add('theme-light');
                themeIcon.className = 'theme-icon fas fa-moon';
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.remove('theme-light');
                body.classList.add('theme-dark');
                themeIcon.className = 'theme-icon fas fa-sun';
                localStorage.setItem('theme', 'dark');
            }
        }

        // Funcionalidad moderna de subida de archivos
        document.addEventListener('DOMContentLoaded', function() {
            setupFileUpload();
        });

        function setupFileUpload() {
            const fileInput = document.getElementById('archivo');
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');

            if (!fileInput || !dropZone) return;

            // Manejar clic en la zona de arrastre
            dropZone.addEventListener('click', function() {
                fileInput.click();
            });

            // Manejar cambio de archivo
            fileInput.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    handleFileSelection(file);
                }
            });

            // Manejar arrastrar y soltar
            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    fileInput.files = files;
                    handleFileSelection(file);
                }
            });
        }

        function handleFileSelection(file) {
            const maxSize = 10 * 1024 * 1024; // 10MB
            const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png'];

            // Validar tamaño
            if (file.size > maxSize) {
                showNotification('El archivo es demasiado grande. Máximo 10MB permitido.', 'error');
                removeFile();
                return;
            }

            // Validar tipo
            if (!allowedTypes.includes(file.type)) {
                showNotification('Tipo de archivo no permitido. Solo PDF, DOC, DOCX, JPG, PNG.', 'error');
                removeFile();
                return;
            }

            // Mostrar preview
            showFilePreview(file);
            showNotification(`Archivo seleccionado: ${file.name}`, 'success');
        }

        function showFilePreview(file) {
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');
            const fileName = filePreview.querySelector('.file-name');
            const fileSize = filePreview.querySelector('.file-size');
            const fileIcon = filePreview.querySelector('.file-icon i');

            // Ocultar zona de arrastre y mostrar preview
            dropZone.style.display = 'none';
            filePreview.style.display = 'block';

            // Actualizar información del archivo
            fileName.textContent = file.name;

            const fileSizeFormatted = file.size >= 1048576 ?
                (file.size / 1048576).toFixed(1) + ' MB' :
                (file.size / 1024).toFixed(1) + ' KB';
            fileSize.textContent = fileSizeFormatted;

            // Actualizar icono según el tipo
            const ext = file.name.split('.').pop().toLowerCase();
            if (ext === 'pdf') {
                fileIcon.className = 'fas fa-file-pdf';
            } else if (['doc', 'docx'].includes(ext)) {
                fileIcon.className = 'fas fa-file-word';
            } else if (['jpg', 'jpeg', 'png'].includes(ext)) {
                fileIcon.className = 'fas fa-file-image';
            } else {
                fileIcon.className = 'fas fa-file';
            }
        }

        function removeFile() {
            const fileInput = document.getElementById('archivo');
            const dropZone = document.getElementById('fileDropZone');
            const filePreview = document.getElementById('filePreview');

            // Limpiar input
            fileInput.value = '';

            // Mostrar zona de arrastre y ocultar preview
            dropZone.style.display = 'block';
            filePreview.style.display = 'none';
        }

        // ===== FUNCIONALIDAD GOOGLE DRIVE =====

        // Configuración de Google Drive API (desde archivo de configuración)
        const GOOGLE_CLIENT_ID = GOOGLE_DRIVE_CONFIG?.CLIENT_ID || 'TU_CLIENT_ID_AQUI';
        const GOOGLE_API_KEY = GOOGLE_DRIVE_CONFIG?.API_KEY || 'TU_API_KEY_AQUI';
        const DISCOVERY_DOC = GOOGLE_DRIVE_CONFIG?.DISCOVERY_DOC || 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest';
        const SCOPES = GOOGLE_DRIVE_CONFIG?.SCOPES || 'https://www.googleapis.com/auth/drive.readonly';

        let gapi_loaded = false;
        let gsi_loaded = false;
        let tokenClient;

        // Cargar APIs de Google
        function loadGoogleAPIs() {
            gapi.load('client', initializeGapi);
            google.accounts.id.initialize({
                client_id: GOOGLE_CLIENT_ID,
                callback: handleCredentialResponse
            });
            gsi_loaded = true;
        }

        async function initializeGapi() {
            await gapi.client.init({
                apiKey: GOOGLE_API_KEY,
                discoveryDocs: [DISCOVERY_DOC],
            });
            gapi_loaded = true;
        }

        function handleCredentialResponse(response) {
            console.log('Credential response:', response);
        }

        // Cambiar método de subida
        function switchUploadMethod(method) {
            const tabs = document.querySelectorAll('.method-tab');
            const methods = document.querySelectorAll('.upload-method');
            const uploadMethodInput = document.getElementById('uploadMethod');
            const fileInput = document.getElementById('archivo');

            // Actualizar tabs
            tabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.dataset.method === method) {
                    tab.classList.add('active');
                }
            });

            // Mostrar/ocultar métodos
            methods.forEach(methodDiv => {
                methodDiv.style.display = 'none';
            });

            if (method === 'local') {
                document.getElementById('localUpload').style.display = 'block';
                fileInput.required = true;
            } else if (method === 'drive') {
                document.getElementById('driveUpload').style.display = 'block';
                fileInput.required = false;
                if (!gapi_loaded || !gsi_loaded) {
                    loadGoogleAPIs();
                }
            }

            // Actualizar campo oculto
            uploadMethodInput.value = method;

            // Limpiar selecciones previas
            clearFileSelections();
        }

        // Autenticar con Google Drive
        function authenticateGoogleDrive() {
            if (!gapi_loaded) {
                showNotification('Cargando APIs de Google...', 'info');
                setTimeout(authenticateGoogleDrive, 1000);
                return;
            }

            tokenClient = google.accounts.oauth2.initTokenClient({
                client_id: GOOGLE_CLIENT_ID,
                scope: SCOPES,
                callback: (response) => {
                    if (response.error !== undefined) {
                        showNotification('Error de autenticación: ' + response.error, 'error');
                        return;
                    }

                    // Autenticación exitosa
                    document.getElementById('driveAuth').style.display = 'none';
                    document.getElementById('drivePicker').style.display = 'block';
                    showNotification('Conectado a Google Drive exitosamente', 'success');
                },
            });

            tokenClient.requestAccessToken();
        }

        // Abrir selector de archivos de Google Drive
        async function openDrivePicker() {
            try {
                const response = await gapi.client.drive.files.list({
                    pageSize: 20,
                    fields: 'nextPageToken, files(id, name, mimeType, size, webViewLink, thumbnailLink)',
                    q: "mimeType='application/pdf' or mimeType='application/msword' or mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document' or mimeType='image/jpeg' or mimeType='image/png'"
                });

                const files = response.result.files;
                if (files && files.length > 0) {
                    showDriveFileSelector(files);
                } else {
                    showNotification('No se encontraron archivos compatibles en tu Google Drive', 'info');
                }
            } catch (error) {
                console.error('Error al obtener archivos:', error);
                showNotification('Error al acceder a Google Drive: ' + error.message, 'error');
            }
        }

        // Mostrar selector de archivos
        function showDriveFileSelector(files) {
            const statusDiv = document.getElementById('driveStatus');

            let html = '<div class="drive-files-list">';
            html += '<h6 class="mb-3">Selecciona un archivo:</h6>';

            files.forEach(file => {
                const fileSize = file.size ? formatFileSize(parseInt(file.size)) : 'Tamaño desconocido';
                const fileIcon = getFileIcon(file.mimeType);

                html += `
                    <div class="drive-file-item" onclick="selectDriveFile('${file.id}', '${file.name}', '${file.webViewLink}', '${file.mimeType}')">
                        <div class="drive-file-icon">
                            <i class="${fileIcon}"></i>
                        </div>
                        <div class="drive-file-details">
                            <div class="drive-file-name">${file.name}</div>
                            <div class="drive-file-meta">${fileSize}</div>
                        </div>
                        <div class="drive-select-btn">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            statusDiv.innerHTML = html;
        }

        // Seleccionar archivo de Google Drive
        function selectDriveFile(fileId, fileName, fileUrl, mimeType) {
            // Actualizar campos ocultos
            document.getElementById('driveFileId').value = fileId;
            document.getElementById('driveFileName').value = fileName;
            document.getElementById('driveFileUrl').value = fileUrl;

            // Mostrar confirmación
            const statusDiv = document.getElementById('driveStatus');
            statusDiv.innerHTML = `
                <div class="drive-file-info">
                    <div class="drive-file-icon">
                        <i class="${getFileIcon(mimeType)}"></i>
                    </div>
                    <div class="drive-file-details">
                        <div class="drive-file-name">${fileName}</div>
                        <div class="drive-file-meta">Archivo seleccionado de Google Drive</div>
                    </div>
                    <button type="button" class="btn-remove-file" onclick="clearDriveSelection()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            statusDiv.className = 'drive-status success';

            showNotification(`Archivo seleccionado: ${fileName}`, 'success');
        }

        // Limpiar selección de Google Drive
        function clearDriveSelection() {
            document.getElementById('driveFileId').value = '';
            document.getElementById('driveFileName').value = '';
            document.getElementById('driveFileUrl').value = '';

            const statusDiv = document.getElementById('driveStatus');
            statusDiv.innerHTML = '';
            statusDiv.className = 'drive-status';
        }

        // Limpiar todas las selecciones de archivos
        function clearFileSelections() {
            // Limpiar archivo local
            removeFile();

            // Limpiar Google Drive
            clearDriveSelection();
        }

        // Obtener icono según tipo de archivo
        function getFileIcon(mimeType) {
            if (mimeType.includes('pdf')) {
                return 'fas fa-file-pdf';
            } else if (mimeType.includes('word') || mimeType.includes('document')) {
                return 'fas fa-file-word';
            } else if (mimeType.includes('image')) {
                return 'fas fa-file-image';
            } else {
                return 'fas fa-file';
            }
        }

        // Formatear tamaño de archivo
        function formatFileSize(bytes) {
            if (bytes >= 1048576) {
                return (bytes / 1048576).toFixed(1) + ' MB';
            } else if (bytes >= 1024) {
                return (bytes / 1024).toFixed(1) + ' KB';
            } else {
                return bytes + ' bytes';
            }
        }

        // Validar formulario antes de enviar
        function validateForm() {
            const uploadMethod = document.getElementById('uploadMethod').value;
            const fileInput = document.getElementById('archivo');
            const driveFileId = document.getElementById('driveFileId').value;

            if (uploadMethod === 'local') {
                if (!fileInput.files || fileInput.files.length === 0) {
                    showNotification('Por favor selecciona un archivo local', 'error');
                    return false;
                }
            } else if (uploadMethod === 'drive') {
                if (!driveFileId) {
                    showNotification('Por favor selecciona un archivo de Google Drive', 'error');
                    return false;
                }
            }

            return true;
        }

        // Agregar validación al formulario
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('.upload-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateForm()) {
                        e.preventDefault();
                        return false;
                    }
                });
            }
        });
    </script>

    <style>
        /* ===== ESTILOS ADICIONALES PARA GOOGLE DRIVE ===== */
        .drive-files-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: white;
        }

        .drive-file-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .drive-file-item:hover {
            background: #f8fafc;
        }

        .drive-file-item:last-child {
            border-bottom: none;
        }

        .drive-select-btn {
            color: #667eea;
            font-size: 18px;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .drive-file-item:hover .drive-select-btn {
            opacity: 1;
        }
    </style>
</body>
</html>
