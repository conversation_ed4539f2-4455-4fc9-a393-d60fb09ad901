// Configuración de Google Drive API
// IMPORTANTE: Reemplaza estos valores con tus credenciales reales de Google Cloud Console

const GOOGLE_DRIVE_CONFIG = {
    // Client ID de Google Cloud Console
    CLIENT_ID: 'TU_CLIENT_ID_AQUI.apps.googleusercontent.com',
    
    // API Key de Google Cloud Console
    API_KEY: 'TU_API_KEY_AQUI',
    
    // Scopes necesarios para acceder a Google Drive
    SCOPES: 'https://www.googleapis.com/auth/drive.readonly',
    
    // Discovery document para Google Drive API v3
    DISCOVERY_DOC: 'https://www.googleapis.com/discovery/v1/apis/drive/v3/rest',
    
    // Tipos de archivo permitidos (MIME types)
    ALLOWED_MIME_TYPES: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png'
    ],
    
    // Tamaño máximo de archivo (en bytes) - 10MB
    MAX_FILE_SIZE: 10 * 1024 * 1024
};

// Instrucciones para configurar Google Drive API:
/*
1. Ve a Google Cloud Console (https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Habilita la Google Drive API
4. Ve a "Credenciales" y crea:
   - Una API Key (para acceso público a la API)
   - Un Client ID OAuth 2.0 (para autenticación de usuarios)
5. En el Client ID OAuth 2.0, agrega tu dominio a "Orígenes autorizados de JavaScript"
6. Reemplaza los valores arriba con tus credenciales reales
7. Asegúrate de que el archivo esté en la carpeta config/ y sea accesible desde tu aplicación
*/

// Exportar configuración (si usas módulos ES6)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GOOGLE_DRIVE_CONFIG;
}
