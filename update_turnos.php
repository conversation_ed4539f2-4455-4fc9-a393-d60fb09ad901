<?php
require_once 'config/functions.php';

$database = new Database();
$conn = $database->getConnection();

try {
    echo "<h2>Actualizando estructura de turnos...</h2>";
    
    // Modificar tabla asignaciones para usar turnos específicos
    $alter_query = "ALTER TABLE asignaciones MODIFY COLUMN turno VARCHAR(20) NOT NULL";
    $conn->exec($alter_query);
    echo "<p>✅ Estructura de tabla asignaciones actualizada</p>";
    
    // Modificar tabla solicitudes_asignacion para usar turnos específicos
    $alter_solicitudes = "ALTER TABLE solicitudes_asignacion MODIFY COLUMN turno VARCHAR(20) NOT NULL";
    $conn->exec($alter_solicitudes);
    echo "<p>✅ Estructura de tabla solicitudes_asignacion actualizada</p>";
    
    echo "<h3>Turnos disponibles por categoría:</h3>";
    
    // Turnos de Apertura
    echo "<h4 style='color: #ffc107;'>🌅 Turnos de Apertura:</h4>";
    $turnos_apertura = [
        'Turno 9' => '05:00AM - 14:00PM',
        'Turno 24' => '06:00AM - 15:00PM',
        'Turno 40' => '09:00AM - 18:00PM',
        'Turno 43' => '09:00AM - 17:00PM',
        'Turno 59' => '03:00AM - 12:00PM',
        'Turno 66' => '06:00AM - 14:00PM',
        'Turno 68' => '05:00AM - 13:00PM',
        'Turno 74' => '10:00AM - 19:00PM',
        'Turno 77' => '06:00AM - 13:00PM',
        'Turno 92' => '11:00AM - 18:00PM',
        'Turno 145' => '06:00AM - 16:00PM',
        'Turno 146' => '03:00AM - 13:00PM',
        'Turno 161' => '07:00AM - 15:00PM',
        'Turno 242' => '11:00AM - 21:00PM',
        'Turno 287' => '11:00AM - 19:00PM'
    ];
    
    foreach ($turnos_apertura as $turno => $horario) {
        echo "<div style='background: rgba(255, 193, 7, 0.1); padding: 8px; margin: 4px 0; border-radius: 6px;'>";
        echo "<strong>$turno:</strong> $horario";
        echo "</div>";
    }
    
    // Turnos Tarde
    echo "<h4 style='color: #ff5722; margin-top: 20px;'>🌤️ Turnos Tarde:</h4>";
    $turnos_tarde = [
        'Turno 6' => '19:00PM - 04:00AM',
        'Turno 11' => '14:00PM - 23:00PM',
        'Turno 82' => '12:00PM - 21:00PM',
        'Turno 91' => '13:00PM - 18:00PM',
        'Turno 94' => '14:00PM - 19:00PM',
        'Turno 103' => '17:00PM - 22:00PM',
        'Turno 104' => '15:00PM - 22:00PM',
        'Turno 107' => '16:00PM - 23:00PM',
        'Turno 126' => '14:00PM - 00:00AM',
        'Turno 163' => '14:00PM - 22:00PM',
        'Turno 180' => '15:00PM - 23:00PM',
        'Turno 213' => '13:00PM - 23:00PM',
        'Turno 227' => '12:00PM - 22:00PM',
        'Turno 245' => '13:00PM - 21:00PM',
        'Turno 295' => '19:00PM - 03:00PM'
    ];
    
    foreach ($turnos_tarde as $turno => $horario) {
        echo "<div style='background: rgba(255, 87, 34, 0.1); padding: 8px; margin: 4px 0; border-radius: 6px;'>";
        echo "<strong>$turno:</strong> $horario";
        echo "</div>";
    }
    
    // Turnos Noche
    echo "<h4 style='color: #3f51b5; margin-top: 20px;'>🌙 Turnos Noche:</h4>";
    $turnos_noche = [
        'Turno 7' => '22:00PM - 07:00AM',
        'Turno 13' => '20:00PM - 05:00AM',
        'Turno 21' => '21:00PM - 06:00AM',
        'Turno 28' => '20:00PM - 06:00AM',
        'Turno 121' => '22:00PM - 08:00AM',
        'Turno 147' => '21:00PM - 07:00AM',
        'Turno 274' => '23:00PM - 07:00AM',
        'Turno 275' => '22:00PM - 06:00AM'
    ];
    
    foreach ($turnos_noche as $turno => $horario) {
        echo "<div style='background: rgba(63, 81, 181, 0.1); padding: 8px; margin: 4px 0; border-radius: 6px;'>";
        echo "<strong>$turno:</strong> $horario";
        echo "</div>";
    }
    
    // Turnos Especiales
    echo "<h4 style='color: #9c27b0; margin-top: 20px;'>⭐ Turnos Especiales:</h4>";
    $turnos_especiales = [
        'SALIENTE' => 'Turno de salida',
        'LIBRE' => 'Día libre',
        'DOMINGO LIBRE' => 'Domingo libre'
    ];
    
    foreach ($turnos_especiales as $turno => $descripcion) {
        echo "<div style='background: rgba(156, 39, 176, 0.1); padding: 8px; margin: 4px 0; border-radius: 6px;'>";
        echo "<strong>$turno:</strong> $descripcion";
        echo "</div>";
    }
    
    echo "<p style='margin-top: 30px;'><strong>Total de turnos:</strong> " . (count($turnos_apertura) + count($turnos_tarde) + count($turnos_noche) + count($turnos_especiales)) . "</p>";
    
    echo "<p style='margin-top: 20px;'><a href='asignaciones.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Ir a Asignaciones</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
