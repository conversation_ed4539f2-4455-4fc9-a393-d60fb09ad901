<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Data Attributes - Botones</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        
        .informativo-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            overflow: hidden;
            border: 1px solid #f1f5f9;
        }
        
        .card-content {
            padding: 25px;
        }
        
        .card-footer-actions {
            background: #f8fafc;
            padding: 20px 25px;
            border-top: 1px solid #f1f5f9;
        }
        
        .secondary-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            gap: 8px;
        }
        
        .action-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 10px 8px;
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-1px);
        }
        
        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .log-area {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-code me-2"></i>
            Test Data Attributes - Botones Informativos
        </h1>
        
        <div class="row">
            <div class="col-md-6">
                <!-- Tarjeta de prueba -->
                <div class="informativo-card" data-id="123">
                    <div class="card-content">
                        <h5>Documento de Prueba</h5>
                        <p>Esta es una tarjeta de prueba para verificar que los data attributes funcionan correctamente.</p>
                    </div>
                    
                    <div class="card-footer-actions">
                        <div class="secondary-actions">
                            <button class="action-btn btn-read"
                                    data-informativo-id="123"
                                    data-action="read"
                                    title="Marcar como leído">
                                <i class="fas fa-eye"></i>
                                <span>Marcar Leído</span>
                            </button>

                            <button class="action-btn btn-sign"
                                    data-informativo-id="123"
                                    data-action="sign"
                                    title="Firmar digitalmente">
                                <i class="fas fa-signature"></i>
                                <span>Firmar</span>
                            </button>

                            <button class="action-btn btn-download"
                                    data-file-path="uploads/test-document.pdf"
                                    data-action="download"
                                    title="Descargar documento">
                                <i class="fas fa-download"></i>
                                <span>Descargar</span>
                            </button>

                            <button class="action-btn btn-delete"
                                    data-informativo-id="123"
                                    data-action="delete"
                                    title="Eliminar documento">
                                <i class="fas fa-trash"></i>
                                <span>Eliminar</span>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Controles de test -->
                <div class="text-center">
                    <button class="btn-test" onclick="testAllButtons()">
                        <i class="fas fa-play me-2"></i>
                        Test Todos los Botones
                    </button>
                    
                    <button class="btn-test" onclick="inspectButtons()">
                        <i class="fas fa-search me-2"></i>
                        Inspeccionar Botones
                    </button>
                    
                    <button class="btn-test" onclick="clearLog()">
                        <i class="fas fa-broom me-2"></i>
                        Limpiar Log
                    </button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Console Log</h3>
                <div id="logArea" class="log-area">
                    === Test de Data Attributes ===<br>
                    Esperando interacción...<br>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Función para agregar logs
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4444' : type === 'success' ? '#44ff44' : '#00ff00';
            
            logArea.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '=== Log Limpiado ===<br>';
        }
        
        // Función para configurar event listeners (copiada del archivo original)
        function setupActionButtons() {
            addLog('🔧 Configurando event listeners para botones...');

            // Event listener universal para todos los botones de acción
            document.querySelectorAll('.action-btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Si el botón está deshabilitado, no hacer nada
                    if (this.disabled) {
                        addLog('🚫 Botón deshabilitado, ignorando click');
                        return;
                    }

                    const action = this.dataset.action;
                    const informativoId = this.dataset.informativoId;
                    const filePath = this.dataset.filePath;

                    addLog('🔍 Botón clickeado:', 'success');
                    addLog(`   - Action: ${action}`);
                    addLog(`   - InformativoId: ${informativoId}`);
                    addLog(`   - FilePath: ${filePath}`);
                    addLog(`   - Button classes: ${this.className}`);

                    // Ejecutar la acción correspondiente
                    switch (action) {
                        case 'read':
                            if (informativoId) {
                                addLog('📖 Ejecutando markAsRead con ID: ' + informativoId, 'success');
                                markAsRead(parseInt(informativoId));
                            } else {
                                addLog('❌ No se encontró informativoId para acción read', 'error');
                            }
                            break;

                        case 'sign':
                            if (informativoId) {
                                addLog('✍️ Ejecutando signDocument con ID: ' + informativoId, 'success');
                                signDocument(parseInt(informativoId));
                            } else {
                                addLog('❌ No se encontró informativoId para acción sign', 'error');
                            }
                            break;

                        case 'download':
                            if (filePath) {
                                addLog('💾 Ejecutando downloadDocument con path: ' + filePath, 'success');
                                downloadDocument(filePath);
                            } else {
                                addLog('❌ No se encontró filePath para acción download', 'error');
                            }
                            break;

                        case 'delete':
                            if (informativoId) {
                                addLog('🗑️ Ejecutando deleteInformativo con ID: ' + informativoId, 'success');
                                deleteInformativo(parseInt(informativoId));
                            } else {
                                addLog('❌ No se encontró informativoId para acción delete', 'error');
                            }
                            break;

                        default:
                            addLog('❌ Acción no reconocida: ' + action, 'error');
                            break;
                    }
                });
            });

            addLog('✅ Event listeners configurados correctamente', 'success');
        }
        
        // Funciones simuladas
        function markAsRead(id) {
            addLog(`✅ markAsRead ejecutada con ID: ${id}`, 'success');
            showNotification('Documento marcado como leído', 'success');
        }
        
        function signDocument(id) {
            addLog(`✅ signDocument ejecutada con ID: ${id}`, 'success');
            if (confirm('¿Firmar documento de prueba?')) {
                showNotification('Documento firmado digitalmente', 'success');
            }
        }
        
        function downloadDocument(path) {
            addLog(`✅ downloadDocument ejecutada con path: ${path}`, 'success');
            showNotification('Descarga iniciada', 'info');
        }
        
        function deleteInformativo(id) {
            addLog(`✅ deleteInformativo ejecutada con ID: ${id}`, 'success');
            if (confirm('¿Eliminar documento de prueba?')) {
                showNotification('Documento eliminado', 'success');
            }
        }
        
        function showNotification(message, type = 'info') {
            addLog(`🔔 Notificación: ${message} (${type})`);
            
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }
        
        // Funciones de test
        function testAllButtons() {
            addLog('🚀 Iniciando test de todos los botones...', 'success');
            
            const buttons = document.querySelectorAll('.action-btn');
            addLog(`📊 Encontrados ${buttons.length} botones`);
            
            buttons.forEach((button, index) => {
                setTimeout(() => {
                    addLog(`🔍 Simulando click en botón ${index + 1}...`);
                    button.click();
                }, index * 1000);
            });
        }
        
        function inspectButtons() {
            addLog('🔍 Inspeccionando botones...', 'success');
            
            const buttons = document.querySelectorAll('.action-btn');
            
            buttons.forEach((button, index) => {
                addLog(`📋 Botón ${index + 1}:`);
                addLog(`   - Classes: ${button.className}`);
                addLog(`   - Action: ${button.dataset.action}`);
                addLog(`   - InformativoId: ${button.dataset.informativoId}`);
                addLog(`   - FilePath: ${button.dataset.filePath}`);
                addLog(`   - Disabled: ${button.disabled}`);
                addLog(`   - Title: ${button.title}`);
                addLog('---');
            });
        }
        
        // Inicializar cuando se carga la página
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 DOM cargado, inicializando sistema...', 'success');
            setupActionButtons();
            addLog('✅ Sistema listo para pruebas', 'success');
        });
    </script>
</body>
</html>
