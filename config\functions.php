<?php
// Prevenir inclusión múltiple
if (defined('FUNCTIONS_LOADED')) {
    return;
}
define('FUNCTIONS_LOADED', true);

// Configurar sesión segura
if (session_status() === PHP_SESSION_NONE) {
    // Configurar parámetros de sesión seguros
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Lax');

    // Regenerar ID de sesión periódicamente para seguridad
    session_start();

    // Regenerar ID de sesión si es una nueva sesión o cada 30 minutos
    if (!isset($_SESSION['last_regeneration'])) {
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 1800) { // 30 minutos
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

require_once 'database.php';

// Función para verificar si el usuario está logueado
function isLoggedIn() {
    if (!isset($_SESSION['agente_id']) || empty($_SESSION['agente_id'])) {
        return false;
    }

    // Verificaciones adicionales de seguridad
    if (isset($_SESSION['user_agent']) && $_SESSION['user_agent'] !== ($_SERVER['HTTP_USER_AGENT'] ?? '')) {
        // User agent cambió, posible hijacking
        session_destroy();
        return false;
    }

    if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== ($_SERVER['REMOTE_ADDR'] ?? '')) {
        // IP cambió, posible hijacking (comentado para desarrollo local)
        // session_destroy();
        // return false;
    }

    return true;
}

// Función para obtener datos del agente actual
function getCurrentAgent() {
    if (!isLoggedIn()) {
        return null;
    }

    $database = new Database();
    $conn = $database->getConnection();

    // Verificar que el agente_id sea un número válido
    $agente_id = (int)$_SESSION['agente_id'];

    if ($agente_id <= 0) {
        // ID inválido, limpiar sesión
        session_destroy();
        return null;
    }

    // Verificar qué columnas de fotos existen
    $foto_columns = [];
    $check_columns = ['foto_perfil', 'foto_portada', 'portada_position_x', 'portada_position_y', 'portada_scale'];

    foreach ($check_columns as $column) {
        $check_query = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
                       WHERE TABLE_SCHEMA = DATABASE()
                       AND TABLE_NAME = 'agentes'
                       AND COLUMN_NAME = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->execute([$column]);
        if ($check_stmt->fetch()['count'] > 0) {
            $foto_columns[] = $column;
        }
    }

    // Construir query dinámicamente según las columnas disponibles
    $select_columns = "a.*, g.nombre as grupo_nombre, g.permisos";
    if (!empty($foto_columns)) {
        $select_columns .= ", " . implode(", ", array_map(function($col) { return "a.$col"; }, $foto_columns));
    }

    $query = "SELECT $select_columns
              FROM agentes a
              LEFT JOIN grupos g ON a.grupo_id = g.id
              WHERE a.id = ? AND a.activo = 1";

    $stmt = $conn->prepare($query);
    $stmt->execute([$agente_id]);
    $agente = $stmt->fetch();

    // Agregar valores por defecto para columnas que no existen
    if ($agente) {
        if (!isset($agente['foto_perfil'])) $agente['foto_perfil'] = null;
        if (!isset($agente['foto_portada'])) $agente['foto_portada'] = null;
        if (!isset($agente['portada_position_x'])) $agente['portada_position_x'] = 0;
        if (!isset($agente['portada_position_y'])) $agente['portada_position_y'] = 0;
        if (!isset($agente['portada_scale'])) $agente['portada_scale'] = 1.0;
    }

    // Si no se encuentra el agente, limpiar sesión
    if (!$agente) {
        session_destroy();
        return null;
    }

    // Verificar y corregir consistencia de datos de sesión
    $sesion_actualizada = false;

    if (isset($_SESSION['agente_nombre']) && $_SESSION['agente_nombre'] !== $agente['nombre']) {
        $_SESSION['agente_nombre'] = $agente['nombre'];
        $sesion_actualizada = true;
    }

    if (!isset($_SESSION['agente_nombre'])) {
        $_SESSION['agente_nombre'] = $agente['nombre'];
        $sesion_actualizada = true;
    }

    if ($sesion_actualizada) {
        $_SESSION['last_verification'] = time();
    }

    return $agente;
}

// Función para verificar permisos
function hasPermission($permission) {
    $agent = getCurrentAgent();
    if (!$agent) return false;
    
    $permisos = json_decode($agent['permisos'], true);
    return in_array('all_permissions', $permisos) || in_array($permission, $permisos);
}

// Función para redirigir si no está logueado
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit();
    }
}

// Función para redirigir si no tiene permisos
function requirePermission($permission) {
    requireLogin();
    if (!hasPermission($permission)) {
        header('Location: index.php?error=sin_permisos');
        exit();
    }
}

// Función para limpiar datos de entrada
function cleanInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Función para generar hash de contraseña
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Función para verificar contraseña
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Función para generar token CSRF
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Función para verificar token CSRF
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Función para crear notificación
function createNotification($agente_id, $categoria, $titulo, $mensaje, $url = null, $datos_adicionales = null) {
    $database = new Database();
    $conn = $database->getConnection();

    // Mapear categorías a tipos válidos
    $tipo_map = [
        'sistema' => 'sistema',
        'operacional' => 'operacional',
        'social' => 'social',
        'urgente' => 'operacional', // Mapear urgente a operacional
        'informativa' => 'sistema', // Mapear informativa a sistema
        'general' => 'sistema' // Mapear general a sistema
    ];

    $tipo = isset($tipo_map[$categoria]) ? $tipo_map[$categoria] : 'sistema';

    // Usar solo las columnas que existen en la tabla
    $query = "INSERT INTO notificaciones (agente_id, tipo, titulo, mensaje, url) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    return $stmt->execute([$agente_id, $tipo, $titulo, $mensaje, $url]);
}

// Función para obtener notificaciones no leídas
function getUnreadNotifications($agente_id) {
    $database = new Database();
    $conn = $database->getConnection();

    $query = "SELECT * FROM notificaciones WHERE agente_id = ? AND leida = 0 ORDER BY created_at DESC";
    $stmt = $conn->prepare($query);
    $stmt->execute([$agente_id]);

    return $stmt->fetchAll();
}

// Función para obtener todas las notificaciones (leídas y no leídas)
function getNotifications($agente_id, $limit = 10) {
    $database = new Database();
    $conn = $database->getConnection();

    try {
        $query = "SELECT * FROM notificaciones
                  WHERE agente_id = ?
                  ORDER BY created_at DESC
                  LIMIT ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$agente_id, $limit]);

        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error en getNotifications: " . $e->getMessage());
        return [];
    }
}



// Función para enviar notificación de asignación
function sendAssignmentNotification($agente_id, $funcion_nombre, $terminal_nombre, $turno, $fecha, $asignado_por_id) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Obtener información del agente asignado
        $agente_query = "SELECT nombre FROM agentes WHERE id = ?";
        $agente_stmt = $conn->prepare($agente_query);
        $agente_stmt->execute([$agente_id]);
        $agente = $agente_stmt->fetch();

        // Obtener información de quien asignó
        $asignador_query = "SELECT nombre FROM agentes WHERE id = ?";
        $asignador_stmt = $conn->prepare($asignador_query);
        $asignador_stmt->execute([$asignado_por_id]);
        $asignador = $asignador_stmt->fetch();

        if ($agente && $asignador) {
            $titulo = "Nueva Asignación de Función";
            $mensaje = "Has sido asignado a la función '{$funcion_nombre}' en {$terminal_nombre} para el turno {$turno} del " .
                      date('d/m/Y', strtotime($fecha)) . ". Asignado por: {$asignador['nombre']}.";

            // Usar la función existente con la firma correcta
            return createNotification($agente_id, 'operacional', $titulo, $mensaje);
        }

        return false;
    } catch (Exception $e) {
        error_log("Error enviando notificación de asignación: " . $e->getMessage());
        return false;
    }
}

// Función para marcar notificación como leída
function markNotificationAsRead($notification_id) {
    $database = new Database();
    $conn = $database->getConnection();

    // Solo actualizar la columna leida que existe en la tabla
    $query = "UPDATE notificaciones SET leida = 1 WHERE id = ?";
    $stmt = $conn->prepare($query);
    return $stmt->execute([$notification_id]);
}

// Función para crear notificación de solicitud de amistad
function createFriendRequestNotification($solicitante_id, $solicitado_id) {
    $database = new Database();
    $conn = $database->getConnection();

    // Obtener nombre del solicitante
    $query = "SELECT nombre FROM agentes WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->execute([$solicitante_id]);
    $solicitante = $stmt->fetch();

    if ($solicitante) {
        $datos_adicionales = json_encode(['solicitante_id' => $solicitante_id]);
        return createNotification(
            $solicitado_id,
            'social',
            'Nueva solicitud de amistad',
            $solicitante['nombre'] . ' te ha enviado una solicitud de amistad',
            null,
            $datos_adicionales
        );
    }

    return false;
}

// Función para crear notificación operacional
function createOperationalNotification($agente_id, $titulo, $mensaje, $url = null) {
    return createNotification($agente_id, 'operacional', $titulo, $mensaje, $url);
}

// Función para crear notificación urgente
function createUrgentNotification($agente_id, $titulo, $mensaje, $url = null) {
    return createNotification($agente_id, 'urgente', $titulo, $mensaje, $url);
}

// Función para crear notificación informativa
function createInformativeNotification($agente_id, $titulo, $mensaje, $url = null) {
    return createNotification($agente_id, 'informativa', $titulo, $mensaje, $url);
}

// Función para formatear tiempo relativo (timeAgo)
if (!function_exists('timeAgo')) {
    function timeAgo($datetime) {
        $time = time() - strtotime($datetime);

        if ($time < 60) return 'hace ' . $time . ' seg';
        if ($time < 3600) return 'hace ' . floor($time/60) . ' min';
        if ($time < 86400) return 'hace ' . floor($time/3600) . ' h';
        if ($time < 2592000) return 'hace ' . floor($time/86400) . ' días';
        if ($time < 31536000) return 'hace ' . floor($time/2592000) . ' meses';
        return 'hace ' . floor($time/31536000) . ' años';
    }
}

// Función para obtener agentes por grupo
function getAgentsByGroup($grupo_nombre) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT a.* FROM agentes a 
              JOIN grupos g ON a.grupo_id = g.id 
              WHERE g.nombre = ? AND a.activo = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$grupo_nombre]);
    
    return $stmt->fetchAll();
}

// Función para actualizar estado del agente
function updateAgentStatus($agente_id, $estado) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "UPDATE agentes SET estado = ?, ultimo_acceso = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($query);
    return $stmt->execute([$estado, $agente_id]);
}

// Función para registrar inicio de turno
function startShift($agente_id) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "UPDATE agentes SET estado = '🟢Disponible', turno_inicio = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($query);
    $result = $stmt->execute([$agente_id]);
    
    if ($result) {
        // Obtener nombre del agente
        $agent = getCurrentAgent();
        $nombre = $agent['nombre'];
        
        // Notificar a supervisores
        $supervisores = ['Lobby', 'Lideres', 'CREC', 'Back Office', 'Duty Manager'];
        foreach ($supervisores as $grupo) {
            $agentes_grupo = getAgentsByGroup($grupo);
            foreach ($agentes_grupo as $supervisor) {
                createNotification(
                    $supervisor['id'],
                    'operacional',
                    'Agente llegó a turno',
                    "$nombre informa que llegó a turno!✈",
                    'index.php'
                );
            }
        }
    }
    
    return $result;
}

// Función para registrar fin de turno
function endShift($agente_id) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "UPDATE agentes SET estado = '🏠Fuera de Turno', turno_fin = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($query);
    $result = $stmt->execute([$agente_id]);
    
    if ($result) {
        // Obtener nombre del agente
        $agent = getCurrentAgent();
        $nombre = $agent['nombre'];
        
        // Notificar a supervisores
        $supervisores = ['Lobby', 'Lideres', 'CREC', 'Back Office', 'Duty Manager'];
        foreach ($supervisores as $grupo) {
            $agentes_grupo = getAgentsByGroup($grupo);
            foreach ($agentes_grupo as $supervisor) {
                createNotification(
                    $supervisor['id'],
                    'operacional',
                    'Agente salió de turno',
                    "$nombre informa que ha salido de turno!🏡",
                    'index.php'
                );
            }
        }
    }
    
    return $result;
}

// Función para formatear fecha en español con soporte UTF-8 completo
function formatearFechaEspanol($fecha) {
    $dias = [
        'Sunday' => 'domingo',
        'Monday' => 'lunes',
        'Tuesday' => 'martes',
        'Wednesday' => 'miércoles',
        'Thursday' => 'jueves',
        'Friday' => 'viernes',
        'Saturday' => 'sábado'
    ];

    $meses = [
        'January' => 'enero',
        'February' => 'febrero',
        'March' => 'marzo',
        'April' => 'abril',
        'May' => 'mayo',
        'June' => 'junio',
        'July' => 'julio',
        'August' => 'agosto',
        'September' => 'septiembre',
        'October' => 'octubre',
        'November' => 'noviembre',
        'December' => 'diciembre'
    ];

    $timestamp = strtotime($fecha);
    $dia_semana = $dias[date('l', $timestamp)];
    $dia = date('d', $timestamp);
    $mes = $meses[date('F', $timestamp)];
    $año = date('Y', $timestamp);

    return ucfirst($dia_semana) . ', ' . $dia . ' de ' . $mes . ' de ' . $año;
}

// Función para formatear fecha (mantenida para compatibilidad)
function formatDate($date, $format = 'd/m/Y') {
    date_default_timezone_set('America/Santiago');

    // Usar la nueva función para formatos en español
    if ($format === 'es_full') {
        return formatearFechaEspanol($date);
    }

    // Para otros formatos, usar date() estándar
    return date($format, strtotime($date));
}

// Función para formatear hora
function formatTime($time, $format = 'H:i') {
    date_default_timezone_set('America/Santiago');
    return date($format, strtotime($time));
}



// Función para obtener avatar del agente
if (!function_exists('getAvatarUrl')) {
function getAvatarUrl($foto_perfil) {
    // Si hay foto de perfil válida, intentar usarla
    if (!empty($foto_perfil) && trim($foto_perfil) !== '') {
        // Si el archivo existe, devolverlo
        if (file_exists($foto_perfil)) {
            return $foto_perfil;
        }
    }

    // Siempre devolver el avatar por defecto
    // Sabemos que existe porque lo vimos en el directorio
    return 'assets/images/default-avatar.png';
}
}

// Función para obtener portada del agente
function getPortadaUrl($foto_portada) {
    if (empty($foto_portada) || !file_exists($foto_portada)) {
        return 'assets/images/default-cover.jpg';
    }
    return $foto_portada;
}

// Función para validar email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Función para generar ID único
function generateUniqueId() {
    return uniqid(mt_rand(), true);
}

// Función para subir archivo
function uploadFile($file, $destination_path, $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'pdf']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No se seleccionó archivo'];
    }

    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'Tipo de archivo no permitido'];
    }

    $new_filename = generateUniqueId() . '.' . $file_extension;
    $full_path = $destination_path . '/' . $new_filename;

    if (!is_dir($destination_path)) {
        mkdir($destination_path, 0755, true);
    }

    if (move_uploaded_file($file['tmp_name'], $full_path)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $full_path];
    } else {
        return ['success' => false, 'message' => 'Error al subir archivo'];
    }
}

// Función para obtener tema del usuario
function getUserTheme($agente_id = null) {
    if (!$agente_id) {
        $agente = getCurrentAgent();
        if (!$agente) return 'light'; // tema por defecto
        $agente_id = $agente['id'];
    }

    $database = new Database();
    $conn = $database->getConnection();

    // Asegurar codificación UTF-8 para caracteres especiales
    $conn->exec("SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");

    $query = "SELECT tema FROM agentes WHERE id = :id";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':id', $agente_id);
    $stmt->execute();

    $result = $stmt->fetch();
    return $result ? ($result['tema'] ?? 'light') : 'light';
}

// Función para generar HTML del selector de tema moderno
// Soporte completo para caracteres especiales, acentos y ñ
function renderModernThemeSelector($current_theme = 'light') {
    $is_dark = $current_theme === 'dark';

    // Textos con soporte completo para caracteres especiales
    $tooltip_claro = 'Cambiar a tema claro ☀️';
    $tooltip_oscuro = 'Cambiar a tema oscuro 🌙';
    $tooltip_text = $is_dark ? $tooltip_claro : $tooltip_oscuro;

    return '
    <div class="modern-theme-selector" data-theme="' . htmlspecialchars($current_theme, ENT_QUOTES, 'UTF-8') . '">
        <div class="theme-toggle-container">
            <div class="theme-toggle-wrapper">
                <input type="checkbox" id="theme-toggle" class="theme-toggle-input" ' . ($is_dark ? 'checked' : '') . '>
                <label for="theme-toggle" class="theme-toggle-label" title="' . htmlspecialchars($tooltip_text, ENT_QUOTES, 'UTF-8') . '">
                    <div class="theme-toggle-slider">
                        <div class="theme-toggle-button">
                            <div class="theme-icon-container">
                                <i class="fas fa-sun theme-icon-light" title="Tema claro"></i>
                                <i class="fas fa-moon theme-icon-dark" title="Tema oscuro"></i>
                            </div>
                        </div>
                        <div class="theme-toggle-track">
                            <div class="theme-toggle-track-light">
                                <i class="fas fa-sun"></i>
                            </div>
                            <div class="theme-toggle-track-dark">
                                <i class="fas fa-moon"></i>
                            </div>
                        </div>
                    </div>
                </label>
            </div>
            <div class="theme-selector-tooltip">
                <span class="tooltip-text">' . htmlspecialchars($tooltip_text, ENT_QUOTES, 'UTF-8') . '</span>
            </div>
        </div>
    </div>';
}



// Función para limpiar número de WhatsApp
function cleanWhatsAppNumber($number) {
    if (empty($number)) return '';

    // Remover todo excepto números y el signo +
    $cleaned = preg_replace('/[^0-9+]/', '', $number);

    // Si no empieza con +, agregar + y código de país por defecto (Chile)
    if (!str_starts_with($cleaned, '+')) {
        $cleaned = '+56' . $cleaned;
    }

    return $cleaned;
}

// Función para validar URL de red social
function validateSocialUrl($url, $platform) {
    if (empty($url)) return true;

    $patterns = [
        'facebook' => '/^https?:\/\/(www\.)?facebook\.com\//',
        'instagram' => '/^https?:\/\/(www\.)?instagram\.com\//',
        'twitter' => '/^https?:\/\/(www\.)?twitter\.com\//',
        'youtube' => '/^https?:\/\/(www\.)?youtube\.com\//',
        'linkedin' => '/^https?:\/\/(www\.)?linkedin\.com\//',
        'tiktok' => '/^https?:\/\/(www\.)?tiktok\.com\//'
    ];

    if (isset($patterns[$platform])) {
        return preg_match($patterns[$platform], $url);
    }

    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

// Función para obtener reacciones de una publicación
function getPublicationReactions($publicacion_id) {
    $database = new Database();
    $conn = $database->getConnection();

    try {
        $query = "SELECT tipo_reaccion, COUNT(*) as count
                  FROM reacciones
                  WHERE publicacion_id = ?
                  GROUP BY tipo_reaccion";
        $stmt = $conn->prepare($query);
        $stmt->execute([$publicacion_id]);

        $reactions = [];
        while ($row = $stmt->fetch()) {
            $reactions[$row['tipo_reaccion']] = $row['count'];
        }

        return $reactions;
    } catch (Exception $e) {
        return [];
    }
}

// Función para verificar si un usuario reaccionó a una publicación
function hasUserReacted($publicacion_id, $agente_id) {
    $database = new Database();
    $conn = $database->getConnection();

    try {
        $query = "SELECT tipo_reaccion FROM reacciones WHERE publicacion_id = ? AND agente_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$publicacion_id, $agente_id]);

        $result = $stmt->fetch();
        return $result ? $result['tipo_reaccion'] : false;
    } catch (Exception $e) {
        return false;
    }
}

// Función para verificar y corregir la estructura de notificaciones
function verificarEstructuraNotificaciones() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Verificar si existe la tabla notificaciones
        $check_table = $conn->prepare("SHOW TABLES LIKE 'notificaciones'");
        $check_table->execute();

        if (!$check_table->fetch()) {
            // Crear tabla si no existe
            $create_table = "CREATE TABLE notificaciones (
                id INT AUTO_INCREMENT PRIMARY KEY,
                agente_id INT NOT NULL,
                tipo VARCHAR(50) NOT NULL DEFAULT 'general',
                mensaje TEXT NOT NULL,
                datos_json JSON NULL,
                leida BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE
            )";
            $conn->exec($create_table);
            return true;
        }

        // Verificar si existe la columna datos_json
        $check_column = $conn->prepare("SHOW COLUMNS FROM notificaciones LIKE 'datos_json'");
        $check_column->execute();

        if (!$check_column->fetch()) {
            // Agregar columna datos_json
            $add_column = "ALTER TABLE notificaciones ADD COLUMN datos_json JSON NULL AFTER mensaje";
            $conn->exec($add_column);
        }

        return true;
    } catch (Exception $e) {
        error_log("Error verificando estructura de notificaciones: " . $e->getMessage());
        return false;
    }
}

// Función para insertar notificación de forma segura
function insertarNotificacion($agente_id, $tipo, $mensaje, $datos_json = null) {
    try {
        // Verificar estructura primero
        verificarEstructuraNotificaciones();

        $database = new Database();
        $conn = $database->getConnection();

        // Verificar si existe la columna datos_json
        $check_column = $conn->prepare("SHOW COLUMNS FROM notificaciones LIKE 'datos_json'");
        $check_column->execute();

        if ($check_column->fetch() && $datos_json !== null) {
            // Insertar con datos_json
            $query = "INSERT INTO notificaciones (agente_id, tipo, mensaje, datos_json) VALUES (?, ?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->execute([$agente_id, $tipo, $mensaje, json_encode($datos_json)]);
        } else {
            // Insertar sin datos_json
            $query = "INSERT INTO notificaciones (agente_id, tipo, mensaje) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($query);
            $stmt->execute([$agente_id, $tipo, $mensaje]);
        }

        return true;
    } catch (Exception $e) {
        error_log("Error insertando notificación: " . $e->getMessage());
        return false;
    }
}

// Función para verificar y crear tabla de amistades
function verificarYCrearTablaAmistades($conn) {
    try {
        // Verificar si existe la tabla usando information_schema
        $tabla_existe = tablaExiste($conn, 'amistades');

        error_log("Debug verificarYCrearTablaAmistades - Tabla existe: " . ($tabla_existe ? 'SÍ' : 'NO'));

        if ($tabla_existe) {
            // Verificar que tenga la estructura correcta
            $columnas_requeridas = ['id', 'agente_solicitante_id', 'agente_receptor_id', 'estado', 'created_at', 'updated_at'];
            $estructura_correcta = verificarEstructuraTabla($conn, 'amistades', $columnas_requeridas);

            if ($estructura_correcta) {
                error_log("Debug verificarYCrearTablaAmistades - Retornando TRUE (tabla existe con estructura correcta)");
                return true;
            } else {
                error_log("Debug verificarYCrearTablaAmistades - Estructura incorrecta, recreando tabla");
                return recrearTablaAmistades($conn);
            }
        }

        // Crear la tabla de amistades
        $create_table = "CREATE TABLE amistades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_solicitante_id INT NOT NULL,
            agente_receptor_id INT NOT NULL,
            estado ENUM('pendiente', 'aceptada', 'rechazada', 'bloqueada') DEFAULT 'pendiente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (agente_solicitante_id) REFERENCES agentes(id) ON DELETE CASCADE,
            FOREIGN KEY (agente_receptor_id) REFERENCES agentes(id) ON DELETE CASCADE,
            UNIQUE KEY unique_friendship (agente_solicitante_id, agente_receptor_id)
        )";

        $conn->exec($create_table);

        // Crear índices para mejor performance
        $conn->exec("CREATE INDEX idx_amistades_solicitante ON amistades(agente_solicitante_id)");
        $conn->exec("CREATE INDEX idx_amistades_receptor ON amistades(agente_receptor_id)");
        $conn->exec("CREATE INDEX idx_amistades_estado ON amistades(estado)");

        error_log("Debug verificarYCrearTablaAmistades - Tabla creada exitosamente, retornando TRUE");
        return true;
    } catch (Exception $e) {
        error_log("Error creando tabla amistades: " . $e->getMessage());
        error_log("Debug verificarYCrearTablaAmistades - Error, retornando FALSE");
        return false;
    }
}

// Función para verificar y crear tabla de publicaciones
function verificarYCrearTablaPublicaciones($conn) {
    try {
        // Verificar si existe la tabla
        if (tablaExiste($conn, 'publicaciones')) {
            return true; // La tabla ya existe
        }

        // Crear la tabla de publicaciones
        $create_table = "CREATE TABLE publicaciones (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_id INT NOT NULL,
            contenido TEXT NOT NULL,
            imagen VARCHAR(255) NULL,
            tipo_privacidad ENUM('publico', 'amigos', 'privado') DEFAULT 'publico',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE
        )";

        $conn->exec($create_table);

        // Crear índices
        $conn->exec("CREATE INDEX idx_publicaciones_agente ON publicaciones(agente_id)");
        $conn->exec("CREATE INDEX idx_publicaciones_privacidad ON publicaciones(tipo_privacidad)");
        $conn->exec("CREATE INDEX idx_publicaciones_fecha ON publicaciones(created_at)");

        return true;
    } catch (Exception $e) {
        error_log("Error creando tabla publicaciones: " . $e->getMessage());
        return false;
    }
}

// Función para verificar y crear tabla de comentarios
function verificarYCrearTablaComentarios($conn) {
    try {
        // Verificar si existe la tabla
        if (tablaExiste($conn, 'comentarios')) {
            return true; // La tabla ya existe
        }

        // Crear la tabla de comentarios
        $create_table = "CREATE TABLE comentarios (
            id INT AUTO_INCREMENT PRIMARY KEY,
            publicacion_id INT NOT NULL,
            agente_id INT NOT NULL,
            contenido TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (publicacion_id) REFERENCES publicaciones(id) ON DELETE CASCADE,
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE
        )";

        $conn->exec($create_table);

        // Crear índices
        $conn->exec("CREATE INDEX idx_comentarios_publicacion ON comentarios(publicacion_id)");
        $conn->exec("CREATE INDEX idx_comentarios_agente ON comentarios(agente_id)");
        $conn->exec("CREATE INDEX idx_comentarios_fecha ON comentarios(created_at)");

        return true;
    } catch (Exception $e) {
        error_log("Error creando tabla comentarios: " . $e->getMessage());
        return false;
    }
}

// Función para verificar y crear tabla de reacciones
function verificarYCrearTablaReacciones($conn) {
    try {
        // Verificar si existe la tabla
        if (tablaExiste($conn, 'reacciones')) {
            return true; // La tabla ya existe
        }

        // Crear la tabla de reacciones
        $create_table = "CREATE TABLE reacciones (
            id INT AUTO_INCREMENT PRIMARY KEY,
            publicacion_id INT NOT NULL,
            agente_id INT NOT NULL,
            tipo_reaccion ENUM('like', 'dislike', 'love', 'laugh', 'sad', 'angry') NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (publicacion_id) REFERENCES publicaciones(id) ON DELETE CASCADE,
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE,
            UNIQUE KEY unique_reaction (publicacion_id, agente_id)
        )";

        $conn->exec($create_table);

        // Crear índices
        $conn->exec("CREATE INDEX idx_reacciones_publicacion ON reacciones(publicacion_id)");
        $conn->exec("CREATE INDEX idx_reacciones_agente ON reacciones(agente_id)");
        $conn->exec("CREATE INDEX idx_reacciones_tipo ON reacciones(tipo_reaccion)");

        return true;
    } catch (Exception $e) {
        error_log("Error creando tabla reacciones: " . $e->getMessage());
        return false;
    }
}

// Función para inicializar todo el sistema social automáticamente
function inicializarSistemaSocial() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Verificar estructura de notificaciones primero
        verificarEstructuraNotificaciones();

        // Crear todas las tablas necesarias
        $tablas_creadas = [
            'publicaciones' => verificarYCrearTablaPublicaciones($conn),
            'amistades' => verificarYCrearTablaAmistades($conn),
            'comentarios' => verificarYCrearTablaComentarios($conn),
            'reacciones' => verificarYCrearTablaReacciones($conn)
        ];

        // Verificar que todas las tablas se crearon correctamente
        $todas_creadas = array_reduce($tablas_creadas, function($carry, $item) {
            return $carry && $item;
        }, true);

        if ($todas_creadas) {
            error_log("Sistema social inicializado correctamente");
        } else {
            error_log("Error inicializando sistema social. Tablas: " . json_encode($tablas_creadas));
        }

        return $todas_creadas;
    } catch (Exception $e) {
        error_log("Error inicializando sistema social: " . $e->getMessage());
        return false;
    }
}

// Función para verificar estructura de tabla
function verificarEstructuraTabla($conn, $tabla, $columnas_requeridas) {
    try {
        if (!tablaExiste($conn, $tabla)) {
            return false;
        }

        // Obtener columnas existentes
        $desc = $conn->prepare("DESCRIBE $tabla");
        $desc->execute();
        $columnas_existentes = $desc->fetchAll(PDO::FETCH_COLUMN);

        // Verificar que todas las columnas requeridas existan
        foreach ($columnas_requeridas as $columna) {
            if (!in_array($columna, $columnas_existentes)) {
                error_log("Debug verificarEstructuraTabla - Tabla '$tabla' falta columna: $columna");
                return false;
            }
        }

        error_log("Debug verificarEstructuraTabla - Tabla '$tabla' tiene estructura correcta");
        return true;
    } catch (Exception $e) {
        error_log("Error verificando estructura de tabla '$tabla': " . $e->getMessage());
        return false;
    }
}

// Función para recrear tabla con estructura correcta
function recrearTablaAmistades($conn) {
    try {
        error_log("Debug recrearTablaAmistades - Iniciando recreación de tabla amistades");

        // Eliminar tabla existente si tiene estructura incorrecta
        $conn->exec("DROP TABLE IF EXISTS amistades");

        // Crear tabla con estructura correcta
        $create_table = "CREATE TABLE amistades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_solicitante_id INT NOT NULL,
            agente_receptor_id INT NOT NULL,
            estado ENUM('pendiente', 'aceptada', 'rechazada', 'bloqueada') DEFAULT 'pendiente',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (agente_solicitante_id) REFERENCES agentes(id) ON DELETE CASCADE,
            FOREIGN KEY (agente_receptor_id) REFERENCES agentes(id) ON DELETE CASCADE,
            UNIQUE KEY unique_friendship (agente_solicitante_id, agente_receptor_id)
        )";

        $conn->exec($create_table);

        // Crear índices
        $conn->exec("CREATE INDEX idx_amistades_solicitante ON amistades(agente_solicitante_id)");
        $conn->exec("CREATE INDEX idx_amistades_receptor ON amistades(agente_receptor_id)");
        $conn->exec("CREATE INDEX idx_amistades_estado ON amistades(estado)");

        error_log("Debug recrearTablaAmistades - Tabla recreada exitosamente");
        return true;
    } catch (Exception $e) {
        error_log("Error recreando tabla amistades: " . $e->getMessage());
        return false;
    }
}

// Función simple para verificar si una tabla existe
function tablaExiste($conn, $nombre_tabla) {
    try {
        $check = $conn->prepare("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?");
        $check->execute([$nombre_tabla]);
        $resultado = $check->fetch();
        $existe = $resultado && $resultado['count'] > 0;

        error_log("Debug tablaExiste - Tabla '$nombre_tabla': " . ($existe ? 'EXISTE' : 'NO EXISTE'));
        return $existe;
    } catch (Exception $e) {
        error_log("Error verificando tabla '$nombre_tabla': " . $e->getMessage());
        return false;
    }
}

// Función para verificar estado completo del sistema social
function verificarEstadoSistemaSocial() {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $tablas_requeridas = ['amistades', 'publicaciones', 'comentarios', 'reacciones'];
        $estado = [];

        foreach ($tablas_requeridas as $tabla) {
            $estado[$tabla] = tablaExiste($conn, $tabla);
        }

        $todas_existen = array_reduce($estado, function($carry, $item) {
            return $carry && $item;
        }, true);

        error_log("Debug verificarEstadoSistemaSocial - Estado: " . json_encode($estado));
        error_log("Debug verificarEstadoSistemaSocial - Todas existen: " . ($todas_existen ? 'SÍ' : 'NO'));

        return [
            'todas_existen' => $todas_existen,
            'estado_individual' => $estado
        ];
    } catch (Exception $e) {
        error_log("Error verificando estado del sistema social: " . $e->getMessage());
        return [
            'todas_existen' => false,
            'estado_individual' => [],
            'error' => $e->getMessage()
        ];
    }
}

// Función para crear notificación de amistad con datos adicionales
function createFriendshipNotification($agente_receptor_id, $agente_solicitante_id, $tipo_notificacion, $titulo, $mensaje, $url = null) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        // Obtener información del agente solicitante
        $agente_query = $conn->prepare("SELECT nombre, foto_perfil FROM agentes WHERE id = ?");
        $agente_query->execute([$agente_solicitante_id]);
        $agente_solicitante = $agente_query->fetch();

        if (!$agente_solicitante) {
            throw new Exception("Agente solicitante no encontrado");
        }

        // Crear datos adicionales para la notificación
        $datos_adicionales = json_encode([
            'tipo' => 'solicitud_amistad',
            'agente_solicitante_id' => $agente_solicitante_id,
            'agente_solicitante_nombre' => $agente_solicitante['nombre'],
            'agente_solicitante_avatar' => $agente_solicitante['foto_perfil'],
            'timestamp' => time()
        ]);

        // Insertar notificación con datos adicionales
        $query = "INSERT INTO notificaciones (agente_id, tipo, titulo, mensaje, url, datos_adicionales, created_at)
                  VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->execute([
            $agente_receptor_id,
            'social',
            $titulo,
            $mensaje,
            $url,
            $datos_adicionales
        ]);

        return true;
    } catch (Exception $e) {
        error_log("Error creando notificación de amistad: " . $e->getMessage());
        return false;
    }
}

// Función para obtener notificaciones de amistad con datos enriquecidos
function getFriendshipNotifications($agente_id, $limite = 10) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $query = "SELECT
            n.*,
            a.nombre as agente_solicitante_nombre,
            a.foto_perfil as agente_solicitante_avatar
            FROM notificaciones n
            LEFT JOIN JSON_EXTRACT(n.datos_adicionales, '$.agente_solicitante_id') as solicitante_id
            LEFT JOIN agentes a ON a.id = solicitante_id
            WHERE n.agente_id = ?
            AND n.tipo = 'social'
            AND n.titulo LIKE '%solicitud de amistad%'
            ORDER BY n.created_at DESC
            LIMIT ?";

        $stmt = $conn->prepare($query);
        $stmt->execute([$agente_id, $limite]);
        $notificaciones = $stmt->fetchAll();

        // Procesar datos adicionales
        foreach ($notificaciones as &$notificacion) {
            if ($notificacion['datos_adicionales']) {
                $datos = json_decode($notificacion['datos_adicionales'], true);
                $notificacion['datos_procesados'] = $datos;
            }
        }

        return $notificaciones;
    } catch (Exception $e) {
        error_log("Error obteniendo notificaciones de amistad: " . $e->getMessage());
        return [];
    }
}
?>