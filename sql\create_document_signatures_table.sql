-- Crear tabla para registrar firmas digitales de documentos
CREATE TABLE IF NOT EXISTS document_signatures (
    id INT AUTO_INCREMENT PRIMARY KEY,
    informativo_id INT NOT NULL,
    agente_id INT NOT NULL,
    action_type ENUM('read', 'signed') NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Índices para optimizar consultas
    INDEX idx_informativo_id (informativo_id),
    INDEX idx_agente_id (agente_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    
    -- Clave única para evitar duplicados
    UNIQUE KEY unique_signature (informativo_id, agente_id, action_type),
    
    -- Claves foráneas
    FOREIGN KEY (informativo_id) REFERENCES informativos(id) ON DELETE CASCADE,
    FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Comentarios sobre la tabla
ALTER TABLE document_signatures 
COMMENT = 'Registro de lecturas y firmas digitales de documentos informativos';

-- Insertar algunos datos de ejemplo (opcional)
-- INSERT INTO document_signatures (informativo_id, agente_id, action_type, ip_address, user_agent) 
-- VALUES 
-- (1, 1, 'read', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
-- (1, 1, 'signed', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
-- (1, 2, 'read', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

-- Vista para consultas rápidas de estadísticas
CREATE OR REPLACE VIEW document_signature_stats AS
SELECT 
    i.id as informativo_id,
    i.titulo as documento_titulo,
    i.tipo as documento_tipo,
    i.fecha_publicacion,
    COUNT(CASE WHEN ds.action_type = 'read' THEN 1 END) as total_lecturas,
    COUNT(CASE WHEN ds.action_type = 'signed' THEN 1 END) as total_firmas,
    COUNT(DISTINCT ds.agente_id) as agentes_unicos,
    MAX(ds.created_at) as ultima_actividad
FROM informativos i
LEFT JOIN document_signatures ds ON i.id = ds.informativo_id
GROUP BY i.id, i.titulo, i.tipo, i.fecha_publicacion
ORDER BY i.fecha_publicacion DESC;

-- Vista para obtener detalles de firmas por documento
CREATE OR REPLACE VIEW document_signature_details AS
SELECT 
    ds.id,
    ds.informativo_id,
    i.titulo as documento_titulo,
    i.tipo as documento_tipo,
    ds.agente_id,
    a.nombre as agente_nombre,
    a.apellido as agente_apellido,
    a.sabre_id as agente_sabre_id,
    a.email as agente_email,
    ds.action_type,
    ds.ip_address,
    ds.created_at,
    DATE_FORMAT(ds.created_at, '%d/%m/%Y %H:%i') as fecha_formateada
FROM document_signatures ds
JOIN informativos i ON ds.informativo_id = i.id
JOIN agentes a ON ds.agente_id = a.id
ORDER BY ds.created_at DESC;
