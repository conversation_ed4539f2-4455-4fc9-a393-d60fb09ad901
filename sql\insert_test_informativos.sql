-- Insertar documentos informativos de prueba
-- Asegúrate de que exista al menos un agente en la tabla agentes

-- Insertar algunos documentos de prueba
INSERT INTO informativos (titulo, contenido, tipo, archivo, fecha_publicacion, publicado_por) VALUES
('Briefing Operacional Enero 2024', 'Información importante sobre procedimientos operacionales para el mes de enero.', 'briefing', 'uploads/briefing-enero-2024.pdf', '2024-01-15 08:00:00', 1),
('Boletín Informativo Diciembre', 'Resumen de actividades y logros del equipo durante el mes de diciembre.', 'boletin', 'uploads/boletin-diciembre-2023.docx', '2023-12-28 10:30:00', 1),
('Circular de Seguridad', 'Nuevas medidas de seguridad implementadas en el aeropuerto.', 'circular', 'uploads/circular-seguridad-2024.pdf', '2024-01-10 14:15:00', 1),
('Manual de Procedimientos Actualizado', 'Versión actualizada del manual de procedimientos operacionales.', 'otro', 'uploads/manual-procedimientos-v2.pdf', '2024-01-05 09:45:00', 1),
('Diagrama de Procesos', 'Nuevo diagrama de flujo de procesos operacionales.', 'briefing', 'uploads/diagrama-procesos.png', '2024-01-12 16:20:00', 1);

-- Crear algunos archivos de prueba (esto es solo para referencia, los archivos reales deben existir)
-- En un entorno real, estos archivos deberían estar en la carpeta uploads/

-- Verificar que se insertaron correctamente
SELECT * FROM informativos ORDER BY fecha_publicacion DESC;

-- Mostrar información de debug
SELECT 
    i.id,
    i.titulo,
    i.tipo,
    i.archivo,
    i.fecha_publicacion,
    a.nombre as publicado_por_nombre
FROM informativos i
LEFT JOIN agentes a ON i.publicado_por = a.id
ORDER BY i.fecha_publicacion DESC;
