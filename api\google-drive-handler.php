<?php
/**
 * Manejador de archivos de Google Drive
 * Este archivo maneja la integración con Google Drive sin subir archivos a la web
 */

require_once '../includes/config.php';
require_once '../includes/functions.php';

header('Content-Type: application/json');

// Verificar que el usuario esté autenticado
session_start();
if (!isset($_SESSION['agente_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit;
}

// Verificar permisos
if (!hasPermission('manage_admin')) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Sin permisos suficientes']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        echo json_encode(['success' => false, 'message' => 'Datos inválidos']);
        exit;
    }
    
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'save_drive_file':
            saveDriveFileReference($input);
            break;
            
        case 'validate_drive_file':
            validateDriveFile($input);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Acción no válida']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
}

/**
 * Guardar referencia de archivo de Google Drive en la base de datos
 */
function saveDriveFileReference($data) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Validar datos requeridos
        $required_fields = ['titulo', 'tipo', 'fecha_publicacion', 'drive_file_id', 'drive_file_name', 'drive_file_url'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Campo requerido faltante: $field");
            }
        }
        
        // Preparar datos
        $titulo = trim($data['titulo']);
        $descripcion = trim($data['descripcion'] ?? '');
        $tipo = $data['tipo'];
        $fecha_publicacion = $data['fecha_publicacion'];
        $drive_file_id = $data['drive_file_id'];
        $drive_file_name = $data['drive_file_name'];
        $drive_file_url = $data['drive_file_url'];
        $subido_por = $_SESSION['agente_id'];
        
        // Validar tipo
        $tipos_validos = ['briefing', 'boletin', 'circular', 'otro'];
        if (!in_array($tipo, $tipos_validos)) {
            throw new Exception('Tipo de documento no válido');
        }
        
        // Validar fecha
        if (!validateDate($fecha_publicacion)) {
            throw new Exception('Fecha de publicación no válida');
        }
        
        // Insertar en la base de datos
        $query = "INSERT INTO informativos (
            titulo, 
            descripcion, 
            tipo, 
            fecha_publicacion, 
            archivo, 
            drive_file_id, 
            drive_file_name, 
            drive_file_url, 
            subido_por, 
            created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $conn->prepare($query);
        
        // Para archivos de Google Drive, usamos la URL como "archivo"
        $archivo_path = "drive://" . $drive_file_id;
        
        $stmt->execute([
            $titulo,
            $descripcion,
            $tipo,
            $fecha_publicacion,
            $archivo_path,
            $drive_file_id,
            $drive_file_name,
            $drive_file_url,
            $subido_por
        ]);
        
        $informativo_id = $conn->lastInsertId();
        
        // Registrar en el log
        logActivity($_SESSION['agente_id'], 'informativo_created', "Informativo creado desde Google Drive: $titulo");
        
        echo json_encode([
            'success' => true, 
            'message' => 'Informativo creado exitosamente desde Google Drive',
            'informativo_id' => $informativo_id
        ]);
        
    } catch (Exception $e) {
        error_log("Error al guardar archivo de Google Drive: " . $e->getMessage());
        echo json_encode([
            'success' => false, 
            'message' => 'Error al guardar: ' . $e->getMessage()
        ]);
    }
}

/**
 * Validar que el archivo de Google Drive sea accesible
 */
function validateDriveFile($data) {
    try {
        $drive_file_id = $data['drive_file_id'] ?? '';
        $access_token = $data['access_token'] ?? '';
        
        if (empty($drive_file_id) || empty($access_token)) {
            throw new Exception('ID de archivo o token de acceso faltante');
        }
        
        // Hacer una petición a la API de Google Drive para validar el archivo
        $url = "https://www.googleapis.com/drive/v3/files/$drive_file_id?fields=id,name,mimeType,size,webViewLink";
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => "Authorization: Bearer $access_token\r\n"
            ]
        ]);
        
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('No se pudo acceder al archivo de Google Drive');
        }
        
        $file_data = json_decode($response, true);
        
        if (!$file_data || isset($file_data['error'])) {
            throw new Exception('Archivo no encontrado o sin permisos de acceso');
        }
        
        // Validar tipo de archivo
        $allowed_types = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png'
        ];
        
        if (!in_array($file_data['mimeType'], $allowed_types)) {
            throw new Exception('Tipo de archivo no permitido');
        }
        
        // Validar tamaño (si está disponible)
        if (isset($file_data['size'])) {
            $max_size = 10 * 1024 * 1024; // 10MB
            if (intval($file_data['size']) > $max_size) {
                throw new Exception('El archivo es demasiado grande (máximo 10MB)');
            }
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Archivo validado exitosamente',
            'file_data' => $file_data
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error de validación: ' . $e->getMessage()
        ]);
    }
}

/**
 * Validar formato de fecha
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * Registrar actividad en el log
 */
function logActivity($agente_id, $action, $description) {
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        $query = "INSERT INTO activity_log (agente_id, action, description, created_at) VALUES (?, ?, ?, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->execute([$agente_id, $action, $description]);
    } catch (Exception $e) {
        error_log("Error al registrar actividad: " . $e->getMessage());
    }
}
?>
