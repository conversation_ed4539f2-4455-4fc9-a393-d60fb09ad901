<?php
// Test simple para verificar la funcionalidad de administración de agentes
session_start();

// Simular usuario logueado con permisos de administrador
$_SESSION['agente_id'] = 1;
$_SESSION['agente_nombre'] = 'Admin Test';

require_once 'config/functions.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Verificar conexión
    echo "<h2>✅ Conexión a base de datos exitosa</h2>";
    
    // Verificar tabla agentes
    $query = "SELECT COUNT(*) as total FROM agentes WHERE activo = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $total_agentes = $stmt->fetch()['total'];
    
    echo "<p><strong>Total de agentes activos:</strong> $total_agentes</p>";
    
    // Obtener algunos agentes de ejemplo
    $query = "SELECT a.*, g.nombre as grupo_nombre 
              FROM agentes a 
              LEFT JOIN grupos g ON a.grupo_id = g.id 
              WHERE a.activo = 1 
              LIMIT 5";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $agentes = $stmt->fetchAll();
    
    echo "<h3>Agentes de ejemplo:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Nombre</th><th>Usuario Sabre</th><th>Email</th><th>Grupo</th><th>Estado</th></tr>";
    
    foreach ($agentes as $ag) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($ag['id']) . "</td>";
        echo "<td>" . htmlspecialchars($ag['nombre']) . "</td>";
        echo "<td>" . htmlspecialchars($ag['usuario_sabre'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($ag['email']) . "</td>";
        echo "<td>" . htmlspecialchars($ag['grupo_nombre'] ?? 'Sin grupo') . "</td>";
        echo "<td>" . htmlspecialchars($ag['estado']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Verificar grupos disponibles
    $query = "SELECT * FROM grupos ORDER BY nombre";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $grupos = $stmt->fetchAll();
    
    echo "<h3>Grupos disponibles:</h3>";
    echo "<ul>";
    foreach ($grupos as $grupo) {
        echo "<li><strong>" . htmlspecialchars($grupo['nombre']) . "</strong> - " . htmlspecialchars($grupo['descripcion']) . "</li>";
    }
    echo "</ul>";
    
    echo "<hr>";
    echo "<h3>Enlaces de prueba:</h3>";
    echo "<p><a href='admin/agentes.php' target='_blank'>🔗 Ir a Administración de Agentes</a></p>";
    echo "<p><a href='admin.php' target='_blank'>🔗 Ir al Panel de Administración</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>
