<?php
header('Content-Type: application/json; charset=UTF-8');
require_once '../config/functions.php';

if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'No autorizado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Método no permitido']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);
$informativo_id = isset($input['informativo_id']) ? (int)$input['informativo_id'] : 0;
$action = isset($input['action']) ? $input['action'] : '';

if ($informativo_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'ID de documento inválido']);
    exit();
}

if (!in_array($action, ['read', 'sign'])) {
    echo json_encode(['success' => false, 'message' => 'Acción inválida']);
    exit();
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    $agente = getCurrentAgent();
    
    if (!$agente) {
        echo json_encode(['success' => false, 'message' => 'No se pudo obtener información del agente']);
        exit();
    }
    
    // Verificar que el documento existe
    $check_doc = "SELECT id, titulo FROM informativos WHERE id = ?";
    $stmt = $conn->prepare($check_doc);
    $stmt->execute([$informativo_id]);
    $documento = $stmt->fetch();
    
    if (!$documento) {
        echo json_encode(['success' => false, 'message' => 'Documento no encontrado']);
        exit();
    }
    
    // Verificar si ya existe un registro de lectura
    $check_query = "SELECT id, leido, firmado FROM informativo_lecturas 
                   WHERE informativo_id = ? AND agente_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->execute([$informativo_id, $agente['id']]);
    $existing = $check_stmt->fetch();
    
    if ($existing) {
        // Actualizar registro existente
        if ($action === 'read') {
            if ($existing['leido']) {
                echo json_encode(['success' => false, 'message' => 'Ya has marcado este documento como leído']);
                exit();
            }
            
            $update_query = "UPDATE informativo_lecturas 
                           SET leido = 1, fecha_lectura = NOW() 
                           WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $result = $update_stmt->execute([$existing['id']]);

            if ($result) {
                // Registrar en la tabla de firmas digitales
                recordDigitalSignature($conn, $informativo_id, $agente['id'], 'read');

                echo json_encode([
                    'success' => true,
                    'message' => 'Documento marcado como leído',
                    'action' => 'read'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al actualizar el estado de lectura']);
            }
            
        } elseif ($action === 'sign') {
            if ($existing['firmado']) {
                echo json_encode(['success' => false, 'message' => 'Ya has firmado este documento']);
                exit();
            }
            
            $update_query = "UPDATE informativo_lecturas 
                           SET firmado = 1, fecha_firma = NOW(), leido = 1, fecha_lectura = COALESCE(fecha_lectura, NOW())
                           WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $result = $update_stmt->execute([$existing['id']]);

            if ($result) {
                // Registrar en la tabla de firmas digitales
                recordDigitalSignature($conn, $informativo_id, $agente['id'], 'signed');

                echo json_encode([
                    'success' => true,
                    'message' => 'Documento firmado digitalmente',
                    'action' => 'sign'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al firmar el documento']);
            }
        }
        
    } else {
        // Crear nuevo registro
        if ($action === 'read') {
            $insert_query = "INSERT INTO informativo_lecturas 
                           (informativo_id, agente_id, leido, fecha_lectura, created_at) 
                           VALUES (?, ?, 1, NOW(), NOW())";
            $insert_stmt = $conn->prepare($insert_query);
            $result = $insert_stmt->execute([$informativo_id, $agente['id']]);

            if ($result) {
                // Registrar en la tabla de firmas digitales
                recordDigitalSignature($conn, $informativo_id, $agente['id'], 'read');

                echo json_encode([
                    'success' => true,
                    'message' => 'Documento marcado como leído',
                    'action' => 'read'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al marcar como leído']);
            }
            
        } elseif ($action === 'sign') {
            $insert_query = "INSERT INTO informativo_lecturas 
                           (informativo_id, agente_id, leido, firmado, fecha_lectura, fecha_firma, created_at) 
                           VALUES (?, ?, 1, 1, NOW(), NOW(), NOW())";
            $insert_stmt = $conn->prepare($insert_query);
            $result = $insert_stmt->execute([$informativo_id, $agente['id']]);

            if ($result) {
                // Registrar en la tabla de firmas digitales
                recordDigitalSignature($conn, $informativo_id, $agente['id'], 'signed');

                echo json_encode([
                    'success' => true,
                    'message' => 'Documento firmado digitalmente',
                    'action' => 'sign'
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Error al firmar el documento']);
            }
        }
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error interno: ' . $e->getMessage()]);
}

/**
 * Registrar firma digital en la tabla de auditoría
 */
function recordDigitalSignature($conn, $informativo_id, $agente_id, $action_type) {
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $query = "INSERT INTO document_signatures (informativo_id, agente_id, action_type, ip_address, user_agent)
                  VALUES (?, ?, ?, ?, ?)
                  ON DUPLICATE KEY UPDATE
                  ip_address = VALUES(ip_address),
                  user_agent = VALUES(user_agent),
                  created_at = CURRENT_TIMESTAMP";

        $stmt = $conn->prepare($query);
        $stmt->execute([$informativo_id, $agente_id, $action_type, $ip_address, $user_agent]);

        // Si es una firma, también registrar como leído automáticamente
        if ($action_type === 'signed') {
            $read_query = "INSERT INTO document_signatures (informativo_id, agente_id, action_type, ip_address, user_agent)
                          VALUES (?, ?, 'read', ?, ?)
                          ON DUPLICATE KEY UPDATE
                          ip_address = VALUES(ip_address),
                          user_agent = VALUES(user_agent)";
            $read_stmt = $conn->prepare($read_query);
            $read_stmt->execute([$informativo_id, $agente_id, $ip_address, $user_agent]);
        }

    } catch (Exception $e) {
        error_log("Error al registrar firma digital: " . $e->getMessage());
    }
}
?>
