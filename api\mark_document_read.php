<?php
/**
 * API para marcar documentos como leídos
 */

header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/config.php';
require_once '../config/functions.php';

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método no permitido'
    ]);
    exit;
}

try {
    // Verificar sesión
    session_start();
    if (!isset($_SESSION['agente_id'])) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'No autorizado'
        ]);
        exit;
    }

    $agente_id = $_SESSION['agente_id'];

    // Obtener datos JSON
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || !isset($input['documento_id']) || !isset($input['action'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Datos incompletos'
        ]);
        exit;
    }

    $documento_id = (int)$input['documento_id'];
    $action = $input['action'];

    // Conectar a la base de datos
    $database = new Database();
    $conn = $database->getConnection();

    // Verificar que el documento existe
    $doc_query = "SELECT id, titulo FROM informativos WHERE id = ?";
    $doc_stmt = $conn->prepare($doc_query);
    $doc_stmt->execute([$documento_id]);
    $documento = $doc_stmt->fetch();

    if (!$documento) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Documento no encontrado'
        ]);
        exit;
    }

    // Crear tabla si no existe
    $create_table_query = "
        CREATE TABLE IF NOT EXISTS document_reads (
            id INT AUTO_INCREMENT PRIMARY KEY,
            agente_id INT NOT NULL,
            documento_id INT NOT NULL,
            read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address VARCHAR(45),
            user_agent TEXT,
            UNIQUE KEY unique_read (agente_id, documento_id),
            FOREIGN KEY (agente_id) REFERENCES agentes(id) ON DELETE CASCADE,
            FOREIGN KEY (documento_id) REFERENCES informativos(id) ON DELETE CASCADE
        )
    ";
    $conn->exec($create_table_query);

    // Verificar si ya fue marcado como leído
    $check_query = "SELECT id FROM document_reads WHERE agente_id = ? AND documento_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->execute([$agente_id, $documento_id]);
    $existing = $check_stmt->fetch();

    if ($existing) {
        echo json_encode([
            'success' => true,
            'message' => 'Documento ya estaba marcado como leído',
            'already_read' => true
        ]);
        exit;
    }

    // Insertar registro de lectura
    $insert_query = "
        INSERT INTO document_reads (agente_id, documento_id, ip_address, user_agent) 
        VALUES (?, ?, ?, ?)
    ";
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->execute([
        $agente_id,
        $documento_id,
        $_SERVER['REMOTE_ADDR'] ?? null,
        $_SERVER['HTTP_USER_AGENT'] ?? null
    ]);

    // Log de la acción
    error_log("Documento {$documento_id} marcado como leído por agente {$agente_id}");

    echo json_encode([
        'success' => true,
        'message' => 'Documento marcado como leído correctamente',
        'documento_id' => $documento_id,
        'documento_titulo' => $documento['titulo'],
        'timestamp' => date('Y-m-d H:i:s')
    ]);

} catch (Exception $e) {
    error_log("Error en mark_document_read.php: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error interno del servidor',
        'error' => $e->getMessage()
    ]);
}
?>
