<?php
require_once '../config/functions.php';
requireLogin();
requirePermission('all_permissions');

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'ID de agente inválido']);
    exit;
}

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT a.*, g.nombre as grupo_nombre 
              FROM agentes a 
              LEFT JOIN grupos g ON a.grupo_id = g.id 
              WHERE a.id = ? AND a.activo = 1";
    $stmt = $conn->prepare($query);
    $stmt->execute([$_GET['id']]);
    $agent = $stmt->fetch();
    
    if ($agent) {
        echo json_encode([
            'success' => true,
            'agent' => $agent
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Agente no encontrado'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error al obtener los datos del agente: ' . $e->getMessage()
    ]);
}
?>
