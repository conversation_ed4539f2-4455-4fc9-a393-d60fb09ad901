<?php
/**
 * Script para configurar datos de prueba para el sistema de informativos
 */

require_once 'config/config.php';
require_once 'config/functions.php';

header('Content-Type: application/json; charset=UTF-8');

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Verificar si ya existen documentos
    $check_query = "SELECT COUNT(*) as count FROM informativos";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->execute();
    $existing_count = $check_stmt->fetch()['count'];
    
    $response = [
        'success' => true,
        'message' => 'Configuración completada',
        'existing_documents' => $existing_count,
        'actions_taken' => []
    ];
    
    // Si no hay documentos, crear algunos de prueba
    if ($existing_count == 0) {
        // Obtener el primer agente para usar como publicador
        $agent_query = "SELECT id FROM agentes LIMIT 1";
        $agent_stmt = $conn->prepare($agent_query);
        $agent_stmt->execute();
        $agent = $agent_stmt->fetch();
        
        if (!$agent) {
            throw new Exception('No hay agentes en el sistema. Crea al menos un agente primero.');
        }
        
        $publicado_por = $agent['id'];
        
        // Crear archivos de prueba
        $upload_dir = 'uploads/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $test_files = [
            'briefing-enero-2024.pdf' => 'Contenido del briefing operacional de enero 2024',
            'boletin-diciembre-2023.docx' => 'Contenido del boletín informativo de diciembre',
            'circular-seguridad-2024.pdf' => 'Contenido de la circular de seguridad',
            'manual-procedimientos-v2.pdf' => 'Contenido del manual de procedimientos actualizado',
            'diagrama-procesos.png' => 'Contenido del diagrama de procesos'
        ];
        
        foreach ($test_files as $filename => $content) {
            $filepath = $upload_dir . $filename;
            if (!file_exists($filepath)) {
                file_put_contents($filepath, $content);
                $response['actions_taken'][] = "Archivo creado: $filename";
            }
        }
        
        // Insertar documentos de prueba
        $test_documents = [
            [
                'titulo' => 'Briefing Operacional Enero 2024',
                'contenido' => 'Información importante sobre procedimientos operacionales para el mes de enero.',
                'tipo' => 'briefing',
                'archivo' => 'uploads/briefing-enero-2024.pdf'
            ],
            [
                'titulo' => 'Boletín Informativo Diciembre',
                'contenido' => 'Resumen de actividades y logros del equipo durante el mes de diciembre.',
                'tipo' => 'boletin',
                'archivo' => 'uploads/boletin-diciembre-2023.docx'
            ],
            [
                'titulo' => 'Circular de Seguridad',
                'contenido' => 'Nuevas medidas de seguridad implementadas en el aeropuerto.',
                'tipo' => 'circular',
                'archivo' => 'uploads/circular-seguridad-2024.pdf'
            ],
            [
                'titulo' => 'Manual de Procedimientos Actualizado',
                'contenido' => 'Versión actualizada del manual de procedimientos operacionales.',
                'tipo' => 'otro',
                'archivo' => 'uploads/manual-procedimientos-v2.pdf'
            ],
            [
                'titulo' => 'Diagrama de Procesos',
                'contenido' => 'Nuevo diagrama de flujo de procesos operacionales.',
                'tipo' => 'briefing',
                'archivo' => 'uploads/diagrama-procesos.png'
            ]
        ];
        
        $insert_query = "INSERT INTO informativos (titulo, contenido, tipo, archivo, fecha_publicacion, publicado_por) VALUES (?, ?, ?, ?, NOW(), ?)";
        $insert_stmt = $conn->prepare($insert_query);
        
        foreach ($test_documents as $doc) {
            $insert_stmt->execute([
                $doc['titulo'],
                $doc['contenido'],
                $doc['tipo'],
                $doc['archivo'],
                $publicado_por
            ]);
            $response['actions_taken'][] = "Documento creado: " . $doc['titulo'];
        }
        
        $response['message'] = 'Datos de prueba creados exitosamente';
        $response['documents_created'] = count($test_documents);
        
    } else {
        $response['message'] = 'Ya existen documentos en el sistema';
        $response['documents_created'] = 0;
    }
    
    // Obtener lista actual de documentos
    $docs_query = "SELECT id, titulo, tipo, archivo, fecha_publicacion FROM informativos ORDER BY fecha_publicacion DESC";
    $docs_stmt = $conn->prepare($docs_query);
    $docs_stmt->execute();
    $documents = $docs_stmt->fetchAll();
    
    $response['current_documents'] = $documents;
    
    // Verificar que las tablas de firmas existan
    $tables_to_check = ['document_signatures'];
    foreach ($tables_to_check as $table) {
        $table_check = "SHOW TABLES LIKE '$table'";
        $table_stmt = $conn->prepare($table_check);
        $table_stmt->execute();
        $table_exists = $table_stmt->fetch();
        
        if (!$table_exists) {
            $response['warnings'][] = "Tabla $table no existe. Ejecuta el script de creación de tablas.";
        }
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
