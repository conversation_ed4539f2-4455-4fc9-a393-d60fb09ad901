<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

// Verificar permisos del usuario
$can_assign = canManageAssignments();
$can_request = !$can_assign; // Los que no pueden asignar, pueden solicitar

// Obtener notificaciones
$notificaciones = getNotifications($agente['id']);

// Obtener todos los agentes para asignaciones
$agentes_query = "SELECT a.id, a.nombre, a.usuario_sabre, a.estado, a.foto_perfil, g.nombre as grupo_nombre
                  FROM agentes a
                  LEFT JOIN grupos g ON a.grupo_id = g.id
                  WHERE a.activo = 1
                  ORDER BY a.nombre";
$agentes_stmt = $conn->prepare($agentes_query);
$agentes_stmt->execute();
$todos_agentes = $agentes_stmt->fetchAll();

// Obtener terminales
$terminales_query = "SELECT * FROM terminales ORDER BY nombre";
$terminales_stmt = $conn->prepare($terminales_query);
$terminales_stmt->execute();
$terminales = $terminales_stmt->fetchAll();

// Obtener funciones de turno
$funciones_query = "SELECT * FROM funciones_turno ORDER BY nombre";
$funciones_stmt = $conn->prepare($funciones_query);
$funciones_stmt->execute();
$funciones = $funciones_stmt->fetchAll();



// Obtener asignaciones del día actual
try {
    // Intentar primero con asignado_por
    $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                  t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                  ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                  asignador.nombre as asignado_por_nombre
                           FROM asignaciones a
                           JOIN funciones_turno f ON a.funcion_id = f.id
                           JOIN terminales t ON a.terminal_id = t.id
                           JOIN agentes ag ON a.agente_id = ag.id
                           LEFT JOIN agentes asignador ON a.asignado_por = asignador.id
                           WHERE a.fecha = CURDATE()
                           ORDER BY t.nombre, f.nombre";
    $asignaciones_stmt = $conn->prepare($asignaciones_query);
    $asignaciones_stmt->execute();
    $asignaciones_hoy = $asignaciones_stmt->fetchAll();
} catch (Exception $e) {
    // Si falla, intentar con created_by
    try {
        $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                      t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                      ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                      asignador.nombre as asignado_por_nombre
                               FROM asignaciones a
                               JOIN funciones_turno f ON a.funcion_id = f.id
                               JOIN terminales t ON a.terminal_id = t.id
                               JOIN agentes ag ON a.agente_id = ag.id
                               LEFT JOIN agentes asignador ON a.created_by = asignador.id
                               WHERE a.fecha = CURDATE()
                               ORDER BY t.nombre, f.nombre";
        $asignaciones_stmt = $conn->prepare($asignaciones_query);
        $asignaciones_stmt->execute();
        $asignaciones_hoy = $asignaciones_stmt->fetchAll();
    } catch (Exception $e2) {
        // Si ambas fallan, obtener sin el asignador
        $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                      t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                      ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                      'Sistema' as asignado_por_nombre
                               FROM asignaciones a
                               JOIN funciones_turno f ON a.funcion_id = f.id
                               JOIN terminales t ON a.terminal_id = t.id
                               JOIN agentes ag ON a.agente_id = ag.id
                               WHERE a.fecha = CURDATE()
                               ORDER BY t.nombre, f.nombre";
        $asignaciones_stmt = $conn->prepare($asignaciones_query);
        $asignaciones_stmt->execute();
        $asignaciones_hoy = $asignaciones_stmt->fetchAll();
        error_log("Error en consulta de asignaciones: " . $e->getMessage() . " | " . $e2->getMessage());
    }
}

// Obtener estadísticas de agentes
$stats_query = "SELECT
    COUNT(CASE WHEN estado = '🟢Disponible' THEN 1 END) as disponibles,
    COUNT(CASE WHEN estado = '🟡En Colación' THEN 1 END) as en_colacion,
    COUNT(CASE WHEN estado = '🔴Ocupado' THEN 1 END) as ocupados,
    COUNT(CASE WHEN estado NOT IN ('🟢Disponible', '🟡En Colación', '🔴Ocupado') THEN 1 END) as fuera_turno
    FROM agentes WHERE activo = 1";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->execute();
$stats_agentes = $stats_stmt->fetch();

// Obtener agentes con asignaciones hoy
$agentes_con_asignaciones = array_unique(array_column($asignaciones_hoy, 'agente_id'));

// Obtener agentes disponibles sin asignaciones
$agentes_disponibles = array_filter($todos_agentes, function($agente) use ($agentes_con_asignaciones) {
    return $agente['estado'] === '🟢Disponible' && !in_array($agente['id'], $agentes_con_asignaciones);
});

// Obtener agentes en colación
$agentes_colacion = array_filter($todos_agentes, function($agente) {
    return $agente['estado'] === '🟡En Colación';
});

// Obtener solicitudes pendientes (solo para supervisores)
$solicitudes_pendientes = [];
if ($can_assign) {
    $solicitudes_query = "SELECT sa.*, a.nombre as agente_nombre, a.usuario_sabre, a.foto_perfil,
                                f.nombre as funcion_nombre, t.nombre as terminal_nombre
                         FROM solicitudes_asignacion sa
                         JOIN agentes a ON sa.agente_id = a.id
                         JOIN funciones_turno f ON sa.funcion_id = f.id
                         JOIN terminales t ON sa.terminal_id = t.id
                         WHERE sa.estado = 'pendiente'
                         ORDER BY sa.solicitado_en DESC";
    $solicitudes_stmt = $conn->prepare($solicitudes_query);
    $solicitudes_stmt->execute();
    $solicitudes_pendientes = $solicitudes_stmt->fetchAll();
}

// Procesar formulario de asignación
$message = '';
$error = '';



if ($_POST) {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'assign') {
            $agente_id = $_POST['agente_id'];
            $funcion_id = $_POST['funcion_id'];
            $terminal_id = $_POST['terminal_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';

            try {
                // Intentar primero con asignado_por
                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, asignado_por)
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);

                if (!$insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']])) {
                    // Si falla, intentar con created_by
                    $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                }

                // Obtener información para la notificación
                $funcion_info = $conn->prepare("SELECT nombre FROM funciones_turno WHERE id = ?");
                $funcion_info->execute([$funcion_id]);
                $funcion = $funcion_info->fetch();

                $terminal_info = $conn->prepare("SELECT nombre FROM terminales WHERE id = ?");
                $terminal_info->execute([$terminal_id]);
                $terminal = $terminal_info->fetch();

                // Enviar notificación al agente asignado
                if ($funcion && $terminal) {
                    sendAssignmentNotification(
                        $agente_id,
                        $funcion['nombre'],
                        $terminal['nombre'],
                        $turno,
                        $fecha,
                        $agente['id']
                    );
                }

                $message = "Asignación creada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?success=1");
                exit;
            } catch (Exception $e) {
                // Si falla con asignado_por, intentar con created_by
                try {
                    $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);

                    // Obtener información para la notificación
                    $funcion_info = $conn->prepare("SELECT nombre FROM funciones_turno WHERE id = ?");
                    $funcion_info->execute([$funcion_id]);
                    $funcion = $funcion_info->fetch();

                    $terminal_info = $conn->prepare("SELECT nombre FROM terminales WHERE id = ?");
                    $terminal_info->execute([$terminal_id]);
                    $terminal = $terminal_info->fetch();

                    // Enviar notificación al agente asignado
                    if ($funcion && $terminal) {
                        sendAssignmentNotification(
                            $agente_id,
                            $funcion['nombre'],
                            $terminal['nombre'],
                            $turno,
                            $fecha,
                            $agente['id']
                        );
                    }

                    $message = "Asignación creada exitosamente";
                    header("Location: asignaciones.php?success=1");
                    exit;
                } catch (Exception $e2) {
                    $error = "Error al crear la asignación: " . $e->getMessage() . " | " . $e2->getMessage();
                    error_log("Error en asignación: " . $e->getMessage() . " | " . $e2->getMessage());
                }
            }
        } elseif ($_POST['action'] === 'remove') {
            $asignacion_id = $_POST['asignacion_id'];

            try {
                $delete_query = "DELETE FROM asignaciones WHERE id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->execute([$asignacion_id]);

                $message = "Asignación eliminada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?removed=1");
                exit;
            } catch (Exception $e) {
                $error = "Error al eliminar la asignación: " . $e->getMessage();
            }
        } elseif ($_POST['action'] === 'bulk_assign') {
            $terminal_id = $_POST['terminal_id'];
            $funcion_id = $_POST['funcion_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';
            $agent_ids = explode(',', $_POST['agent_ids']);

            try {
                $conn->beginTransaction();

                // Intentar primero con asignado_por
                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, asignado_por)
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);

                $success_count = 0;
                $error_count = 0;
                $use_created_by = false;

                // Obtener información para las notificaciones (una sola vez)
                $funcion_info = $conn->prepare("SELECT nombre FROM funciones_turno WHERE id = ?");
                $funcion_info->execute([$funcion_id]);
                $funcion = $funcion_info->fetch();

                $terminal_info = $conn->prepare("SELECT nombre FROM terminales WHERE id = ?");
                $terminal_info->execute([$terminal_id]);
                $terminal = $terminal_info->fetch();

                foreach ($agent_ids as $agent_id) {
                    if (empty(trim($agent_id))) continue;

                    try {
                        if (!$use_created_by) {
                            $result = $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                            if (!$result) {
                                // Si falla, cambiar a created_by
                                $use_created_by = true;
                                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                            }
                        } else {
                            $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                        }

                        // Enviar notificación al agente asignado
                        if ($funcion && $terminal) {
                            sendAssignmentNotification(
                                $agent_id,
                                $funcion['nombre'],
                                $terminal['nombre'],
                                $turno,
                                $fecha,
                                $agente['id']
                            );
                        }

                        $success_count++;
                    } catch (Exception $e) {
                        // Si es el primer error y no hemos probado created_by, intentarlo
                        if (!$use_created_by && $error_count == 0) {
                            try {
                                $use_created_by = true;
                                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);

                                // Enviar notificación al agente asignado
                                if ($funcion && $terminal) {
                                    sendAssignmentNotification(
                                        $agent_id,
                                        $funcion['nombre'],
                                        $terminal['nombre'],
                                        $turno,
                                        $fecha,
                                        $agente['id']
                                    );
                                }

                                $success_count++;
                            } catch (Exception $e2) {
                                $error_count++;
                                error_log("Error en asignación masiva para agente $agent_id: " . $e->getMessage() . " | " . $e2->getMessage());
                            }
                        } else {
                            $error_count++;
                            error_log("Error en asignación masiva para agente $agent_id: " . $e->getMessage());
                        }
                    }
                }

                $conn->commit();

                if ($success_count > 0) {
                    $message = "Se crearon $success_count asignaciones exitosamente";
                    if ($error_count > 0) {
                        $message .= " ($error_count fallaron)";
                    }
                } else {
                    $error = "No se pudo crear ninguna asignación";
                }

                // Recargar datos
                header("Location: asignaciones.php?bulk_success=$success_count&bulk_errors=$error_count");
                exit;

            } catch (Exception $e) {
                $conn->rollBack();
                $error = "Error en asignaciones masivas: " . $e->getMessage();
                error_log("Error en transacción masiva: " . $e->getMessage());
            }
        } elseif ($_POST['action'] === 'request_assignment' && $can_request) {
            // Procesar solicitud de asignación
            $funcion_id = $_POST['funcion_id'];
            $terminal_id = $_POST['terminal_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';

            $result = createAssignmentRequest($agente['id'], $funcion_id, $terminal_id, $turno, $fecha, $observaciones);

            if ($result['success']) {
                header("Location: asignaciones.php?request_sent=1");
                exit;
            } else {
                $error = $result['message'];
            }
        } elseif ($_POST['action'] === 'process_request' && $can_assign) {
            // Procesar solicitud (aprobar/rechazar)
            $solicitud_id = $_POST['solicitud_id'];
            $accion = $_POST['decision']; // 'aprobar' o 'rechazar'
            $motivo_rechazo = $_POST['motivo_rechazo'] ?? '';

            $result = processAssignmentRequest($solicitud_id, $accion, $agente['id'], $motivo_rechazo);

            if ($result['success']) {
                $message = $result['message'];
                header("Location: asignaciones.php?processed=1&message=" . urlencode($message));
                exit;
            } else {
                $error = $result['message'];
            }
        }
    }
}

if (isset($_GET['success'])) {
    $message = "Asignación creada exitosamente";
}
if (isset($_GET['removed'])) {
    $message = "Asignación eliminada exitosamente";
}
if (isset($_GET['bulk_success'])) {
    $success_count = (int)$_GET['bulk_success'];
    $error_count = (int)($_GET['bulk_errors'] ?? 0);

    if ($success_count > 0) {
        $message = "Se crearon $success_count asignaciones exitosamente";
        if ($error_count > 0) {
            $message .= " ($error_count fallaron)";
        }
    } else {
        $error = "No se pudo crear ninguna asignación masiva";
    }
}
if (isset($_GET['request_sent'])) {
    $message = "Solicitud de asignación enviada exitosamente";
}
if (isset($_GET['processed'])) {
    $message = $_GET['message'] ?? "Solicitud procesada exitosamente";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asignaciones de Turno - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --gradient-admin: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            color: #2d3748;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-soft);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            margin: 20px;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            filter: brightness(0) invert(1);
        }

        .user-info {
            padding: 25px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            z-index: 0;
        }

        .user-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .user-name {
            color: #2d3748;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .user-group {
            color: #718096;
            position: relative;
            z-index: 1;
        }

        .status-badge {
            position: relative;
            z-index: 1;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.status-disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .status-badge.status-colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .status-badge.status-ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .status-badge.status-fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
            color: white;
        }

        .sidebar-nav {
            padding: 0 20px 20px;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            color: #4a5568;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid transparent;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            opacity: 1;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .nav-link i {
            width: 20px;
            margin-right: 15px;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* ===== TOP BAR ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .top-bar > * {
            position: relative;
            z-index: 1;
        }

        /* ===== NOTIFICATION BELL ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .notification-bell:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .notification-counter {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .notifications-panel {
            position: absolute;
            top: 60px;
            right: 0;
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-hover);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .notifications-panel.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notifications-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
            color: #2d3748;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME SELECTOR ===== */
        .theme-selector {
            display: flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .theme-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin: 0 2px;
        }

        .theme-btn.light {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .theme-btn.dark {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: #ddd;
        }

        .theme-btn.active {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* ===== STATS CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .stat-card.disponibles::before {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.asignados::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.colacion::before {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-card.fuera::before {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-card > * {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon.disponibles {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-icon.asignados {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-icon.fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 16px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* ===== AGENT CARDS ===== */
        .agents-section {
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .agent-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
            object-fit: cover;
        }

        .agent-details h6 {
            margin: 0;
            font-weight: 600;
            color: #2d3748;
        }

        .agent-details small {
            color: #6b7280;
            font-weight: 500;
        }

        .agent-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .agent-status.disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .agent-status.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .agent-status.ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .assignment-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 12px;
            margin-top: 10px;
        }

        .assignment-info h6 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 600;
        }

        .assignment-info p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }

        /* ===== MANAGEMENT PANEL ===== */
        .management-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .management-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .management-panel > * {
            position: relative;
            z-index: 1;
        }

        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .management-title-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .management-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .management-text h3 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .management-text p {
            margin: 5px 0 0 0;
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
        }

        .management-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn-new-assignment,
        .btn-bulk-actions {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
            min-width: 280px;
        }

        .btn-new-assignment::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .btn-bulk-actions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .btn-new-assignment:hover::before,
        .btn-bulk-actions:hover::before {
            opacity: 1;
        }

        .btn-new-assignment:hover,
        .btn-bulk-actions:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-new-assignment > *,
        .btn-bulk-actions > * {
            position: relative;
            z-index: 1;
        }

        .btn-icon {
            width: 50px;
            height: 50px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-new-assignment:hover .btn-icon,
        .btn-bulk-actions:hover .btn-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: scale(1.1);
        }

        .btn-text {
            flex: 1;
        }

        .btn-title {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
            transition: color 0.3s ease;
        }

        .btn-subtitle {
            display: block;
            font-size: 14px;
            color: #6b7280;
            transition: color 0.3s ease;
        }

        .btn-new-assignment:hover .btn-title,
        .btn-new-assignment:hover .btn-subtitle,
        .btn-bulk-actions:hover .btn-title,
        .btn-bulk-actions:hover .btn-subtitle {
            color: white;
        }

        .btn-arrow {
            font-size: 16px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-new-assignment:hover .btn-arrow,
        .btn-bulk-actions:hover .btn-arrow {
            color: white;
            transform: translateX(5px);
        }

        .management-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-item .stat-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .stat-item .stat-icon.disponibles {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-item .stat-icon.asignados {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-item .stat-icon.terminales {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-item .stat-icon.funciones {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* ===== ACTION BUTTONS ===== */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-assign {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-assign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-remove {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-remove:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
        }

        /* ===== ASSIGNMENT MODAL ===== */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }

        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .sidebar-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .user-info {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #f7fafc !important;
        }

        .theme-dark .user-name {
            color: #f7fafc !important;
        }

        .theme-dark .user-group {
            color: #cbd5e0 !important;
        }

        .theme-dark .nav-link {
            color: #e2e8f0 !important;
        }

        .theme-dark .nav-link:hover,
        .theme-dark .nav-link.active {
            color: white !important;
        }

        .theme-dark .top-bar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-number {
            color: #f7fafc !important;
        }

        .theme-dark .stat-label {
            color: #cbd5e0 !important;
        }

        .theme-dark .agent-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .agent-details h6 {
            color: #f7fafc !important;
        }

        .theme-dark .agent-details small {
            color: #cbd5e0 !important;
        }

        .theme-dark .section-title {
            color: #f7fafc !important;
        }

        .theme-dark .assignment-info {
            background: rgba(102, 126, 234, 0.2) !important;
        }

        .theme-dark .assignment-info h6 {
            color: #81e6d9 !important;
        }

        .theme-dark .assignment-info p {
            color: #cbd5e0 !important;
        }

        .theme-dark .management-panel {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .management-text h3 {
            color: #f7fafc !important;
            -webkit-text-fill-color: #f7fafc !important;
        }

        .theme-dark .management-text p {
            color: #cbd5e0 !important;
        }

        .theme-dark .btn-new-assignment,
        .theme-dark .btn-bulk-actions {
            background: rgba(26, 32, 44, 0.9) !important;
            color: #f7fafc !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .btn-title {
            color: #f7fafc !important;
        }

        .theme-dark .btn-subtitle {
            color: #cbd5e0 !important;
        }

        .theme-dark .stat-item {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .stat-number {
            color: #f7fafc !important;
        }

        .theme-dark .stat-label {
            color: #cbd5e0 !important;
        }

        /* ===== NOTIFICATIONS PANEL STYLES ===== */
        .notifications-panel {
            position: fixed;
            top: 0;
            right: -450px;
            width: 420px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 10000;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
        }

        .notifications-panel.show {
            right: 0;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(2px);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-header {
            padding: 30px 25px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .notifications-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notifications-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .notifications-actions {
            display: flex;
            gap: 10px;
        }

        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .notifications-list {
            padding: 0;
        }

        .notification-item {
            padding: 20px 25px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .notification-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-item.unread::before {
            opacity: 1;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(5px);
        }

        .notification-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .notification-icon.operacional {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .notification-icon.social {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .notification-icon.urgente {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .notification-icon.informativo {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .notification-icon.general {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            line-height: 1.3;
        }

        .notification-message {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 11px;
            color: #9ca3af;
            font-weight: 500;
        }

        .empty-notifications {
            text-align: center;
            padding: 60px 25px;
            color: #6b7280;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .empty-notifications h6 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #4a5568;
        }

        .empty-notifications p {
            margin: 0;
            font-size: 14px;
        }

        /* Dark theme notifications */
        .theme-dark .notifications-panel {
            background: rgba(26, 32, 44, 0.98) !important;
            border-left-color: #4a5568 !important;
        }

        .theme-dark .notifications-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .notification-item {
            border-bottom-color: #4a5568 !important;
        }

        .theme-dark .notification-item:hover {
            background: rgba(102, 126, 234, 0.1) !important;
        }

        .theme-dark .notification-title {
            color: #f7fafc !important;
        }

        .theme-dark .notification-message {
            color: #cbd5e0 !important;
        }

        .theme-dark .notification-time {
            color: #a0aec0 !important;
        }

        .theme-dark .empty-notifications h6 {
            color: #f7fafc !important;
        }

        .theme-dark .empty-notifications p {
            color: #cbd5e0 !important;
        }

        /* ===== BULK ASSIGNMENT MODAL ===== */
        #bulkAssignModal .modal-dialog {
            max-width: 1200px;
        }

        #bulkAssignModal .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
        }

        #bulkAssignModal .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 25px 30px;
        }

        #bulkAssignModal .modal-title {
            font-size: 24px;
            font-weight: 700;
        }

        #bulkAssignModal .modal-body {
            padding: 30px;
        }

        /* Tabs Navigation */
        .nav-pills .nav-link {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            border-radius: 12px;
            padding: 12px 20px;
            margin-right: 10px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .nav-pills .nav-link:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .nav-pills .nav-link.active {
            background: var(--gradient-primary);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* Filter Panel */
        .filter-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .filter-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .filter-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .selection-summary {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .summary-label {
            font-weight: 600;
            color: #4a5568;
        }

        .summary-count {
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Agents Selection Panel */
        .agents-selection-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            max-height: 600px;
            overflow-y: auto;
        }

        .panel-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        .agent-selection-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .agent-selection-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .agent-selection-card.selected {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .selection-checkbox {
            position: relative;
        }

        .agent-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .agent-avatar-section {
            position: relative;
        }

        .agent-avatar-bulk {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            object-fit: cover;
        }

        .agent-status-bulk {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .agent-status-bulk.status-disponible {
            background: #48bb78;
        }

        .agent-status-bulk.status-colacion {
            background: #ed8936;
        }

        .agent-status-bulk.status-ocupado {
            background: #f56565;
        }

        .agent-status-bulk.status-fuera {
            background: #a0aec0;
        }

        .agent-info-bulk {
            flex: 1;
        }

        .agent-name-bulk {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .agent-sabre-bulk,
        .agent-grupo-bulk {
            margin: 0 0 3px 0;
            font-size: 12px;
            color: #6b7280;
        }

        /* Assignment Config Panel */
        .assignment-config-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            height: fit-content;
        }

        /* Selected Agents Panel */
        .selected-agents-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            max-height: 500px;
            overflow-y: auto;
        }

        .selected-agents-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .empty-selection {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .selected-agent-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .selected-agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
        }

        .selected-agent-info h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
        }

        .selected-agent-info small {
            color: #6b7280;
        }

        /* Review Panel */
        .review-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .assignment-summary {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .summary-section {
            margin-bottom: 20px;
        }

        .summary-section h6 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .summary-card .label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .summary-card .value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .confirmation-actions {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding-top: 25px;
        }

        /* Step Navigation */
        .step-navigation {
            display: flex;
            gap: 10px;
        }

        /* Dark Theme for Bulk Modal */
        .theme-dark #bulkAssignModal .modal-content {
            background: #2d3748 !important;
            color: #f7fafc !important;
        }

        .theme-dark .filter-panel,
        .theme-dark .agents-selection-panel,
        .theme-dark .assignment-config-panel,
        .theme-dark .selected-agents-panel,
        .theme-dark .review-panel {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .filter-title,
        .theme-dark .panel-title {
            color: #f7fafc !important;
        }

        .theme-dark .agent-selection-card {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
        }

        .theme-dark .agent-name-bulk {
            color: #f7fafc !important;
        }

        .theme-dark .agent-sabre-bulk,
        .theme-dark .agent-grupo-bulk {
            color: #cbd5e0 !important;
        }

        .theme-dark .summary-card {
            background: rgba(45, 55, 72, 0.8) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .summary-card .value {
            color: #f7fafc !important;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                z-index: 9999;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .agents-grid {
                grid-template-columns: 1fr;
            }

            .top-bar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }

        /* ===== REQUEST PANEL ===== */
        .request-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .request-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(139, 195, 74, 0.02) 100%);
            z-index: 0;
        }

        .request-panel > * {
            position: relative;
            z-index: 1;
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(76, 175, 80, 0.1);
        }

        .request-title-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .request-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        .request-title-content h3 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .request-title-content p {
            margin: 5px 0 0 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .request-form-container {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 25px;
            border: 1px solid rgba(76, 175, 80, 0.1);
        }

        .request-form .btn-primary {
            background: linear-gradient(135deg, #4caf50, #8bc34a);
            border: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .request-form .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(76, 175, 80, 0.3);
        }

        /* ===== ULTRA MODERN REQUEST PANEL ===== */
        .ultra-modern-request-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(30px);
            border-radius: 32px;
            margin-bottom: 50px;
            box-shadow:
                0 30px 80px rgba(0, 0, 0, 0.12),
                0 15px 40px rgba(0, 0, 0, 0.08),
                inset 0 2px 0 rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.4);
            position: relative;
            overflow: hidden;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            animation: panelEntrance 1s ease-out;
        }

        @keyframes panelEntrance {
            from { opacity: 0; transform: translateY(50px) scale(0.95); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }

        .ultra-modern-request-panel:hover {
            transform: translateY(-8px);
            box-shadow:
                0 40px 100px rgba(0, 0, 0, 0.15),
                0 20px 50px rgba(0, 0, 0, 0.1),
                inset 0 2px 0 rgba(255, 255, 255, 1);
        }

        /* Header ultra moderno con gradiente rainbow */
        .ultra-modern-header {
            position: relative;
            background: linear-gradient(135deg,
                #667eea 0%,
                #764ba2 15%,
                #f093fb 30%,
                #f5576c 45%,
                #4facfe 60%,
                #00f2fe 75%,
                #43e97b 90%,
                #38f9d7 100%);
            padding: 50px 40px;
            border-radius: 32px 32px 0 0;
            overflow: hidden;
            min-height: 200px;
        }

        .rainbow-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(240, 147, 251, 0.4) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(67, 233, 123, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 60% 60%, rgba(79, 172, 254, 0.3) 0%, transparent 50%);
            animation: rainbowShift 8s ease-in-out infinite;
        }

        @keyframes rainbowShift {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(180deg); }
        }

        /* Orbes de gradiente flotantes */
        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            animation: orbFloat 6s ease-in-out infinite;
        }

        .orb-1 {
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.6) 0%, transparent 70%);
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .orb-2 {
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, rgba(240, 147, 251, 0.6) 0%, transparent 70%);
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .orb-3 {
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(67, 233, 123, 0.6) 0%, transparent 70%);
            top: 30%;
            right: 30%;
            animation-delay: 4s;
        }

        .orb-4 {
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, rgba(79, 172, 254, 0.6) 0%, transparent 70%);
            bottom: 20%;
            left: 40%;
            animation-delay: 1s;
        }

        @keyframes orbFloat {
            0%, 100% { transform: translateY(0px) translateX(0px) scale(1); }
            33% { transform: translateY(-30px) translateX(20px) scale(1.1); }
            66% { transform: translateY(20px) translateX(-15px) scale(0.9); }
        }

        /* Partículas flotantes */
        .floating-particles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: particleFloat 8s linear infinite;
        }

        .particle-1 { left: 10%; animation-delay: 0s; }
        .particle-2 { left: 20%; animation-delay: 1s; }
        .particle-3 { left: 40%; animation-delay: 2s; }
        .particle-4 { left: 60%; animation-delay: 3s; }
        .particle-5 { left: 80%; animation-delay: 4s; }
        .particle-6 { left: 90%; animation-delay: 5s; }

        @keyframes particleFloat {
            0% { transform: translateY(100vh) scale(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) scale(1); opacity: 0; }
        }

        /* Contenido del header */
        .header-content-ultra {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 40px;
        }

        /* Icono 3D ultra moderno */
        .request-icon-ultra {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-container-3d {
            position: relative;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-inner {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 3;
            animation: iconRotate 4s ease-in-out infinite;
            box-shadow:
                0 10px 30px rgba(0, 0, 0, 0.2),
                inset 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        @keyframes iconRotate {
            0%, 100% { transform: rotateY(0deg) rotateX(0deg); }
            50% { transform: rotateY(180deg) rotateX(10deg); }
        }

        .icon-glow {
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            animation: glowPulse 2s ease-in-out infinite;
        }

        @keyframes glowPulse {
            0%, 100% { transform: scale(1); opacity: 0.5; }
            50% { transform: scale(1.2); opacity: 0.8; }
        }

        .icon-ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            animation: ringRotate 3s linear infinite;
        }

        .ring-1 {
            width: 110px;
            height: 110px;
            animation-delay: 0s;
        }

        .ring-2 {
            width: 130px;
            height: 130px;
            animation-delay: 1s;
            animation-direction: reverse;
        }

        .ring-3 {
            width: 150px;
            height: 150px;
            animation-delay: 2s;
        }

        @keyframes ringRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Texto del header */
        .header-text-ultra {
            flex: 1;
            color: white;
        }

        .request-title-ultra {
            margin: 0 0 15px 0;
            font-size: 42px;
            font-weight: 900;
            line-height: 1.1;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            letter-spacing: -1px;
        }

        .title-gradient {
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .title-highlight {
            background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
        }

        .request-subtitle-ultra {
            margin: 0 0 30px 0;
            font-size: 18px;
            color: rgba(255, 255, 255, 0.95);
            line-height: 1.6;
            font-weight: 500;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* Estadísticas ultra coloridas */
        .request-stats-ultra {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-card-ultra {
            position: relative;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 16px 20px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            min-width: 140px;
        }

        .stat-card-ultra::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-card-ultra:hover {
            transform: translateY(-5px) scale(1.05);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .stat-card-ultra:hover::before {
            opacity: 1;
        }

        .stat-primary::before {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.3) 0%, rgba(255, 142, 83, 0.3) 100%);
        }

        .stat-secondary::before {
            background: linear-gradient(135deg, rgba(72, 187, 120, 0.3) 0%, rgba(56, 249, 215, 0.3) 100%);
        }

        .stat-accent::before {
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.3) 0%, rgba(0, 242, 254, 0.3) 100%);
        }

        .stat-icon-ultra {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            position: relative;
            z-index: 2;
        }

        .stat-content {
            display: flex;
            flex-direction: column;
            position: relative;
            z-index: 2;
        }

        .stat-number-ultra {
            font-size: 14px;
            font-weight: 800;
            color: white;
            line-height: 1;
            text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .stat-label-ultra {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 600;
        }

        .stat-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            transform: translate(-50%, -50%);
            border-radius: 16px;
            animation: statGlow 3s ease-in-out infinite;
        }

        @keyframes statGlow {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
            50% { transform: translate(-50%, -50%) scale(1.1); opacity: 0.8; }
        }

        /* Formulario ultra moderno */
        .ultra-modern-form {
            padding: 40px;
            background: rgba(255, 255, 255, 0.02);
        }

        .form-section-ultra {
            margin-bottom: 40px;
            position: relative;
        }

        .section-header-ultra {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 30px;
            position: relative;
        }

        .section-icon-ultra {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            animation: sectionIconPulse 3s ease-in-out infinite;
        }

        @keyframes sectionIconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .section-title-ultra {
            margin: 0;
            font-size: 22px;
            font-weight: 800;
            color: #2d3748;
            flex: 1;
        }

        .section-line {
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            flex: 1;
            margin-left: 20px;
        }

        .form-grid-ultra {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
        }

        .form-group-ultra {
            position: relative;
        }

        .ultra-label {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 14px;
            font-weight: 700;
            color: #4a5568;
            text-transform: uppercase;
            letter-spacing: 0.8px;
        }

        .label-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }

        .label-text {
            color: #2d3748;
        }

        /* Selects ultra modernos */
        .select-container-ultra {
            position: relative;
        }

        .ultra-select {
            width: 100%;
            padding: 18px 60px 18px 24px;
            border: 3px solid #e2e8f0;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            appearance: none;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .ultra-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.15);
            transform: translateY(-3px);
        }

        .select-arrow-ultra {
            position: absolute;
            right: 24px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            pointer-events: none;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .ultra-select:focus + .select-arrow-ultra {
            color: #667eea;
            transform: translateY(-50%) rotate(180deg);
        }

        .select-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .ultra-select:focus ~ .select-glow {
            opacity: 1;
        }

        /* Contenedor de horario */
        .schedule-container-ultra {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            align-items: start;
        }

        /* Selector de turnos ultra moderno */
        .turno-selector-ultra {
            display: grid;
            gap: 30px;
        }

        .turno-category {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .category-title {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 0 0 20px 0;
            font-size: 18px;
            font-weight: 800;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .category-title.apertura {
            color: #ffc107;
        }

        .category-title.tarde {
            color: #ff5722;
        }

        .category-title.noche {
            color: #3f51b5;
        }

        .category-title.especial {
            color: #9c27b0;
        }

        .turno-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 15px;
        }

        .turno-grid.especiales {
            grid-template-columns: repeat(3, 1fr);
            max-width: 500px;
        }

        .turno-selector-ultra input[type="radio"] {
            display: none;
        }

        .turno-option-ultra {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 12px;
            border: 2px solid transparent;
            border-radius: 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-align: center;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            min-height: 80px;
            justify-content: center;
        }

        .turno-option-ultra::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
            border-radius: 16px;
        }

        .turno-option-ultra > * {
            position: relative;
            z-index: 2;
        }

        /* Colores específicos para cada categoría */
        .turno-option-ultra.apertura {
            background: rgba(255, 193, 7, 0.08);
            border-color: rgba(255, 193, 7, 0.2);
        }

        .turno-option-ultra.apertura::before {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
        }

        .turno-option-ultra.tarde {
            background: rgba(255, 87, 34, 0.08);
            border-color: rgba(255, 87, 34, 0.2);
        }

        .turno-option-ultra.tarde::before {
            background: linear-gradient(135deg, #ff5722 0%, #e91e63 100%);
        }

        .turno-option-ultra.noche {
            background: rgba(63, 81, 181, 0.08);
            border-color: rgba(63, 81, 181, 0.2);
        }

        .turno-option-ultra.noche::before {
            background: linear-gradient(135deg, #3f51b5 0%, #9c27b0 100%);
        }

        .turno-option-ultra.especial {
            background: rgba(156, 39, 176, 0.08);
            border-color: rgba(156, 39, 176, 0.2);
        }

        .turno-option-ultra.especial::before {
            background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
        }

        .turno-option-ultra:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .turno-selector-ultra input[type="radio"]:checked + .turno-option-ultra {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
        }

        .turno-selector-ultra input[type="radio"]:checked + .turno-option-ultra::before {
            opacity: 1;
        }

        .turno-selector-ultra input[type="radio"]:checked + .turno-option-ultra .turno-number,
        .turno-selector-ultra input[type="radio"]:checked + .turno-option-ultra .turno-time {
            color: white !important;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        /* Estilos para números y horarios */
        .turno-number {
            font-size: 18px;
            font-weight: 900;
            margin-bottom: 4px;
            line-height: 1;
            transition: color 0.3s ease;
        }

        .turno-time {
            font-size: 11px;
            font-weight: 600;
            opacity: 0.8;
            transition: color 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* Colores específicos para números */
        .turno-option-ultra.apertura .turno-number {
            color: #f57c00;
        }

        .turno-option-ultra.apertura .turno-time {
            color: #ff8f00;
        }

        .turno-option-ultra.tarde .turno-number {
            color: #d32f2f;
        }

        .turno-option-ultra.tarde .turno-time {
            color: #c2185b;
        }

        .turno-option-ultra.noche .turno-number {
            color: #303f9f;
        }

        .turno-option-ultra.noche .turno-time {
            color: #512da8;
        }

        .turno-option-ultra.especial .turno-number {
            color: #7b1fa2;
        }

        .turno-option-ultra.especial .turno-time {
            color: #8e24aa;
        }

        .turno-icon-ultra {
            width: 50px;
            height: 50px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .turno-morning .turno-icon-ultra {
            color: #ffc107;
            background: rgba(255, 193, 7, 0.2);
        }

        .turno-afternoon .turno-icon-ultra {
            color: #ff5722;
            background: rgba(255, 87, 34, 0.2);
        }

        .turno-night .turno-icon-ultra {
            color: #3f51b5;
            background: rgba(63, 81, 181, 0.2);
        }

        .turno-cards-ultra input[type="radio"]:checked + .turno-card-ultra .turno-icon-ultra {
            color: white;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .turno-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .turno-name-ultra {
            font-size: 16px;
            font-weight: 800;
            margin-bottom: 6px;
            transition: color 0.3s ease;
        }

        .turno-morning .turno-name-ultra {
            color: #f57c00;
        }

        .turno-afternoon .turno-name-ultra {
            color: #d32f2f;
        }

        .turno-night .turno-name-ultra {
            color: #303f9f;
        }

        .turno-time-ultra {
            font-size: 13px;
            font-weight: 600;
            opacity: 0.8;
            transition: color 0.3s ease;
        }

        .turno-morning .turno-time-ultra {
            color: #ff8f00;
        }

        .turno-afternoon .turno-time-ultra {
            color: #c2185b;
        }

        .turno-night .turno-time-ultra {
            color: #512da8;
        }

        .turno-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 100%;
            height: 100%;
            border-radius: 20px;
            transform: translate(-50%, -50%);
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .turno-morning .turno-glow {
            background: radial-gradient(circle, rgba(255, 193, 7, 0.3) 0%, transparent 70%);
        }

        .turno-afternoon .turno-glow {
            background: radial-gradient(circle, rgba(255, 87, 34, 0.3) 0%, transparent 70%);
        }

        .turno-night .turno-glow {
            background: radial-gradient(circle, rgba(63, 81, 181, 0.3) 0%, transparent 70%);
        }

        .turno-card-ultra:hover .turno-glow {
            opacity: 1;
        }

        /* Contenedor de fecha */
        .date-container-ultra {
            display: flex;
            flex-direction: column;
        }

        .date-input-container {
            position: relative;
        }

        .ultra-date {
            width: 100%;
            padding: 18px 60px 18px 24px;
            border: 3px solid #e2e8f0;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .ultra-date:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.15);
            transform: translateY(-3px);
        }

        .date-icon-ultra {
            position: absolute;
            right: 24px;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
            pointer-events: none;
            font-size: 18px;
        }

        .date-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .ultra-date:focus ~ .date-glow {
            opacity: 1;
        }

        /* Textarea ultra moderno */
        .textarea-container-ultra {
            margin-top: 20px;
        }

        .textarea-wrapper-ultra {
            position: relative;
        }

        .ultra-textarea {
            width: 100%;
            padding: 24px;
            border: 3px solid #e2e8f0;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            font-size: 16px;
            font-weight: 500;
            color: #2d3748;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            resize: vertical;
            min-height: 120px;
            font-family: inherit;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .ultra-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 6px rgba(102, 126, 234, 0.15);
            transform: translateY(-3px);
        }

        .textarea-counter-ultra {
            position: absolute;
            bottom: 16px;
            right: 24px;
            font-size: 12px;
            color: #a0aec0;
            font-weight: 600;
            background: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .textarea-glow {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .ultra-textarea:focus ~ .textarea-glow {
            opacity: 1;
        }

        /* Botón de envío espectacular */
        .submit-container-ultra {
            margin-top: 50px;
            display: flex;
            justify-content: center;
        }

        .btn-submit-ultra {
            position: relative;
            border: none;
            border-radius: 25px;
            padding: 0;
            cursor: pointer;
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            min-width: 320px;
            min-height: 80px;
            background: transparent;
        }

        .btn-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 25px;
            overflow: hidden;
        }

        .btn-gradient {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg,
                #667eea 0%,
                #764ba2 25%,
                #f093fb 50%,
                #f5576c 75%,
                #4facfe 100%);
            animation: gradientShift 4s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { transform: translateX(0%); }
            50% { transform: translateX(-10%); }
        }

        .btn-particles {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        .btn-particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: btnParticleFloat 3s linear infinite;
        }

        .btn-particle:nth-child(1) {
            left: 20%;
            animation-delay: 0s;
        }

        .btn-particle:nth-child(2) {
            left: 50%;
            animation-delay: 1s;
        }

        .btn-particle:nth-child(3) {
            left: 80%;
            animation-delay: 2s;
        }

        @keyframes btnParticleFloat {
            0% { transform: translateY(80px) scale(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-20px) scale(1); opacity: 0; }
        }

        .btn-content-ultra {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px 35px;
            transition: all 0.3s ease;
        }

        .btn-icon-ultra {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            transition: all 0.4s ease;
            backdrop-filter: blur(10px);
        }

        .btn-text-ultra {
            flex: 1;
            text-align: left;
        }

        .btn-title-ultra {
            display: block;
            font-size: 20px;
            font-weight: 800;
            color: white;
            margin-bottom: 4px;
            line-height: 1;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .btn-subtitle-ultra {
            display: block;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
        }

        .btn-loading-ultra {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 25px;
            opacity: 0;
            transition: opacity 0.3s ease;
            color: white;
            font-weight: 600;
        }

        .loading-spinner {
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .btn-submit-ultra:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.4);
        }

        .btn-submit-ultra:hover .btn-icon-ultra {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1) rotate(15deg);
        }

        .btn-submit-ultra.loading .btn-content-ultra {
            opacity: 0;
        }

        .btn-submit-ultra.loading .btn-loading-ultra {
            opacity: 1;
        }

        /* ===== TEMA DARK PARA ULTRA MODERN PANEL ===== */
        .theme-dark .ultra-modern-request-panel {
            background: rgba(26, 32, 44, 0.98) !important;
            border-color: #4a5568 !important;
            box-shadow:
                0 30px 80px rgba(0, 0, 0, 0.4),
                0 15px 40px rgba(0, 0, 0, 0.3),
                inset 0 2px 0 rgba(255, 255, 255, 0.1) !important;
        }

        .theme-dark .ultra-modern-header {
            background: linear-gradient(135deg,
                #2d3748 0%,
                #4a5568 15%,
                #553c9a 30%,
                #6b46c1 45%,
                #1e40af 60%,
                #0891b2 75%,
                #059669 90%,
                #047857 100%) !important;
        }

        .theme-dark .section-title-ultra {
            color: #f7fafc !important;
        }

        .theme-dark .label-text {
            color: #e2e8f0 !important;
        }

        .theme-dark .ultra-select,
        .theme-dark .ultra-date,
        .theme-dark .ultra-textarea {
            background: rgba(45, 55, 72, 0.9) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .ultra-select:focus,
        .theme-dark .ultra-date:focus,
        .theme-dark .ultra-textarea:focus {
            border-color: #81e6d9 !important;
            box-shadow: 0 0 0 6px rgba(129, 230, 217, 0.15) !important;
        }

        .theme-dark .ultra-select option {
            background: #2d3748 !important;
            color: #f7fafc !important;
        }

        .theme-dark .turno-category {
            background: rgba(45, 55, 72, 0.6) !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .category-title.apertura {
            color: #fbbf24 !important;
        }

        .theme-dark .category-title.tarde {
            color: #f87171 !important;
        }

        .theme-dark .category-title.noche {
            color: #818cf8 !important;
        }

        .theme-dark .category-title.especial {
            color: #c084fc !important;
        }

        .theme-dark .turno-option-ultra {
            background: rgba(45, 55, 72, 0.4) !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .turno-option-ultra.apertura .turno-number {
            color: #fbbf24 !important;
        }

        .theme-dark .turno-option-ultra.apertura .turno-time {
            color: #fcd34d !important;
        }

        .theme-dark .turno-option-ultra.tarde .turno-number {
            color: #f87171 !important;
        }

        .theme-dark .turno-option-ultra.tarde .turno-time {
            color: #fca5a5 !important;
        }

        .theme-dark .turno-option-ultra.noche .turno-number {
            color: #818cf8 !important;
        }

        .theme-dark .turno-option-ultra.noche .turno-time {
            color: #a5b4fc !important;
        }

        .theme-dark .turno-option-ultra.especial .turno-number {
            color: #c084fc !important;
        }

        .theme-dark .turno-option-ultra.especial .turno-time {
            color: #d8b4fe !important;
        }

        .theme-dark .textarea-counter-ultra {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #cbd5e0 !important;
        }

        /* Responsive para ultra modern */
        @media (max-width: 768px) {
            .ultra-modern-request-panel {
                margin: 0 -20px 40px -20px;
                border-radius: 0;
            }

            .ultra-modern-header {
                padding: 40px 25px;
                border-radius: 0;
            }

            .header-content-ultra {
                flex-direction: column;
                text-align: center;
                gap: 25px;
            }

            .request-title-ultra {
                font-size: 32px;
            }

            .request-stats-ultra {
                justify-content: center;
                gap: 15px;
            }

            .ultra-modern-form {
                padding: 30px 20px;
            }

            .form-grid-ultra {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .schedule-container-ultra {
                gap: 20px;
            }

            .turno-category {
                padding: 20px 15px;
            }

            .turno-grid {
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 12px;
            }

            .turno-grid.especiales {
                grid-template-columns: repeat(3, 1fr);
            }

            .turno-option-ultra {
                padding: 12px 8px;
                min-height: 70px;
            }

            .turno-number {
                font-size: 16px;
            }

            .turno-time {
                font-size: 10px;
            }

            .btn-submit-ultra {
                min-width: 100%;
            }
        }

        /* ===== PENDING REQUESTS PANEL ===== */
        .pending-requests-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .pending-requests-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 152, 0, 0.02) 0%, rgba(255, 193, 7, 0.02) 100%);
            z-index: 0;
        }

        .pending-requests-panel > * {
            position: relative;
            z-index: 1;
        }

        .pending-requests-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(255, 152, 0, 0.1);
        }

        .pending-requests-title-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .pending-requests-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff9800, #ffc107);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(255, 152, 0, 0.3);
        }

        .pending-requests-title-content h3 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            background: linear-gradient(135deg, #ff9800, #ffc107);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pending-requests-title-content p {
            margin: 5px 0 0 0;
            color: var(--text-secondary);
            font-size: 14px;
        }

        .pending-requests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .pending-request-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid rgba(255, 152, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pending-request-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #ff9800, #ffc107);
        }

        .pending-request-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(255, 152, 0, 0.2);
        }

        .request-agent-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .request-agent-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid rgba(255, 152, 0, 0.3);
        }

        .request-agent-details h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-primary);
        }

        .request-agent-details small {
            color: var(--text-secondary);
        }

        .request-details {
            margin-bottom: 20px;
        }

        .request-details > div {
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .request-function {
            font-size: 16px !important;
            font-weight: 600 !important;
            color: var(--text-primary) !important;
            margin-bottom: 10px !important;
        }

        .request-observations {
            background: rgba(255, 152, 0, 0.1);
            padding: 10px;
            border-radius: 8px;
            font-style: italic;
            margin-top: 10px;
        }

        .request-actions {
            display: flex;
            gap: 10px;
        }

        .request-actions .btn {
            flex: 1;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .request-actions .btn:hover {
            transform: translateY(-2px);
        }

        /* Dark theme for request panels */
        .theme-dark .request-panel,
        .theme-dark .pending-requests-panel {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .request-title-content h3,
        .theme-dark .pending-requests-title-content h3 {
            color: #f7fafc !important;
            -webkit-text-fill-color: #f7fafc !important;
        }

        .theme-dark .request-title-content p,
        .theme-dark .pending-requests-title-content p {
            color: #cbd5e0 !important;
        }

        .theme-dark .request-form-container {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .pending-request-card {
            background: rgba(26, 32, 44, 0.9) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .request-agent-details h6 {
            color: #f7fafc !important;
        }

        .theme-dark .request-agent-details small {
            color: #cbd5e0 !important;
        }

        .theme-dark .request-function {
            color: #f7fafc !important;
        }

        .theme-dark .request-details > div {
            color: #cbd5e0 !important;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1 user-name"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small class="user-group"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>"><?php echo $agente['estado']; ?></span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link active">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Lobby</span>
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    <span>CREC</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link">
                    <i class="fas fa-file-pdf"></i>
                    <span>Informativos</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Mi Perfil</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Buscar Agentes</span>
                </a>
            </div>
            <?php if (hasPermission('all_permissions')): ?>
            <div class="nav-item">
                <a href="admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="color: #2d3748; font-weight: 700;">
                        <i class="fas fa-tasks me-2" style="color: var(--primary-color);"></i>
                        Asignaciones de Turno
                    </h2>
                    <p class="mb-0" style="color: #6b7280; font-weight: 500;">
                        <?php echo date('l, j \d\e F \d\e Y'); ?> • <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <!-- Panel de Notificaciones -->
                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <?php if (count($notificaciones) > 0): ?>
                            <span class="notification-counter"><?php echo count($notificaciones); ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>
            </div>
        </div>

        <!-- Mensajes de éxito/error -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Estadísticas de Agentes -->
        <div class="stats-grid">
            <div class="stat-card disponibles">
                <div class="stat-icon disponibles">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['disponibles']; ?></div>
                <div class="stat-label">Disponibles</div>
            </div>

            <div class="stat-card asignados">
                <div class="stat-icon asignados">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="stat-number"><?php echo count($agentes_con_asignaciones); ?></div>
                <div class="stat-label">Con Asignaciones</div>
            </div>

            <div class="stat-card colacion">
                <div class="stat-icon colacion">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['en_colacion']; ?></div>
                <div class="stat-label">En Colación</div>
            </div>

            <div class="stat-card fuera">
                <div class="stat-icon fuera">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['fuera_turno']; ?></div>
                <div class="stat-label">Fuera de Turno</div>
            </div>
        </div>

        <?php if ($can_assign): ?>
        <!-- Panel de Gestión de Asignaciones Moderno -->
        <div class="management-panel">
            <div class="management-header">
                <div class="management-title-section">
                    <div class="management-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="management-text">
                        <h3 class="management-title">Gestión de Asignaciones</h3>
                        <p class="management-subtitle">Administra las funciones y turnos del personal</p>
                    </div>
                </div>

                <div class="management-actions">
                    <button class="btn-new-assignment" data-bs-toggle="modal" data-bs-target="#assignModal">
                        <div class="btn-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="btn-text">
                            <span class="btn-title">Nueva Asignación</span>
                            <span class="btn-subtitle">Asignar función a agente</span>
                        </div>
                        <div class="btn-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>

                    <button class="btn-bulk-actions" onclick="showBulkActions()">
                        <div class="btn-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="btn-text">
                            <span class="btn-title">Acciones Masivas</span>
                            <span class="btn-subtitle">Gestionar múltiples asignaciones</span>
                        </div>
                        <div class="btn-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>
                </div>
            </div>

            <div class="management-stats">
                <div class="stat-item">
                    <div class="stat-icon disponibles">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($agentes_disponibles); ?></span>
                        <span class="stat-label">Disponibles</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon asignados">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($asignaciones_hoy); ?></span>
                        <span class="stat-label">Asignados</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon terminales">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($terminales); ?></span>
                        <span class="stat-label">Terminales</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon funciones">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($funciones); ?></span>
                        <span class="stat-label">Funciones</span>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes Disponibles Sin Asignaciones -->
        <?php if (!empty($agentes_disponibles)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-check"></i>
                    Agentes Disponibles Sin Asignaciones (<?php echo count($agentes_disponibles); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_disponibles as $agente_disp): ?>
                <div class="agent-card">
                    <div class="agent-status disponible">Disponible</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_disp['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_disp['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_disp['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-assign" onclick="openAssignModal(<?php echo $agente_disp['id']; ?>, '<?php echo htmlspecialchars($agente_disp['nombre']); ?>')">
                            <i class="fas fa-plus me-1"></i>
                            Asignar Función
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes Con Asignaciones -->
        <?php if (!empty($asignaciones_hoy)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-cog"></i>
                    Agentes Con Asignaciones Hoy (<?php echo count($asignaciones_hoy); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($asignaciones_hoy as $asignacion): ?>
                <div class="agent-card">
                    <div class="agent-status ocupado">Asignado</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($asignacion['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($asignacion['agente_nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($asignacion['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <div class="assignment-info">
                        <h6><?php echo htmlspecialchars($asignacion['funcion_nombre']); ?></h6>
                        <p><?php echo htmlspecialchars($asignacion['terminal_nombre']); ?> • Turno <?php echo ucfirst($asignacion['turno']); ?></p>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-remove" onclick="removeAssignment(<?php echo $asignacion['id']; ?>)">
                            <i class="fas fa-times me-1"></i>
                            Remover
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes En Colación -->
        <?php if (!empty($agentes_colacion)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-coffee"></i>
                    Agentes En Colación (<?php echo count($agentes_colacion); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_colacion as $agente_col): ?>
                <div class="agent-card">
                    <div class="agent-status colacion">En Colación</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_col['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_col['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_col['usuario_sabre']); ?></small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Panel de Solicitud de Asignación (para agentes que no pueden asignar) -->
        <?php if ($can_request): ?>
        <div class="ultra-modern-request-panel">
            <!-- Header con gradiente rainbow y efectos 3D -->
            <div class="ultra-modern-header">
                <div class="rainbow-background">
                    <div class="gradient-orb orb-1"></div>
                    <div class="gradient-orb orb-2"></div>
                    <div class="gradient-orb orb-3"></div>
                    <div class="gradient-orb orb-4"></div>
                    <div class="floating-particles">
                        <div class="particle particle-1"></div>
                        <div class="particle particle-2"></div>
                        <div class="particle particle-3"></div>
                        <div class="particle particle-4"></div>
                        <div class="particle particle-5"></div>
                        <div class="particle particle-6"></div>
                    </div>
                </div>

                <div class="header-content-ultra">
                    <div class="request-icon-ultra">
                        <div class="icon-container-3d">
                            <div class="icon-inner">
                                <i class="fas fa-rocket"></i>
                            </div>
                            <div class="icon-glow"></div>
                            <div class="icon-ring ring-1"></div>
                            <div class="icon-ring ring-2"></div>
                            <div class="icon-ring ring-3"></div>
                        </div>
                    </div>

                    <div class="header-text-ultra">
                        <h2 class="request-title-ultra">
                            <span class="title-gradient">Solicitar</span>
                            <span class="title-highlight">Asignación</span>
                        </h2>
                        <p class="request-subtitle-ultra">
                            <i class="fas fa-sparkles me-2"></i>
                            Envía tu solicitud y recibe respuesta instantánea con notificaciones en tiempo real
                        </p>

                        <div class="request-stats-ultra">
                            <div class="stat-card-ultra stat-primary">
                                <div class="stat-icon-ultra">
                                    <i class="fas fa-bolt"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-ultra">Instantáneo</span>
                                    <span class="stat-label-ultra">Procesamiento</span>
                                </div>
                                <div class="stat-glow"></div>
                            </div>

                            <div class="stat-card-ultra stat-secondary">
                                <div class="stat-icon-ultra">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-ultra">100%</span>
                                    <span class="stat-label-ultra">Notificado</span>
                                </div>
                                <div class="stat-glow"></div>
                            </div>

                            <div class="stat-card-ultra stat-accent">
                                <div class="stat-icon-ultra">
                                    <i class="fas fa-shield-check"></i>
                                </div>
                                <div class="stat-content">
                                    <span class="stat-number-ultra">Seguro</span>
                                    <span class="stat-label-ultra">Proceso</span>
                                </div>
                                <div class="stat-glow"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Formulario ultra moderno -->
            <div class="ultra-modern-form">
                <form method="POST" class="request-form-ultra" id="requestFormUltra">
                    <input type="hidden" name="action" value="request_assignment">

                    <!-- Sección de ubicación -->
                    <div class="form-section-ultra">
                        <div class="section-header-ultra">
                            <div class="section-icon-ultra">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h4 class="section-title-ultra">Ubicación y Función</h4>
                            <div class="section-line"></div>
                        </div>

                        <div class="form-grid-ultra">
                            <div class="form-group-ultra">
                                <label class="ultra-label">
                                    <span class="label-icon"><i class="fas fa-building"></i></span>
                                    <span class="label-text">Terminal</span>
                                </label>
                                <div class="select-container-ultra">
                                    <select name="terminal_id" class="ultra-select" required>
                                        <option value="">🏢 Selecciona tu terminal...</option>
                                        <?php foreach ($terminales as $terminal): ?>
                                        <option value="<?php echo $terminal['id']; ?>">
                                            🏢 <?php echo htmlspecialchars($terminal['nombre']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="select-arrow-ultra">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="select-glow"></div>
                                </div>
                            </div>

                            <div class="form-group-ultra">
                                <label class="ultra-label">
                                    <span class="label-icon"><i class="fas fa-briefcase"></i></span>
                                    <span class="label-text">Función Deseada</span>
                                </label>
                                <div class="select-container-ultra">
                                    <select name="funcion_id" class="ultra-select" required>
                                        <option value="">💼 Selecciona tu función...</option>
                                        <?php foreach ($funciones as $funcion): ?>
                                        <option value="<?php echo $funcion['id']; ?>">
                                            💼 <?php echo htmlspecialchars($funcion['nombre']); ?>
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="select-arrow-ultra">
                                        <i class="fas fa-chevron-down"></i>
                                    </div>
                                    <div class="select-glow"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sección de horario -->
                    <div class="form-section-ultra">
                        <div class="section-header-ultra">
                            <div class="section-icon-ultra">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h4 class="section-title-ultra">Horario Preferido</h4>
                            <div class="section-line"></div>
                        </div>

                        <div class="schedule-container-ultra">
                            <div class="turno-selector-ultra">
                                <!-- Turnos de Apertura -->
                                <div class="turno-category">
                                    <h5 class="category-title apertura">
                                        <i class="fas fa-sun"></i>
                                        Turnos de Apertura
                                    </h5>
                                    <div class="turno-grid">
                                        <input type="radio" name="turno" value="Turno 9" id="turno-9" required>
                                        <label for="turno-9" class="turno-option-ultra apertura">
                                            <span class="turno-number">9</span>
                                            <span class="turno-time">05:00 - 14:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 24" id="turno-24" required>
                                        <label for="turno-24" class="turno-option-ultra apertura">
                                            <span class="turno-number">24</span>
                                            <span class="turno-time">06:00 - 15:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 40" id="turno-40" required>
                                        <label for="turno-40" class="turno-option-ultra apertura">
                                            <span class="turno-number">40</span>
                                            <span class="turno-time">09:00 - 18:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 43" id="turno-43" required>
                                        <label for="turno-43" class="turno-option-ultra apertura">
                                            <span class="turno-number">43</span>
                                            <span class="turno-time">09:00 - 17:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 59" id="turno-59" required>
                                        <label for="turno-59" class="turno-option-ultra apertura">
                                            <span class="turno-number">59</span>
                                            <span class="turno-time">03:00 - 12:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 66" id="turno-66" required>
                                        <label for="turno-66" class="turno-option-ultra apertura">
                                            <span class="turno-number">66</span>
                                            <span class="turno-time">06:00 - 14:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 68" id="turno-68" required>
                                        <label for="turno-68" class="turno-option-ultra apertura">
                                            <span class="turno-number">68</span>
                                            <span class="turno-time">05:00 - 13:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 74" id="turno-74" required>
                                        <label for="turno-74" class="turno-option-ultra apertura">
                                            <span class="turno-number">74</span>
                                            <span class="turno-time">10:00 - 19:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 77" id="turno-77" required>
                                        <label for="turno-77" class="turno-option-ultra apertura">
                                            <span class="turno-number">77</span>
                                            <span class="turno-time">06:00 - 13:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 92" id="turno-92" required>
                                        <label for="turno-92" class="turno-option-ultra apertura">
                                            <span class="turno-number">92</span>
                                            <span class="turno-time">11:00 - 18:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 145" id="turno-145" required>
                                        <label for="turno-145" class="turno-option-ultra apertura">
                                            <span class="turno-number">145</span>
                                            <span class="turno-time">06:00 - 16:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 146" id="turno-146" required>
                                        <label for="turno-146" class="turno-option-ultra apertura">
                                            <span class="turno-number">146</span>
                                            <span class="turno-time">03:00 - 13:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 161" id="turno-161" required>
                                        <label for="turno-161" class="turno-option-ultra apertura">
                                            <span class="turno-number">161</span>
                                            <span class="turno-time">07:00 - 15:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 242" id="turno-242" required>
                                        <label for="turno-242" class="turno-option-ultra apertura">
                                            <span class="turno-number">242</span>
                                            <span class="turno-time">11:00 - 21:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 287" id="turno-287" required>
                                        <label for="turno-287" class="turno-option-ultra apertura">
                                            <span class="turno-number">287</span>
                                            <span class="turno-time">11:00 - 19:00</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Turnos Tarde -->
                                <div class="turno-category">
                                    <h5 class="category-title tarde">
                                        <i class="fas fa-cloud-sun"></i>
                                        Turnos Tarde
                                    </h5>
                                    <div class="turno-grid">
                                        <input type="radio" name="turno" value="Turno 6" id="turno-6" required>
                                        <label for="turno-6" class="turno-option-ultra tarde">
                                            <span class="turno-number">6</span>
                                            <span class="turno-time">19:00 - 04:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 11" id="turno-11" required>
                                        <label for="turno-11" class="turno-option-ultra tarde">
                                            <span class="turno-number">11</span>
                                            <span class="turno-time">14:00 - 23:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 82" id="turno-82" required>
                                        <label for="turno-82" class="turno-option-ultra tarde">
                                            <span class="turno-number">82</span>
                                            <span class="turno-time">12:00 - 21:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 91" id="turno-91" required>
                                        <label for="turno-91" class="turno-option-ultra tarde">
                                            <span class="turno-number">91</span>
                                            <span class="turno-time">13:00 - 18:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 94" id="turno-94" required>
                                        <label for="turno-94" class="turno-option-ultra tarde">
                                            <span class="turno-number">94</span>
                                            <span class="turno-time">14:00 - 19:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 103" id="turno-103" required>
                                        <label for="turno-103" class="turno-option-ultra tarde">
                                            <span class="turno-number">103</span>
                                            <span class="turno-time">17:00 - 22:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 104" id="turno-104" required>
                                        <label for="turno-104" class="turno-option-ultra tarde">
                                            <span class="turno-number">104</span>
                                            <span class="turno-time">15:00 - 22:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 107" id="turno-107" required>
                                        <label for="turno-107" class="turno-option-ultra tarde">
                                            <span class="turno-number">107</span>
                                            <span class="turno-time">16:00 - 23:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 126" id="turno-126" required>
                                        <label for="turno-126" class="turno-option-ultra tarde">
                                            <span class="turno-number">126</span>
                                            <span class="turno-time">14:00 - 00:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 163" id="turno-163" required>
                                        <label for="turno-163" class="turno-option-ultra tarde">
                                            <span class="turno-number">163</span>
                                            <span class="turno-time">14:00 - 22:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 180" id="turno-180" required>
                                        <label for="turno-180" class="turno-option-ultra tarde">
                                            <span class="turno-number">180</span>
                                            <span class="turno-time">15:00 - 23:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 213" id="turno-213" required>
                                        <label for="turno-213" class="turno-option-ultra tarde">
                                            <span class="turno-number">213</span>
                                            <span class="turno-time">13:00 - 23:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 227" id="turno-227" required>
                                        <label for="turno-227" class="turno-option-ultra tarde">
                                            <span class="turno-number">227</span>
                                            <span class="turno-time">12:00 - 22:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 245" id="turno-245" required>
                                        <label for="turno-245" class="turno-option-ultra tarde">
                                            <span class="turno-number">245</span>
                                            <span class="turno-time">13:00 - 21:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 295" id="turno-295" required>
                                        <label for="turno-295" class="turno-option-ultra tarde">
                                            <span class="turno-number">295</span>
                                            <span class="turno-time">19:00 - 03:00</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Turnos Noche -->
                                <div class="turno-category">
                                    <h5 class="category-title noche">
                                        <i class="fas fa-moon"></i>
                                        Turnos Noche
                                    </h5>
                                    <div class="turno-grid">
                                        <input type="radio" name="turno" value="Turno 7" id="turno-7" required>
                                        <label for="turno-7" class="turno-option-ultra noche">
                                            <span class="turno-number">7</span>
                                            <span class="turno-time">22:00 - 07:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 13" id="turno-13" required>
                                        <label for="turno-13" class="turno-option-ultra noche">
                                            <span class="turno-number">13</span>
                                            <span class="turno-time">20:00 - 05:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 21" id="turno-21" required>
                                        <label for="turno-21" class="turno-option-ultra noche">
                                            <span class="turno-number">21</span>
                                            <span class="turno-time">21:00 - 06:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 28" id="turno-28" required>
                                        <label for="turno-28" class="turno-option-ultra noche">
                                            <span class="turno-number">28</span>
                                            <span class="turno-time">20:00 - 06:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 121" id="turno-121" required>
                                        <label for="turno-121" class="turno-option-ultra noche">
                                            <span class="turno-number">121</span>
                                            <span class="turno-time">22:00 - 08:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 147" id="turno-147" required>
                                        <label for="turno-147" class="turno-option-ultra noche">
                                            <span class="turno-number">147</span>
                                            <span class="turno-time">21:00 - 07:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 274" id="turno-274" required>
                                        <label for="turno-274" class="turno-option-ultra noche">
                                            <span class="turno-number">274</span>
                                            <span class="turno-time">23:00 - 07:00</span>
                                        </label>

                                        <input type="radio" name="turno" value="Turno 275" id="turno-275" required>
                                        <label for="turno-275" class="turno-option-ultra noche">
                                            <span class="turno-number">275</span>
                                            <span class="turno-time">22:00 - 06:00</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- Turnos Especiales -->
                                <div class="turno-category">
                                    <h5 class="category-title especial">
                                        <i class="fas fa-star"></i>
                                        Turnos Especiales
                                    </h5>
                                    <div class="turno-grid especiales">
                                        <input type="radio" name="turno" value="SALIENTE" id="turno-saliente" required>
                                        <label for="turno-saliente" class="turno-option-ultra especial">
                                            <span class="turno-number">SAL</span>
                                            <span class="turno-time">Saliente</span>
                                        </label>

                                        <input type="radio" name="turno" value="LIBRE" id="turno-libre" required>
                                        <label for="turno-libre" class="turno-option-ultra especial">
                                            <span class="turno-number">LIB</span>
                                            <span class="turno-time">Libre</span>
                                        </label>

                                        <input type="radio" name="turno" value="DOMINGO LIBRE" id="turno-domingo" required>
                                        <label for="turno-domingo" class="turno-option-ultra especial">
                                            <span class="turno-number">DOM</span>
                                            <span class="turno-time">Domingo Libre</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="date-container-ultra">
                                <label class="ultra-label">
                                    <span class="label-icon"><i class="fas fa-calendar"></i></span>
                                    <span class="label-text">Fecha Solicitada</span>
                                </label>
                                <div class="date-input-container">
                                    <input type="date" name="fecha" class="ultra-date" value="<?php echo date('Y-m-d'); ?>" required>
                                    <div class="date-icon-ultra">
                                        <i class="fas fa-calendar-alt"></i>
                                    </div>
                                    <div class="date-glow"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sección de observaciones -->
                    <div class="form-section-ultra">
                        <div class="section-header-ultra">
                            <div class="section-icon-ultra">
                                <i class="fas fa-comment-alt"></i>
                            </div>
                            <h4 class="section-title-ultra">Información Adicional</h4>
                            <div class="section-line"></div>
                        </div>

                        <div class="textarea-container-ultra">
                            <label class="ultra-label">
                                <span class="label-icon"><i class="fas fa-edit"></i></span>
                                <span class="label-text">Observaciones (Opcional)</span>
                            </label>
                            <div class="textarea-wrapper-ultra">
                                <textarea name="observaciones" class="ultra-textarea" rows="3" placeholder="💭 Comparte cualquier información relevante sobre tu solicitud..." maxlength="500"></textarea>
                                <div class="textarea-counter-ultra">
                                    <span id="charCountUltra">0</span>/500
                                </div>
                                <div class="textarea-glow"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Botón de envío espectacular -->
                    <div class="submit-container-ultra">
                        <button type="submit" class="btn-submit-ultra">
                            <div class="btn-background">
                                <div class="btn-gradient"></div>
                                <div class="btn-particles">
                                    <div class="btn-particle"></div>
                                    <div class="btn-particle"></div>
                                    <div class="btn-particle"></div>
                                </div>
                            </div>
                            <div class="btn-content-ultra">
                                <div class="btn-icon-ultra">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="btn-text-ultra">
                                    <span class="btn-title-ultra">Enviar Solicitud</span>
                                    <span class="btn-subtitle-ultra">Recibirás una notificación instantánea</span>
                                </div>
                            </div>
                            <div class="btn-loading-ultra">
                                <div class="loading-spinner"></div>
                                <span>Enviando...</span>
                            </div>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <!-- Panel de Solicitudes Pendientes (para supervisores) -->
        <?php if ($can_assign && !empty($solicitudes_pendientes)): ?>
        <div class="pending-requests-panel">
            <div class="pending-requests-header">
                <div class="pending-requests-title-section">
                    <div class="pending-requests-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="pending-requests-title-content">
                        <h3>Solicitudes Pendientes</h3>
                        <p><?php echo count($solicitudes_pendientes); ?> solicitudes esperando aprobación</p>
                    </div>
                </div>
            </div>

            <div class="pending-requests-grid">
                <?php foreach ($solicitudes_pendientes as $solicitud): ?>
                <div class="pending-request-card">
                    <div class="request-agent-info">
                        <img src="<?php echo getAvatarUrl($solicitud['foto_perfil']); ?>" alt="Avatar" class="request-agent-avatar">
                        <div class="request-agent-details">
                            <h6><?php echo htmlspecialchars($solicitud['agente_nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($solicitud['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <div class="request-details">
                        <div class="request-function">
                            <strong><?php echo htmlspecialchars($solicitud['funcion_nombre']); ?></strong>
                        </div>
                        <div class="request-location">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            <?php echo htmlspecialchars($solicitud['terminal_nombre']); ?>
                        </div>
                        <div class="request-schedule">
                            <i class="fas fa-clock me-1"></i>
                            Turno <?php echo ucfirst($solicitud['turno']); ?> - <?php echo date('d/m/Y', strtotime($solicitud['fecha'])); ?>
                        </div>
                        <div class="request-time">
                            <i class="fas fa-calendar me-1"></i>
                            Solicitado <?php echo timeAgo($solicitud['solicitado_en']); ?>
                        </div>
                        <?php if (!empty($solicitud['observaciones'])): ?>
                        <div class="request-observations">
                            <i class="fas fa-comment me-1"></i>
                            <?php echo htmlspecialchars($solicitud['observaciones']); ?>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="request-actions">
                        <button class="btn btn-success btn-sm" onclick="processRequest(<?php echo $solicitud['id']; ?>, 'aprobar')">
                            <i class="fas fa-check me-1"></i>
                            Aprobar
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="showRejectModal(<?php echo $solicitud['id']; ?>)">
                            <i class="fas fa-times me-1"></i>
                            Rechazar
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    </div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">📭</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativo' => 'ℹ️',
                                default => '📢'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>
                            <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Overlay para notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Modal de Asignación -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        Nueva Asignación
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="assign">

                        <div class="mb-3">
                            <label class="form-label">Agente</label>
                            <select name="agente_id" class="form-select" required id="agenteSelect">
                                <option value="">Seleccionar agente...</option>
                                <?php foreach ($agentes_disponibles as $agente_opt): ?>
                                <option value="<?php echo $agente_opt['id']; ?>">
                                    <?php echo htmlspecialchars($agente_opt['nombre']); ?> (<?php echo htmlspecialchars($agente_opt['usuario_sabre']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Terminal</label>
                            <select name="terminal_id" class="form-select" required>
                                <option value="">Seleccionar terminal...</option>
                                <?php foreach ($terminales as $terminal): ?>
                                <option value="<?php echo $terminal['id']; ?>">
                                    <?php echo htmlspecialchars($terminal['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Función</label>
                            <select name="funcion_id" class="form-select" required>
                                <option value="">Seleccionar función...</option>
                                <?php foreach ($funciones as $funcion): ?>
                                <option value="<?php echo $funcion['id']; ?>">
                                    <?php echo htmlspecialchars($funcion['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Turno</label>
                            <select name="turno" class="form-select" required>
                                <option value="">Seleccionar turno...</option>
                                <optgroup label="🌅 Turnos de Apertura">
                                    <option value="Turno 9">Turno 9 (05:00 - 14:00)</option>
                                    <option value="Turno 24">Turno 24 (06:00 - 15:00)</option>
                                    <option value="Turno 40">Turno 40 (09:00 - 18:00)</option>
                                    <option value="Turno 43">Turno 43 (09:00 - 17:00)</option>
                                    <option value="Turno 59">Turno 59 (03:00 - 12:00)</option>
                                    <option value="Turno 66">Turno 66 (06:00 - 14:00)</option>
                                    <option value="Turno 68">Turno 68 (05:00 - 13:00)</option>
                                    <option value="Turno 74">Turno 74 (10:00 - 19:00)</option>
                                    <option value="Turno 77">Turno 77 (06:00 - 13:00)</option>
                                    <option value="Turno 92">Turno 92 (11:00 - 18:00)</option>
                                    <option value="Turno 145">Turno 145 (06:00 - 16:00)</option>
                                    <option value="Turno 146">Turno 146 (03:00 - 13:00)</option>
                                    <option value="Turno 161">Turno 161 (07:00 - 15:00)</option>
                                    <option value="Turno 242">Turno 242 (11:00 - 21:00)</option>
                                    <option value="Turno 287">Turno 287 (11:00 - 19:00)</option>
                                </optgroup>
                                <optgroup label="🌤️ Turnos Tarde">
                                    <option value="Turno 6">Turno 6 (19:00 - 04:00)</option>
                                    <option value="Turno 11">Turno 11 (14:00 - 23:00)</option>
                                    <option value="Turno 82">Turno 82 (12:00 - 21:00)</option>
                                    <option value="Turno 91">Turno 91 (13:00 - 18:00)</option>
                                    <option value="Turno 94">Turno 94 (14:00 - 19:00)</option>
                                    <option value="Turno 103">Turno 103 (17:00 - 22:00)</option>
                                    <option value="Turno 104">Turno 104 (15:00 - 22:00)</option>
                                    <option value="Turno 107">Turno 107 (16:00 - 23:00)</option>
                                    <option value="Turno 126">Turno 126 (14:00 - 00:00)</option>
                                    <option value="Turno 163">Turno 163 (14:00 - 22:00)</option>
                                    <option value="Turno 180">Turno 180 (15:00 - 23:00)</option>
                                    <option value="Turno 213">Turno 213 (13:00 - 23:00)</option>
                                    <option value="Turno 227">Turno 227 (12:00 - 22:00)</option>
                                    <option value="Turno 245">Turno 245 (13:00 - 21:00)</option>
                                    <option value="Turno 295">Turno 295 (19:00 - 03:00)</option>
                                </optgroup>
                                <optgroup label="🌙 Turnos Noche">
                                    <option value="Turno 7">Turno 7 (22:00 - 07:00)</option>
                                    <option value="Turno 13">Turno 13 (20:00 - 05:00)</option>
                                    <option value="Turno 21">Turno 21 (21:00 - 06:00)</option>
                                    <option value="Turno 28">Turno 28 (20:00 - 06:00)</option>
                                    <option value="Turno 121">Turno 121 (22:00 - 08:00)</option>
                                    <option value="Turno 147">Turno 147 (21:00 - 07:00)</option>
                                    <option value="Turno 274">Turno 274 (23:00 - 07:00)</option>
                                    <option value="Turno 275">Turno 275 (22:00 - 06:00)</option>
                                </optgroup>
                                <optgroup label="⭐ Turnos Especiales">
                                    <option value="SALIENTE">SALIENTE</option>
                                    <option value="LIBRE">LIBRE</option>
                                    <option value="DOMINGO LIBRE">DOMINGO LIBRE</option>
                                </optgroup>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fecha</label>
                            <input type="date" name="fecha" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Observaciones (Opcional)</label>
                            <textarea name="observaciones" class="form-control" rows="3" placeholder="Observaciones adicionales..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Crear Asignación
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Modal de Asignaciones Masivas -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="bulkAssignModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-layer-group me-2"></i>
                        Asignaciones Masivas
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs de navegación -->
                    <ul class="nav nav-pills mb-4" id="bulkTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="select-agents-tab" data-bs-toggle="pill" data-bs-target="#select-agents" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>
                                1. Seleccionar Agentes
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="assign-functions-tab" data-bs-toggle="pill" data-bs-target="#assign-functions" type="button" role="tab">
                                <i class="fas fa-tasks me-2"></i>
                                2. Asignar Funciones
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="review-assignments-tab" data-bs-toggle="pill" data-bs-target="#review-assignments" type="button" role="tab">
                                <i class="fas fa-check-circle me-2"></i>
                                3. Revisar y Confirmar
                            </button>
                        </li>
                    </ul>

                    <!-- Contenido de los tabs -->
                    <div class="tab-content" id="bulkTabContent">
                        <!-- Tab 1: Seleccionar Agentes -->
                        <div class="tab-pane fade show active" id="select-agents" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="filter-panel">
                                        <h6 class="filter-title">
                                            <i class="fas fa-filter me-2"></i>
                                            Filtros
                                        </h6>

                                        <div class="mb-3">
                                            <label class="form-label">Estado</label>
                                            <select class="form-select" id="filterEstado" onchange="filterAgents()">
                                                <option value="">Todos los estados</option>
                                                <option value="🟢Disponible">🟢 Disponible</option>
                                                <option value="🟡En Colación">🟡 En Colación</option>
                                                <option value="🔴Ocupado">🔴 Ocupado</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Grupo</label>
                                            <select class="form-select" id="filterGrupo" onchange="filterAgents()">
                                                <option value="">Todos los grupos</option>
                                                <?php
                                                $grupos = array_unique(array_filter(array_column($todos_agentes, 'grupo_nombre')));
                                                foreach ($grupos as $grupo): ?>
                                                    <option value="<?php echo htmlspecialchars($grupo); ?>">
                                                        <?php echo htmlspecialchars($grupo); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Búsqueda</label>
                                            <input type="text" class="form-control" id="searchAgent" placeholder="Buscar por nombre o Sabre..." onkeyup="filterAgents()">
                                        </div>

                                        <div class="filter-actions">
                                            <button class="btn btn-outline-primary btn-sm" onclick="selectAllVisible()">
                                                <i class="fas fa-check-square me-1"></i>
                                                Seleccionar Visibles
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                                <i class="fas fa-times me-1"></i>
                                                Limpiar Selección
                                            </button>
                                        </div>

                                        <div class="selection-summary">
                                            <div class="summary-item">
                                                <span class="summary-label">Seleccionados:</span>
                                                <span class="summary-count" id="selectedCount">0</span>
                                            </div>
                                            <div class="summary-item">
                                                <span class="summary-label">Total visible:</span>
                                                <span class="summary-count" id="visibleCount"><?php echo count($todos_agentes); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-8">
                                    <div class="agents-selection-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-users me-2"></i>
                                            Agentes Disponibles
                                        </h6>

                                        <div class="agents-grid" id="agentsGrid">
                                            <?php foreach ($todos_agentes as $agente_bulk): ?>
                                            <div class="agent-selection-card"
                                                 data-estado="<?php echo htmlspecialchars($agente_bulk['estado']); ?>"
                                                 data-grupo="<?php echo htmlspecialchars($agente_bulk['grupo_nombre'] ?? ''); ?>"
                                                 data-nombre="<?php echo htmlspecialchars($agente_bulk['nombre']); ?>"
                                                 data-sabre="<?php echo htmlspecialchars($agente_bulk['usuario_sabre']); ?>"
                                                 data-id="<?php echo $agente_bulk['id']; ?>">

                                                <div class="selection-checkbox">
                                                    <input type="checkbox" class="agent-checkbox" value="<?php echo $agente_bulk['id']; ?>" onchange="updateSelection()">
                                                </div>

                                                <div class="agent-avatar-section">
                                                    <img src="<?php echo getAvatarUrl($agente_bulk['foto_perfil']); ?>" alt="Avatar" class="agent-avatar-bulk">
                                                    <div class="agent-status-bulk <?php
                                                        echo match($agente_bulk['estado']) {
                                                            '🟢Disponible' => 'status-disponible',
                                                            '🟡En Colación' => 'status-colacion',
                                                            '🔴Ocupado' => 'status-ocupado',
                                                            default => 'status-fuera'
                                                        };
                                                    ?>"></div>
                                                </div>

                                                <div class="agent-info-bulk">
                                                    <h6 class="agent-name-bulk"><?php echo htmlspecialchars($agente_bulk['nombre']); ?></h6>
                                                    <p class="agent-sabre-bulk">Sabre: <?php echo htmlspecialchars($agente_bulk['usuario_sabre']); ?></p>
                                                    <p class="agent-grupo-bulk"><?php echo htmlspecialchars($agente_bulk['grupo_nombre'] ?? 'Sin grupo'); ?></p>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 2: Asignar Funciones -->
                        <div class="tab-pane fade" id="assign-functions" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="assignment-config-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-cog me-2"></i>
                                            Configuración de Asignación
                                        </h6>

                                        <form id="bulkAssignmentForm">
                                            <div class="mb-3">
                                                <label class="form-label">Terminal</label>
                                                <select class="form-select" id="bulkTerminal" required>
                                                    <option value="">Seleccionar terminal...</option>
                                                    <?php foreach ($terminales as $terminal): ?>
                                                    <option value="<?php echo $terminal['id']; ?>">
                                                        <?php echo htmlspecialchars($terminal['nombre']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Función</label>
                                                <select class="form-select" id="bulkFuncion" required>
                                                    <option value="">Seleccionar función...</option>
                                                    <?php foreach ($funciones as $funcion): ?>
                                                    <option value="<?php echo $funcion['id']; ?>">
                                                        <?php echo htmlspecialchars($funcion['nombre']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Turno</label>
                                                <select class="form-select" id="bulkTurno" required>
                                                    <option value="">Seleccionar turno...</option>
                                                    <optgroup label="🌅 Turnos de Apertura">
                                                        <option value="Turno 9">Turno 9 (05:00 - 14:00)</option>
                                                        <option value="Turno 24">Turno 24 (06:00 - 15:00)</option>
                                                        <option value="Turno 40">Turno 40 (09:00 - 18:00)</option>
                                                        <option value="Turno 43">Turno 43 (09:00 - 17:00)</option>
                                                        <option value="Turno 59">Turno 59 (03:00 - 12:00)</option>
                                                        <option value="Turno 66">Turno 66 (06:00 - 14:00)</option>
                                                        <option value="Turno 68">Turno 68 (05:00 - 13:00)</option>
                                                        <option value="Turno 74">Turno 74 (10:00 - 19:00)</option>
                                                        <option value="Turno 77">Turno 77 (06:00 - 13:00)</option>
                                                        <option value="Turno 92">Turno 92 (11:00 - 18:00)</option>
                                                        <option value="Turno 145">Turno 145 (06:00 - 16:00)</option>
                                                        <option value="Turno 146">Turno 146 (03:00 - 13:00)</option>
                                                        <option value="Turno 161">Turno 161 (07:00 - 15:00)</option>
                                                        <option value="Turno 242">Turno 242 (11:00 - 21:00)</option>
                                                        <option value="Turno 287">Turno 287 (11:00 - 19:00)</option>
                                                    </optgroup>
                                                    <optgroup label="🌤️ Turnos Tarde">
                                                        <option value="Turno 6">Turno 6 (19:00 - 04:00)</option>
                                                        <option value="Turno 11">Turno 11 (14:00 - 23:00)</option>
                                                        <option value="Turno 82">Turno 82 (12:00 - 21:00)</option>
                                                        <option value="Turno 91">Turno 91 (13:00 - 18:00)</option>
                                                        <option value="Turno 94">Turno 94 (14:00 - 19:00)</option>
                                                        <option value="Turno 103">Turno 103 (17:00 - 22:00)</option>
                                                        <option value="Turno 104">Turno 104 (15:00 - 22:00)</option>
                                                        <option value="Turno 107">Turno 107 (16:00 - 23:00)</option>
                                                        <option value="Turno 126">Turno 126 (14:00 - 00:00)</option>
                                                        <option value="Turno 163">Turno 163 (14:00 - 22:00)</option>
                                                        <option value="Turno 180">Turno 180 (15:00 - 23:00)</option>
                                                        <option value="Turno 213">Turno 213 (13:00 - 23:00)</option>
                                                        <option value="Turno 227">Turno 227 (12:00 - 22:00)</option>
                                                        <option value="Turno 245">Turno 245 (13:00 - 21:00)</option>
                                                        <option value="Turno 295">Turno 295 (19:00 - 03:00)</option>
                                                    </optgroup>
                                                    <optgroup label="🌙 Turnos Noche">
                                                        <option value="Turno 7">Turno 7 (22:00 - 07:00)</option>
                                                        <option value="Turno 13">Turno 13 (20:00 - 05:00)</option>
                                                        <option value="Turno 21">Turno 21 (21:00 - 06:00)</option>
                                                        <option value="Turno 28">Turno 28 (20:00 - 06:00)</option>
                                                        <option value="Turno 121">Turno 121 (22:00 - 08:00)</option>
                                                        <option value="Turno 147">Turno 147 (21:00 - 07:00)</option>
                                                        <option value="Turno 274">Turno 274 (23:00 - 07:00)</option>
                                                        <option value="Turno 275">Turno 275 (22:00 - 06:00)</option>
                                                    </optgroup>
                                                    <optgroup label="⭐ Turnos Especiales">
                                                        <option value="SALIENTE">SALIENTE</option>
                                                        <option value="LIBRE">LIBRE</option>
                                                        <option value="DOMINGO LIBRE">DOMINGO LIBRE</option>
                                                    </optgroup>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Fecha</label>
                                                <input type="date" class="form-control" id="bulkFecha" value="<?php echo date('Y-m-d'); ?>" required>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Observaciones (Opcional)</label>
                                                <textarea class="form-control" id="bulkObservaciones" rows="3" placeholder="Observaciones para todas las asignaciones..."></textarea>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="selected-agents-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-user-check me-2"></i>
                                            Agentes Seleccionados (<span id="selectedAgentsCount">0</span>)
                                        </h6>

                                        <div class="selected-agents-list" id="selectedAgentsList">
                                            <div class="empty-selection">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <p>No hay agentes seleccionados</p>
                                                <small>Regresa al paso anterior para seleccionar agentes</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 3: Revisar y Confirmar -->
                        <div class="tab-pane fade" id="review-assignments" role="tabpanel">
                            <div class="review-panel">
                                <h6 class="panel-title">
                                    <i class="fas fa-clipboard-check me-2"></i>
                                    Resumen de Asignaciones
                                </h6>

                                <div class="assignment-summary" id="assignmentSummary">
                                    <!-- El contenido se generará dinámicamente -->
                                </div>

                                <div class="confirmation-actions">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Importante:</strong> Una vez confirmadas, las asignaciones se crearán inmediatamente.
                                        Asegúrate de revisar toda la información antes de proceder.
                                    </div>

                                    <div class="d-flex gap-3 justify-content-end">
                                        <button type="button" class="btn btn-secondary" onclick="goToPreviousStep()">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            Volver a Editar
                                        </button>
                                        <button type="button" class="btn btn-success btn-lg" onclick="confirmBulkAssignments()">
                                            <i class="fas fa-check me-2"></i>
                                            Confirmar Asignaciones
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <div class="d-flex justify-content-between w-100">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </button>

                        <div class="step-navigation">
                            <button type="button" class="btn btn-outline-primary" id="prevStepBtn" onclick="previousStep()" style="display: none;">
                                <i class="fas fa-arrow-left me-2"></i>
                                Anterior
                            </button>
                            <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()">
                                Siguiente
                                <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Modal para Rechazar Solicitud -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="rejectRequestModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-times-circle me-2"></i>
                        Rechazar Solicitud
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="rejectForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="process_request">
                        <input type="hidden" name="decision" value="rechazar">
                        <input type="hidden" name="solicitud_id" id="rejectSolicitudId">

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>¿Estás seguro?</strong> Esta acción rechazará la solicitud de asignación.
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Motivo del rechazo (Opcional)</label>
                            <textarea name="motivo_rechazo" class="form-control" rows="3" placeholder="Explica el motivo del rechazo..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times me-2"></i>
                            Rechazar Solicitud
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>
        // Variables globales
        let currentTheme = '<?php echo $agente['tema'] ?? 'light'; ?>';

        // Toggle sidebar
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Toggle notifications con efectos completos
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            // Toggle panel y overlay
            const isShowing = panel.classList.contains('show');

            if (isShowing) {
                closeNotifications();
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');

                // Animar las notificaciones
                const items = panel.querySelectorAll('.notification-item');
                items.forEach((item, index) => {
                    item.style.animation = `slideInNotification 0.5s ease-out ${index * 0.1}s`;
                });
            }
        }

        function closeNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            panel.classList.remove('show');
            overlay.classList.remove('show');
        }

        // Open assign modal with pre-selected agent
        function openAssignModal(agenteId, agenteNombre) {
            const modal = new bootstrap.Modal(document.getElementById('assignModal'));
            const select = document.getElementById('agenteSelect');
            select.value = agenteId;
            modal.show();
        }

        // Remove assignment
        function removeAssignment(assignmentId) {
            if (confirm('¿Estás seguro de que deseas remover esta asignación?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="remove">
                    <input type="hidden" name="asignacion_id" value="${assignmentId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Show bulk actions
        function showBulkActions() {
            const modal = new bootstrap.Modal(document.getElementById('bulkAssignModal'));
            modal.show();

            // Reset modal to first step
            resetBulkModal();
        }

        // Variables globales para el modal masivo
        let selectedAgents = [];
        let currentStep = 1;

        // Reset bulk modal
        function resetBulkModal() {
            currentStep = 1;
            selectedAgents = [];

            // Reset tabs
            document.querySelectorAll('#bulkTabs .nav-link').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#bulkTabContent .tab-pane').forEach(pane => pane.classList.remove('show', 'active'));

            document.getElementById('select-agents-tab').classList.add('active');
            document.getElementById('select-agents').classList.add('show', 'active');

            // Reset form
            document.getElementById('bulkAssignmentForm').reset();
            document.getElementById('bulkFecha').value = '<?php echo date('Y-m-d'); ?>';

            // Clear selections
            clearSelection();

            // Update navigation
            updateStepNavigation();
        }

        // Filter agents
        function filterAgents() {
            const estado = document.getElementById('filterEstado').value;
            const grupo = document.getElementById('filterGrupo').value;
            const search = document.getElementById('searchAgent').value.toLowerCase();

            const cards = document.querySelectorAll('.agent-selection-card');
            let visibleCount = 0;

            cards.forEach(card => {
                const cardEstado = card.dataset.estado;
                const cardGrupo = card.dataset.grupo;
                const cardNombre = card.dataset.nombre.toLowerCase();
                const cardSabre = card.dataset.sabre.toLowerCase();

                let show = true;

                if (estado && cardEstado !== estado) show = false;
                if (grupo && cardGrupo !== grupo) show = false;
                if (search && !cardNombre.includes(search) && !cardSabre.includes(search)) show = false;

                if (show) {
                    card.style.display = 'flex';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            document.getElementById('visibleCount').textContent = visibleCount;
        }

        // Select all visible agents
        function selectAllVisible() {
            const visibleCards = document.querySelectorAll('.agent-selection-card[style*="flex"], .agent-selection-card:not([style])');

            visibleCards.forEach(card => {
                const checkbox = card.querySelector('.agent-checkbox');
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    card.classList.add('selected');
                }
            });

            updateSelection();
        }

        // Clear selection
        function clearSelection() {
            selectedAgents = [];

            document.querySelectorAll('.agent-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            document.querySelectorAll('.agent-selection-card').forEach(card => {
                card.classList.remove('selected');
            });

            updateSelection();
        }

        // Update selection
        function updateSelection() {
            selectedAgents = [];

            document.querySelectorAll('.agent-checkbox:checked').forEach(checkbox => {
                const card = checkbox.closest('.agent-selection-card');
                card.classList.add('selected');

                selectedAgents.push({
                    id: checkbox.value,
                    nombre: card.dataset.nombre,
                    sabre: card.dataset.sabre,
                    grupo: card.dataset.grupo,
                    avatar: card.querySelector('.agent-avatar-bulk').src
                });
            });

            document.querySelectorAll('.agent-checkbox:not(:checked)').forEach(checkbox => {
                const card = checkbox.closest('.agent-selection-card');
                card.classList.remove('selected');
            });

            document.getElementById('selectedCount').textContent = selectedAgents.length;
            document.getElementById('selectedAgentsCount').textContent = selectedAgents.length;

            updateSelectedAgentsList();
            updateStepNavigation();
        }

        // Update selected agents list
        function updateSelectedAgentsList() {
            const container = document.getElementById('selectedAgentsList');

            if (selectedAgents.length === 0) {
                container.innerHTML = `
                    <div class="empty-selection">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>No hay agentes seleccionados</p>
                        <small>Regresa al paso anterior para seleccionar agentes</small>
                    </div>
                `;
            } else {
                container.innerHTML = selectedAgents.map(agent => `
                    <div class="selected-agent-item">
                        <img src="${agent.avatar}" alt="Avatar" class="selected-agent-avatar">
                        <div class="selected-agent-info">
                            <h6>${agent.nombre}</h6>
                            <small>Sabre: ${agent.sabre} • ${agent.grupo || 'Sin grupo'}</small>
                        </div>
                    </div>
                `).join('');
            }
        }

        // Step navigation
        function nextStep() {
            if (currentStep === 1) {
                if (selectedAgents.length === 0) {
                    alert('Debes seleccionar al menos un agente para continuar.');
                    return;
                }
                goToStep(2);
            } else if (currentStep === 2) {
                if (!validateAssignmentForm()) {
                    return;
                }
                generateAssignmentSummary();
                goToStep(3);
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                goToStep(currentStep - 1);
            }
        }

        function goToStep(step) {
            currentStep = step;

            // Update tabs
            document.querySelectorAll('#bulkTabs .nav-link').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#bulkTabContent .tab-pane').forEach(pane => pane.classList.remove('show', 'active'));

            if (step === 1) {
                document.getElementById('select-agents-tab').classList.add('active');
                document.getElementById('select-agents').classList.add('show', 'active');
            } else if (step === 2) {
                document.getElementById('assign-functions-tab').classList.add('active');
                document.getElementById('assign-functions').classList.add('show', 'active');
                updateSelectedAgentsList();
            } else if (step === 3) {
                document.getElementById('review-assignments-tab').classList.add('active');
                document.getElementById('review-assignments').classList.add('show', 'active');
            }

            updateStepNavigation();
        }

        function updateStepNavigation() {
            const prevBtn = document.getElementById('prevStepBtn');
            const nextBtn = document.getElementById('nextStepBtn');

            if (currentStep === 1) {
                prevBtn.style.display = 'none';
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = 'Siguiente <i class="fas fa-arrow-right ms-2"></i>';
                nextBtn.disabled = selectedAgents.length === 0;
            } else if (currentStep === 2) {
                prevBtn.style.display = 'block';
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = 'Revisar <i class="fas fa-arrow-right ms-2"></i>';
                nextBtn.disabled = false;
            } else if (currentStep === 3) {
                prevBtn.style.display = 'block';
                nextBtn.style.display = 'none';
            }
        }

        // Validate assignment form
        function validateAssignmentForm() {
            const terminal = document.getElementById('bulkTerminal').value;
            const funcion = document.getElementById('bulkFuncion').value;
            const turno = document.getElementById('bulkTurno').value;
            const fecha = document.getElementById('bulkFecha').value;

            if (!terminal || !funcion || !turno || !fecha) {
                alert('Por favor completa todos los campos obligatorios.');
                return false;
            }

            return true;
        }

        // Generate assignment summary
        function generateAssignmentSummary() {
            const terminal = document.getElementById('bulkTerminal');
            const funcion = document.getElementById('bulkFuncion');
            const turno = document.getElementById('bulkTurno');
            const fecha = document.getElementById('bulkFecha').value;
            const observaciones = document.getElementById('bulkObservaciones').value;

            const summary = document.getElementById('assignmentSummary');

            summary.innerHTML = `
                <div class="summary-section">
                    <h6><i class="fas fa-info-circle me-2"></i>Detalles de la Asignación</h6>
                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="label">Terminal</div>
                            <div class="value">${terminal.options[terminal.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Función</div>
                            <div class="value">${funcion.options[funcion.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Turno</div>
                            <div class="value">${turno.options[turno.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Fecha</div>
                            <div class="value">${new Date(fecha).toLocaleDateString('es-ES')}</div>
                        </div>
                    </div>
                    ${observaciones ? `
                        <div class="summary-card">
                            <div class="label">Observaciones</div>
                            <div class="value">${observaciones}</div>
                        </div>
                    ` : ''}
                </div>

                <div class="summary-section">
                    <h6><i class="fas fa-users me-2"></i>Agentes Seleccionados (${selectedAgents.length})</h6>
                    <div class="selected-agents-list">
                        ${selectedAgents.map(agent => `
                            <div class="selected-agent-item">
                                <img src="${agent.avatar}" alt="Avatar" class="selected-agent-avatar">
                                <div class="selected-agent-info">
                                    <h6>${agent.nombre}</h6>
                                    <small>Sabre: ${agent.sabre} • ${agent.grupo || 'Sin grupo'}</small>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Go to previous step from review
        function goToPreviousStep() {
            goToStep(2);
        }

        // Confirm bulk assignments
        function confirmBulkAssignments() {
            if (confirm(`¿Estás seguro de crear ${selectedAgents.length} asignaciones?`)) {
                // Crear formulario para envío
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                // Datos de la asignación
                const terminal = document.getElementById('bulkTerminal').value;
                const funcion = document.getElementById('bulkFuncion').value;
                const turno = document.getElementById('bulkTurno').value;
                const fecha = document.getElementById('bulkFecha').value;
                const observaciones = document.getElementById('bulkObservaciones').value;

                // Agregar campos
                form.innerHTML = `
                    <input type="hidden" name="action" value="bulk_assign">
                    <input type="hidden" name="terminal_id" value="${terminal}">
                    <input type="hidden" name="funcion_id" value="${funcion}">
                    <input type="hidden" name="turno" value="${turno}">
                    <input type="hidden" name="fecha" value="${fecha}">
                    <input type="hidden" name="observaciones" value="${observaciones}">
                    <input type="hidden" name="agent_ids" value="${selectedAgents.map(a => a.id).join(',')}">
                `;

                document.body.appendChild(form);
                form.submit();
            }
        }

        // Mark all notifications as read
        function markAllAsRead() {
            // Implementar llamada AJAX para marcar todas como leídas
            console.log('Marcando todas las notificaciones como leídas...');
        }

        // Funciones para manejar solicitudes de asignación
        function processRequest(solicitudId, accion) {
            if (accion === 'aprobar') {
                if (confirm('¿Estás seguro de que deseas aprobar esta solicitud?')) {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="action" value="process_request">
                        <input type="hidden" name="decision" value="aprobar">
                        <input type="hidden" name="solicitud_id" value="${solicitudId}">
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            }
        }

        function showRejectModal(solicitudId) {
            document.getElementById('rejectSolicitudId').value = solicitudId;
            const modal = new bootstrap.Modal(document.getElementById('rejectRequestModal'));
            modal.show();
        }

        function refreshNotifications() {
            // Implementar recarga de notificaciones
            location.reload();
        }

        // Refresh notifications
        function refreshNotifications() {
            // Implementar recarga de notificaciones
            location.reload();
        }

        // Animaciones CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInNotification {
                from {
                    opacity: 0;
                    transform: translateX(20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Load saved theme and initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Sincronizar con localStorage
            localStorage.setItem('theme', currentTheme);

            // Aplicar tema inicial
            if (currentTheme === 'dark') {
                document.body.classList.add('theme-dark');
            }

            // Close notifications when clicking outside
            document.addEventListener('click', function(e) {
                const panel = document.getElementById('notificationsPanel');
                const bell = document.querySelector('.notification-bell');
                const overlay = document.getElementById('notificationsOverlay');

                if (!panel.contains(e.target) && !bell.contains(e.target)) {
                    closeNotifications();
                }
            });

            // Agregar efectos de hover a las tarjetas
            const cards = document.querySelectorAll('.stat-card, .agent-card, .management-panel');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('management-panel')) {
                        this.style.transform = 'translateY(-5px) scale(1.02)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('management-panel')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    }
                });
            });

            // JavaScript para panel ultra moderno
            const textareaUltra = document.querySelector('.ultra-textarea');
            const charCountUltra = document.getElementById('charCountUltra');

            if (textareaUltra && charCountUltra) {
                textareaUltra.addEventListener('input', function() {
                    const count = this.value.length;
                    charCountUltra.textContent = count;

                    if (count > 450) {
                        charCountUltra.style.color = '#f56565';
                    } else if (count > 350) {
                        charCountUltra.style.color = '#ed8936';
                    } else {
                        charCountUltra.style.color = '#a0aec0';
                    }
                });
            }

            // Animación del botón de envío ultra
            const submitBtnUltra = document.querySelector('.btn-submit-ultra');
            const requestFormUltra = document.getElementById('requestFormUltra');

            if (requestFormUltra && submitBtnUltra) {
                requestFormUltra.addEventListener('submit', function(e) {
                    submitBtnUltra.classList.add('loading');

                    // Simular delay para mostrar animación
                    setTimeout(() => {
                        // El formulario se enviará normalmente
                    }, 800);
                });
            }

            // Efectos especiales para las opciones de turno
            const turnoOptionsUltra = document.querySelectorAll('.turno-option-ultra');
            turnoOptionsUltra.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px) scale(1.03)';
                    this.style.boxShadow = '0 12px 30px rgba(0, 0, 0, 0.15)';
                });

                option.addEventListener('mouseleave', function() {
                    if (!this.previousElementSibling.checked) {
                        this.style.transform = 'translateY(0) scale(1)';
                        this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';
                    }
                });
            });

            // Animación de entrada para el panel ultra
            const ultraPanel = document.querySelector('.ultra-modern-request-panel');
            if (ultraPanel) {
                ultraPanel.style.opacity = '0';
                ultraPanel.style.transform = 'translateY(50px) scale(0.95)';

                setTimeout(() => {
                    ultraPanel.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
                    ultraPanel.style.opacity = '1';
                    ultraPanel.style.transform = 'translateY(0) scale(1)';
                }, 200);
            }

            // Validación en tiempo real para campos ultra
            const ultraFields = document.querySelectorAll('.ultra-select, .ultra-date');
            ultraFields.forEach(field => {
                field.addEventListener('change', function() {
                    if (this.value) {
                        this.style.borderColor = '#48bb78';
                        this.style.boxShadow = '0 0 0 6px rgba(72, 187, 120, 0.15)';

                        // Agregar checkmark visual
                        if (!this.parentNode.querySelector('.field-check')) {
                            const check = document.createElement('div');
                            check.className = 'field-check';
                            check.innerHTML = '<i class="fas fa-check"></i>';
                            check.style.cssText = `
                                position: absolute;
                                right: 50px;
                                top: 50%;
                                transform: translateY(-50%);
                                color: #48bb78;
                                font-size: 16px;
                                animation: checkAppear 0.3s ease-out;
                            `;
                            this.parentNode.appendChild(check);
                        }
                    } else {
                        this.style.borderColor = '#e2e8f0';
                        this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';

                        // Remover checkmark
                        const check = this.parentNode.querySelector('.field-check');
                        if (check) check.remove();
                    }
                });
            });

            // Agregar animación para checkmarks
            const checkStyle = document.createElement('style');
            checkStyle.textContent = `
                @keyframes checkAppear {
                    from { opacity: 0; transform: translateY(-50%) scale(0); }
                    to { opacity: 1; transform: translateY(-50%) scale(1); }
                }
            `;
            document.head.appendChild(checkStyle);

            console.log('✅ Asignaciones panel inicializado correctamente');
        });
    </script>
</body>
</html>
