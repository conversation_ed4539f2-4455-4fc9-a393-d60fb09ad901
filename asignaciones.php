<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

// Verificar permisos del usuario
$can_assign = hasPermission('manage_lobby') || hasPermission('manage_resources') || hasPermission('all_permissions');

// Obtener notificaciones
$notificaciones = getNotifications($agente['id']);

// Obtener todos los agentes para asignaciones
$agentes_query = "SELECT a.id, a.nombre, a.usuario_sabre, a.estado, a.foto_perfil, g.nombre as grupo_nombre
                  FROM agentes a
                  LEFT JOIN grupos g ON a.grupo_id = g.id
                  WHERE a.activo = 1
                  ORDER BY a.nombre";
$agentes_stmt = $conn->prepare($agentes_query);
$agentes_stmt->execute();
$todos_agentes = $agentes_stmt->fetchAll();

// Obtener terminales
$terminales_query = "SELECT * FROM terminales ORDER BY nombre";
$terminales_stmt = $conn->prepare($terminales_query);
$terminales_stmt->execute();
$terminales = $terminales_stmt->fetchAll();

// Obtener funciones de turno
$funciones_query = "SELECT * FROM funciones_turno ORDER BY nombre";
$funciones_stmt = $conn->prepare($funciones_query);
$funciones_stmt->execute();
$funciones = $funciones_stmt->fetchAll();

// Obtener asignaciones del día actual
$asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                              t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                              ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                              asignador.nombre as asignado_por_nombre
                       FROM asignaciones a
                       JOIN funciones_turno f ON a.funcion_id = f.id
                       JOIN terminales t ON a.terminal_id = t.id
                       JOIN agentes ag ON a.agente_id = ag.id
                       JOIN agentes asignador ON a.asignado_por = asignador.id
                       WHERE a.fecha = CURDATE()
                       ORDER BY t.nombre, f.nombre";
$asignaciones_stmt = $conn->prepare($asignaciones_query);
$asignaciones_stmt->execute();
$asignaciones_hoy = $asignaciones_stmt->fetchAll();

// Obtener estadísticas de agentes
$stats_query = "SELECT
    COUNT(CASE WHEN estado = '🟢Disponible' THEN 1 END) as disponibles,
    COUNT(CASE WHEN estado = '🟡En Colación' THEN 1 END) as en_colacion,
    COUNT(CASE WHEN estado = '🔴Ocupado' THEN 1 END) as ocupados,
    COUNT(CASE WHEN estado NOT IN ('🟢Disponible', '🟡En Colación', '🔴Ocupado') THEN 1 END) as fuera_turno
    FROM agentes WHERE activo = 1";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->execute();
$stats_agentes = $stats_stmt->fetch();

// Obtener agentes con asignaciones hoy
$agentes_con_asignaciones = array_unique(array_column($asignaciones_hoy, 'agente_id'));

// Obtener agentes disponibles sin asignaciones
$agentes_disponibles = array_filter($todos_agentes, function($agente) use ($agentes_con_asignaciones) {
    return $agente['estado'] === '🟢Disponible' && !in_array($agente['id'], $agentes_con_asignaciones);
});

// Obtener agentes en colación
$agentes_colacion = array_filter($todos_agentes, function($agente) {
    return $agente['estado'] === '🟡En Colación';
});

// Procesar formulario de asignación
$message = '';
$error = '';

if ($_POST && $can_assign) {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'assign') {
            $agente_id = $_POST['agente_id'];
            $funcion_id = $_POST['funcion_id'];
            $terminal_id = $_POST['terminal_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';

            try {
                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, asignado_por)
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);

                $message = "Asignación creada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?success=1");
                exit;
            } catch (Exception $e) {
                $error = "Error al crear la asignación: " . $e->getMessage();
            }
        } elseif ($_POST['action'] === 'remove') {
            $asignacion_id = $_POST['asignacion_id'];

            try {
                $delete_query = "DELETE FROM asignaciones WHERE id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->execute([$asignacion_id]);

                $message = "Asignación eliminada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?removed=1");
                exit;
            } catch (Exception $e) {
                $error = "Error al eliminar la asignación: " . $e->getMessage();
            }
        }
    }
}

if (isset($_GET['success'])) {
    $message = "Asignación creada exitosamente";
}
if (isset($_GET['removed'])) {
    $message = "Asignación eliminada exitosamente";
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asignaciones de Turno - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --gradient-admin: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            color: #2d3748;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-soft);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            margin: 20px;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            filter: brightness(0) invert(1);
        }

        .user-info {
            padding: 25px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            z-index: 0;
        }

        .user-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .user-name {
            color: #2d3748;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .user-group {
            color: #718096;
            position: relative;
            z-index: 1;
        }

        .status-badge {
            position: relative;
            z-index: 1;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.status-disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .status-badge.status-colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .status-badge.status-ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .status-badge.status-fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
            color: white;
        }

        .sidebar-nav {
            padding: 0 20px 20px;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            color: #4a5568;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid transparent;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            opacity: 1;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .nav-link i {
            width: 20px;
            margin-right: 15px;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* ===== TOP BAR ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .top-bar > * {
            position: relative;
            z-index: 1;
        }

        /* ===== NOTIFICATION BELL ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .notification-bell:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .notification-counter {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .notifications-panel {
            position: absolute;
            top: 60px;
            right: 0;
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-hover);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .notifications-panel.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notifications-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
            color: #2d3748;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME SELECTOR ===== */
        .theme-selector {
            display: flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .theme-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin: 0 2px;
        }

        .theme-btn.light {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .theme-btn.dark {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: #ddd;
        }

        .theme-btn.active {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* ===== STATS CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .stat-card.disponibles::before {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.asignados::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.colacion::before {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-card.fuera::before {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-card > * {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon.disponibles {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-icon.asignados {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-icon.fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 16px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* ===== AGENT CARDS ===== */
        .agents-section {
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .agent-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
            object-fit: cover;
        }

        .agent-details h6 {
            margin: 0;
            font-weight: 600;
            color: #2d3748;
        }

        .agent-details small {
            color: #6b7280;
            font-weight: 500;
        }

        .agent-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .agent-status.disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .agent-status.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .agent-status.ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .assignment-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 12px;
            margin-top: 10px;
        }

        .assignment-info h6 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 600;
        }

        .assignment-info p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }

        /* ===== ACTION BUTTONS ===== */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-assign {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-assign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-remove {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-remove:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
        }

        /* ===== ASSIGNMENT MODAL ===== */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }

        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .sidebar-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .user-info {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #f7fafc !important;
        }

        .theme-dark .user-name {
            color: #f7fafc !important;
        }

        .theme-dark .user-group {
            color: #cbd5e0 !important;
        }

        .theme-dark .nav-link {
            color: #e2e8f0 !important;
        }

        .theme-dark .nav-link:hover,
        .theme-dark .nav-link.active {
            color: white !important;
        }

        .theme-dark .top-bar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-number {
            color: #f7fafc !important;
        }

        .theme-dark .stat-label {
            color: #cbd5e0 !important;
        }

        .theme-dark .agent-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .agent-details h6 {
            color: #f7fafc !important;
        }

        .theme-dark .agent-details small {
            color: #cbd5e0 !important;
        }

        .theme-dark .section-title {
            color: #f7fafc !important;
        }

        .theme-dark .assignment-info {
            background: rgba(102, 126, 234, 0.2) !important;
        }

        .theme-dark .assignment-info h6 {
            color: #81e6d9 !important;
        }

        .theme-dark .assignment-info p {
            color: #cbd5e0 !important;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                z-index: 9999;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .agents-grid {
                grid-template-columns: 1fr;
            }

            .top-bar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1 user-name"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small class="user-group"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>"><?php echo $agente['estado']; ?></span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link active">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Lobby</span>
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    <span>CREC</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link">
                    <i class="fas fa-file-pdf"></i>
                    <span>Informativos</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Mi Perfil</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Buscar Agentes</span>
                </a>
            </div>
            <?php if (hasPermission('all_permissions')): ?>
            <div class="nav-item">
                <a href="admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="color: #2d3748; font-weight: 700;">
                        <i class="fas fa-tasks me-2" style="color: var(--primary-color);"></i>
                        Asignaciones de Turno
                    </h2>
                    <p class="mb-0" style="color: #6b7280; font-weight: 500;">
                        <?php echo date('l, j \d\e F \d\e Y'); ?> • <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <!-- Notification Bell -->
                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        <i class="fas fa-bell" style="color: white;"></i>
                        <?php if (count($notificaciones) > 0): ?>
                            <span class="notification-counter"><?php echo count($notificaciones); ?></span>
                        <?php endif; ?>
                    </div>

                    <div class="notifications-panel" id="notificationsPanel">
                        <div class="notifications-header">
                            <i class="fas fa-bell me-2"></i>
                            Notificaciones
                        </div>
                        <?php if (empty($notificaciones)): ?>
                            <div class="notification-item">
                                <p class="mb-0 text-muted">No hay notificaciones nuevas</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($notificaciones as $notif): ?>
                                <div class="notification-item <?php echo $notif['leida'] ? '' : 'unread'; ?>">
                                    <h6 class="mb-1"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                                    <p class="mb-1 text-muted"><?php echo htmlspecialchars($notif['mensaje']); ?></p>
                                    <small class="text-muted"><?php echo timeAgo($notif['created_at']); ?></small>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Theme Selector -->
                <div class="theme-selector">
                    <button class="theme-btn light active" onclick="setTheme('light')" title="Tema Claro">
                        <i class="fas fa-sun"></i>
                    </button>
                    <button class="theme-btn dark" onclick="setTheme('dark')" title="Tema Oscuro">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Estadísticas de Agentes -->
        <div class="stats-grid">
            <div class="stat-card disponibles">
                <div class="stat-icon disponibles">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['disponibles']; ?></div>
                <div class="stat-label">Disponibles</div>
            </div>

            <div class="stat-card asignados">
                <div class="stat-icon asignados">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="stat-number"><?php echo count($agentes_con_asignaciones); ?></div>
                <div class="stat-label">Con Asignaciones</div>
            </div>

            <div class="stat-card colacion">
                <div class="stat-icon colacion">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['en_colacion']; ?></div>
                <div class="stat-label">En Colación</div>
            </div>

            <div class="stat-card fuera">
                <div class="stat-icon fuera">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['fuera_turno']; ?></div>
                <div class="stat-label">Fuera de Turno</div>
            </div>
        </div>

        <?php if ($can_assign): ?>
        <!-- Botón para Nueva Asignación -->
        <div class="section-header">
            <h3 class="section-title">
                <i class="fas fa-plus-circle"></i>
                Gestión de Asignaciones
            </h3>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignModal">
                <i class="fas fa-plus me-2"></i>
                Nueva Asignación
            </button>
        </div>
        <?php endif; ?>

        <!-- Agentes Disponibles Sin Asignaciones -->
        <?php if (!empty($agentes_disponibles)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-check"></i>
                    Agentes Disponibles Sin Asignaciones (<?php echo count($agentes_disponibles); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_disponibles as $agente_disp): ?>
                <div class="agent-card">
                    <div class="agent-status disponible">Disponible</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_disp['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_disp['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_disp['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-assign" onclick="openAssignModal(<?php echo $agente_disp['id']; ?>, '<?php echo htmlspecialchars($agente_disp['nombre']); ?>')">
                            <i class="fas fa-plus me-1"></i>
                            Asignar Función
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes Con Asignaciones -->
        <?php if (!empty($asignaciones_hoy)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-cog"></i>
                    Agentes Con Asignaciones Hoy (<?php echo count($asignaciones_hoy); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($asignaciones_hoy as $asignacion): ?>
                <div class="agent-card">
                    <div class="agent-status ocupado">Asignado</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($asignacion['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($asignacion['agente_nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($asignacion['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <div class="assignment-info">
                        <h6><?php echo htmlspecialchars($asignacion['funcion_nombre']); ?></h6>
                        <p><?php echo htmlspecialchars($asignacion['terminal_nombre']); ?> • Turno <?php echo ucfirst($asignacion['turno']); ?></p>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-remove" onclick="removeAssignment(<?php echo $asignacion['id']; ?>)">
                            <i class="fas fa-times me-1"></i>
                            Remover
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes En Colación -->
        <?php if (!empty($agentes_colacion)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-coffee"></i>
                    Agentes En Colación (<?php echo count($agentes_colacion); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_colacion as $agente_col): ?>
                <div class="agent-card">
                    <div class="agent-status colacion">En Colación</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_col['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_col['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_col['usuario_sabre']); ?></small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    </div>

    <!-- Modal de Asignación -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        Nueva Asignación
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="assign">

                        <div class="mb-3">
                            <label class="form-label">Agente</label>
                            <select name="agente_id" class="form-select" required id="agenteSelect">
                                <option value="">Seleccionar agente...</option>
                                <?php foreach ($agentes_disponibles as $agente_opt): ?>
                                <option value="<?php echo $agente_opt['id']; ?>">
                                    <?php echo htmlspecialchars($agente_opt['nombre']); ?> (<?php echo htmlspecialchars($agente_opt['usuario_sabre']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Terminal</label>
                            <select name="terminal_id" class="form-select" required>
                                <option value="">Seleccionar terminal...</option>
                                <?php foreach ($terminales as $terminal): ?>
                                <option value="<?php echo $terminal['id']; ?>">
                                    <?php echo htmlspecialchars($terminal['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Función</label>
                            <select name="funcion_id" class="form-select" required>
                                <option value="">Seleccionar función...</option>
                                <?php foreach ($funciones as $funcion): ?>
                                <option value="<?php echo $funcion['id']; ?>">
                                    <?php echo htmlspecialchars($funcion['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Turno</label>
                            <select name="turno" class="form-select" required>
                                <option value="">Seleccionar turno...</option>
                                <option value="mañana">Mañana</option>
                                <option value="tarde">Tarde</option>
                                <option value="noche">Noche</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fecha</label>
                            <input type="date" name="fecha" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Observaciones (Opcional)</label>
                            <textarea name="observaciones" class="form-control" rows="3" placeholder="Observaciones adicionales..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Crear Asignación
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle sidebar
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Toggle notifications
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            panel.classList.toggle('show');
        }

        // Set theme
        function setTheme(theme) {
            document.body.className = theme === 'dark' ? 'theme-dark' : '';

            // Update active button
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`.theme-btn.${theme}`).classList.add('active');

            // Save preference
            localStorage.setItem('theme', theme);
        }

        // Open assign modal with pre-selected agent
        function openAssignModal(agenteId, agenteNombre) {
            const modal = new bootstrap.Modal(document.getElementById('assignModal'));
            const select = document.getElementById('agenteSelect');
            select.value = agenteId;
            modal.show();
        }

        // Remove assignment
        function removeAssignment(assignmentId) {
            if (confirm('¿Estás seguro de que deseas remover esta asignación?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="remove">
                    <input type="hidden" name="asignacion_id" value="${assignmentId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            setTheme(savedTheme);

            // Close notifications when clicking outside
            document.addEventListener('click', function(e) {
                const panel = document.getElementById('notificationsPanel');
                const bell = document.querySelector('.notification-bell');

                if (!panel.contains(e.target) && !bell.contains(e.target)) {
                    panel.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
