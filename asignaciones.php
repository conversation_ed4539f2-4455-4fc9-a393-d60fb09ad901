<?php
require_once 'config/functions.php';
requireLogin();

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

// Verificar permisos del usuario
$can_assign = hasPermission('manage_lobby') || hasPermission('manage_resources') || hasPermission('all_permissions');

// Obtener notificaciones
$notificaciones = getNotifications($agente['id']);

// Obtener todos los agentes para asignaciones
$agentes_query = "SELECT a.id, a.nombre, a.usuario_sabre, a.estado, a.foto_perfil, g.nombre as grupo_nombre
                  FROM agentes a
                  LEFT JOIN grupos g ON a.grupo_id = g.id
                  WHERE a.activo = 1
                  ORDER BY a.nombre";
$agentes_stmt = $conn->prepare($agentes_query);
$agentes_stmt->execute();
$todos_agentes = $agentes_stmt->fetchAll();

// Obtener terminales
$terminales_query = "SELECT * FROM terminales ORDER BY nombre";
$terminales_stmt = $conn->prepare($terminales_query);
$terminales_stmt->execute();
$terminales = $terminales_stmt->fetchAll();

// Obtener funciones de turno
$funciones_query = "SELECT * FROM funciones_turno ORDER BY nombre";
$funciones_stmt = $conn->prepare($funciones_query);
$funciones_stmt->execute();
$funciones = $funciones_stmt->fetchAll();



// Obtener asignaciones del día actual
try {
    // Intentar primero con asignado_por
    $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                  t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                  ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                  asignador.nombre as asignado_por_nombre
                           FROM asignaciones a
                           JOIN funciones_turno f ON a.funcion_id = f.id
                           JOIN terminales t ON a.terminal_id = t.id
                           JOIN agentes ag ON a.agente_id = ag.id
                           LEFT JOIN agentes asignador ON a.asignado_por = asignador.id
                           WHERE a.fecha = CURDATE()
                           ORDER BY t.nombre, f.nombre";
    $asignaciones_stmt = $conn->prepare($asignaciones_query);
    $asignaciones_stmt->execute();
    $asignaciones_hoy = $asignaciones_stmt->fetchAll();
} catch (Exception $e) {
    // Si falla, intentar con created_by
    try {
        $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                      t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                      ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                      asignador.nombre as asignado_por_nombre
                               FROM asignaciones a
                               JOIN funciones_turno f ON a.funcion_id = f.id
                               JOIN terminales t ON a.terminal_id = t.id
                               JOIN agentes ag ON a.agente_id = ag.id
                               LEFT JOIN agentes asignador ON a.created_by = asignador.id
                               WHERE a.fecha = CURDATE()
                               ORDER BY t.nombre, f.nombre";
        $asignaciones_stmt = $conn->prepare($asignaciones_query);
        $asignaciones_stmt->execute();
        $asignaciones_hoy = $asignaciones_stmt->fetchAll();
    } catch (Exception $e2) {
        // Si ambas fallan, obtener sin el asignador
        $asignaciones_query = "SELECT a.*, f.nombre as funcion_nombre, f.codigo as funcion_codigo,
                                      t.nombre as terminal_nombre, t.codigo as terminal_codigo,
                                      ag.nombre as agente_nombre, ag.usuario_sabre, ag.foto_perfil,
                                      'Sistema' as asignado_por_nombre
                               FROM asignaciones a
                               JOIN funciones_turno f ON a.funcion_id = f.id
                               JOIN terminales t ON a.terminal_id = t.id
                               JOIN agentes ag ON a.agente_id = ag.id
                               WHERE a.fecha = CURDATE()
                               ORDER BY t.nombre, f.nombre";
        $asignaciones_stmt = $conn->prepare($asignaciones_query);
        $asignaciones_stmt->execute();
        $asignaciones_hoy = $asignaciones_stmt->fetchAll();
        error_log("Error en consulta de asignaciones: " . $e->getMessage() . " | " . $e2->getMessage());
    }
}

// Obtener estadísticas de agentes
$stats_query = "SELECT
    COUNT(CASE WHEN estado = '🟢Disponible' THEN 1 END) as disponibles,
    COUNT(CASE WHEN estado = '🟡En Colación' THEN 1 END) as en_colacion,
    COUNT(CASE WHEN estado = '🔴Ocupado' THEN 1 END) as ocupados,
    COUNT(CASE WHEN estado NOT IN ('🟢Disponible', '🟡En Colación', '🔴Ocupado') THEN 1 END) as fuera_turno
    FROM agentes WHERE activo = 1";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->execute();
$stats_agentes = $stats_stmt->fetch();

// Obtener agentes con asignaciones hoy
$agentes_con_asignaciones = array_unique(array_column($asignaciones_hoy, 'agente_id'));

// Obtener agentes disponibles sin asignaciones
$agentes_disponibles = array_filter($todos_agentes, function($agente) use ($agentes_con_asignaciones) {
    return $agente['estado'] === '🟢Disponible' && !in_array($agente['id'], $agentes_con_asignaciones);
});

// Obtener agentes en colación
$agentes_colacion = array_filter($todos_agentes, function($agente) {
    return $agente['estado'] === '🟡En Colación';
});

// Procesar formulario de asignación
$message = '';
$error = '';



if ($_POST && $can_assign) {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'assign') {
            $agente_id = $_POST['agente_id'];
            $funcion_id = $_POST['funcion_id'];
            $terminal_id = $_POST['terminal_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';

            try {
                // Intentar primero con asignado_por
                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, asignado_por)
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);

                if (!$insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']])) {
                    // Si falla, intentar con created_by
                    $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                }

                $message = "Asignación creada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?success=1");
                exit;
            } catch (Exception $e) {
                // Si falla con asignado_por, intentar con created_by
                try {
                    $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                    VALUES (?, ?, ?, ?, ?, ?, ?)";
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->execute([$agente_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);

                    $message = "Asignación creada exitosamente";
                    header("Location: asignaciones.php?success=1");
                    exit;
                } catch (Exception $e2) {
                    $error = "Error al crear la asignación: " . $e->getMessage() . " | " . $e2->getMessage();
                    error_log("Error en asignación: " . $e->getMessage() . " | " . $e2->getMessage());
                }
            }
        } elseif ($_POST['action'] === 'remove') {
            $asignacion_id = $_POST['asignacion_id'];

            try {
                $delete_query = "DELETE FROM asignaciones WHERE id = ?";
                $delete_stmt = $conn->prepare($delete_query);
                $delete_stmt->execute([$asignacion_id]);

                $message = "Asignación eliminada exitosamente";

                // Recargar datos
                header("Location: asignaciones.php?removed=1");
                exit;
            } catch (Exception $e) {
                $error = "Error al eliminar la asignación: " . $e->getMessage();
            }
        } elseif ($_POST['action'] === 'bulk_assign') {
            $terminal_id = $_POST['terminal_id'];
            $funcion_id = $_POST['funcion_id'];
            $turno = $_POST['turno'];
            $fecha = $_POST['fecha'] ?? date('Y-m-d');
            $observaciones = $_POST['observaciones'] ?? '';
            $agent_ids = explode(',', $_POST['agent_ids']);

            try {
                $conn->beginTransaction();

                // Intentar primero con asignado_por
                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, asignado_por)
                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $conn->prepare($insert_query);

                $success_count = 0;
                $error_count = 0;
                $use_created_by = false;

                foreach ($agent_ids as $agent_id) {
                    if (empty(trim($agent_id))) continue;

                    try {
                        if (!$use_created_by) {
                            $result = $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                            if (!$result) {
                                // Si falla, cambiar a created_by
                                $use_created_by = true;
                                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                            }
                        } else {
                            $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                        }
                        $success_count++;
                    } catch (Exception $e) {
                        // Si es el primer error y no hemos probado created_by, intentarlo
                        if (!$use_created_by && $error_count == 0) {
                            try {
                                $use_created_by = true;
                                $insert_query = "INSERT INTO asignaciones (agente_id, funcion_id, terminal_id, turno, fecha, observaciones, created_by)
                                                VALUES (?, ?, ?, ?, ?, ?, ?)";
                                $insert_stmt = $conn->prepare($insert_query);
                                $insert_stmt->execute([$agent_id, $funcion_id, $terminal_id, $turno, $fecha, $observaciones, $agente['id']]);
                                $success_count++;
                            } catch (Exception $e2) {
                                $error_count++;
                                error_log("Error en asignación masiva para agente $agent_id: " . $e->getMessage() . " | " . $e2->getMessage());
                            }
                        } else {
                            $error_count++;
                            error_log("Error en asignación masiva para agente $agent_id: " . $e->getMessage());
                        }
                    }
                }

                $conn->commit();

                if ($success_count > 0) {
                    $message = "Se crearon $success_count asignaciones exitosamente";
                    if ($error_count > 0) {
                        $message .= " ($error_count fallaron)";
                    }
                } else {
                    $error = "No se pudo crear ninguna asignación";
                }

                // Recargar datos
                header("Location: asignaciones.php?bulk_success=$success_count&bulk_errors=$error_count");
                exit;

            } catch (Exception $e) {
                $conn->rollBack();
                $error = "Error en asignaciones masivas: " . $e->getMessage();
                error_log("Error en transacción masiva: " . $e->getMessage());
            }
        }
    }
}

if (isset($_GET['success'])) {
    $message = "Asignación creada exitosamente";
}
if (isset($_GET['removed'])) {
    $message = "Asignación eliminada exitosamente";
}
if (isset($_GET['bulk_success'])) {
    $success_count = (int)$_GET['bulk_success'];
    $error_count = (int)($_GET['bulk_errors'] ?? 0);

    if ($success_count > 0) {
        $message = "Se crearon $success_count asignaciones exitosamente";
        if ($error_count > 0) {
            $message .= " ($error_count fallaron)";
        }
    } else {
        $error = "No se pudo crear ninguna asignación masiva";
    }
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asignaciones de Turno - SwissportAgents</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/theme-selector.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --gradient-warning: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            --gradient-info: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            --gradient-admin: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            --border-radius: 20px;
            --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 20px 60px rgba(0, 0, 0, 0.15);
            --sidebar-width: 280px;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: var(--sidebar-width);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            color: #2d3748;
            z-index: 1000;
            overflow-y: auto;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-soft);
        }

        .sidebar-header {
            padding: 30px 20px 20px;
            text-align: center;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            margin: 20px;
            border-radius: var(--border-radius);
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .sidebar-header img {
            max-width: 120px;
            height: auto;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
            filter: brightness(0) invert(1);
        }

        .user-info {
            padding: 25px 20px;
            text-align: center;
            background: rgba(255, 255, 255, 0.8);
            margin: 0 20px 20px;
            border-radius: var(--border-radius);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
        }

        .user-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
            z-index: 0;
        }

        .user-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin-bottom: 15px;
            border: 3px solid var(--primary-color);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .user-name {
            color: #2d3748;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .user-group {
            color: #718096;
            position: relative;
            z-index: 1;
        }

        .status-badge {
            position: relative;
            z-index: 1;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: inline-block;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge.status-disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .status-badge.status-colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .status-badge.status-ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .status-badge.status-fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
            color: white;
        }

        .sidebar-nav {
            padding: 0 20px 20px;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            color: #4a5568;
            padding: 15px 20px;
            text-decoration: none;
            border-radius: 15px;
            display: flex;
            align-items: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 500;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid transparent;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            opacity: 1;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            transform: translateX(5px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            border-color: rgba(255, 255, 255, 0.2);
        }

        .nav-link i {
            width: 20px;
            margin-right: 15px;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .nav-link span {
            position: relative;
            z-index: 1;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* ===== TOP BAR ===== */
        .top-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 25px 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .top-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .top-bar > * {
            position: relative;
            z-index: 1;
        }

        /* ===== NOTIFICATION BELL ===== */
        .notification-bell-container {
            position: relative;
        }

        .notification-bell {
            width: 50px;
            height: 50px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
        }

        .notification-bell:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .notification-counter {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #ff4757;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 11px;
            font-weight: 600;
        }

        .notifications-panel {
            position: absolute;
            top: 60px;
            right: 0;
            width: 350px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-hover);
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
            display: none;
            max-height: 400px;
            overflow-y: auto;
        }

        .notifications-panel.show {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .notifications-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            font-weight: 600;
            color: #2d3748;
        }

        .notification-item {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            transition: background 0.3s ease;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .notification-item.unread {
            background: rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME SELECTOR ===== */
        .theme-selector {
            display: flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 25px;
            padding: 5px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .theme-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin: 0 2px;
        }

        .theme-btn.light {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .theme-btn.dark {
            background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
            color: #ddd;
        }

        .theme-btn.active {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* ===== STATS CARDS ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .stat-card.disponibles::before {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-card.asignados::before {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-card.colacion::before {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-card.fuera::before {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-hover);
        }

        .stat-card:hover::before {
            opacity: 0.05;
        }

        .stat-card > * {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            margin-bottom: 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .stat-icon.disponibles {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-icon.asignados {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-icon.fuera {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .stat-number {
            font-size: 36px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 16px;
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* ===== AGENT CARDS ===== */
        .agents-section {
            margin-bottom: 30px;
        }

        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-title i {
            color: var(--primary-color);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .agent-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .agent-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-hover);
        }

        .agent-info {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
            object-fit: cover;
        }

        .agent-details h6 {
            margin: 0;
            font-weight: 600;
            color: #2d3748;
        }

        .agent-details small {
            color: #6b7280;
            font-weight: 500;
        }

        .agent-status {
            position: absolute;
            top: 15px;
            right: 15px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .agent-status.disponible {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            color: white;
        }

        .agent-status.colacion {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
            color: white;
        }

        .agent-status.ocupado {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
        }

        .assignment-info {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 12px;
            margin-top: 10px;
        }

        .assignment-info h6 {
            margin: 0 0 5px 0;
            color: var(--primary-color);
            font-size: 14px;
            font-weight: 600;
        }

        .assignment-info p {
            margin: 0;
            font-size: 12px;
            color: #6b7280;
        }

        /* ===== MANAGEMENT PANEL ===== */
        .management-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-soft);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
        }

        .management-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
            z-index: 0;
        }

        .management-panel > * {
            position: relative;
            z-index: 1;
        }

        .management-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .management-title-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .management-icon {
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .management-text h3 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            color: #2d3748;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .management-text p {
            margin: 5px 0 0 0;
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
        }

        .management-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn-new-assignment,
        .btn-bulk-actions {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 2px solid transparent;
            border-radius: 16px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
            min-width: 280px;
        }

        .btn-new-assignment::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .btn-bulk-actions::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 0;
        }

        .btn-new-assignment:hover::before,
        .btn-bulk-actions:hover::before {
            opacity: 1;
        }

        .btn-new-assignment:hover,
        .btn-bulk-actions:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            color: white;
        }

        .btn-new-assignment > *,
        .btn-bulk-actions > * {
            position: relative;
            z-index: 1;
        }

        .btn-icon {
            width: 50px;
            height: 50px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-new-assignment:hover .btn-icon,
        .btn-bulk-actions:hover .btn-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: scale(1.1);
        }

        .btn-text {
            flex: 1;
        }

        .btn-title {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 2px;
            transition: color 0.3s ease;
        }

        .btn-subtitle {
            display: block;
            font-size: 14px;
            color: #6b7280;
            transition: color 0.3s ease;
        }

        .btn-new-assignment:hover .btn-title,
        .btn-new-assignment:hover .btn-subtitle,
        .btn-bulk-actions:hover .btn-title,
        .btn-bulk-actions:hover .btn-subtitle {
            color: white;
        }

        .btn-arrow {
            font-size: 16px;
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-new-assignment:hover .btn-arrow,
        .btn-bulk-actions:hover .btn-arrow {
            color: white;
            transform: translateX(5px);
        }

        .management-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .stat-item .stat-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .stat-item .stat-icon.disponibles {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .stat-item .stat-icon.asignados {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-item .stat-icon.terminales {
            background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        }

        .stat-item .stat-icon.funciones {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .stat-info {
            display: flex;
            flex-direction: column;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #2d3748;
            line-height: 1;
        }

        .stat-label {
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* ===== ACTION BUTTONS ===== */
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-assign {
            background: var(--gradient-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-assign:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-remove {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn-remove:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(245, 101, 101, 0.3);
        }

        /* ===== ASSIGNMENT MODAL ===== */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
        }

        .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid rgba(0, 0, 0, 0.1);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* ===== THEME DARK ===== */
        .theme-dark {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
        }

        .theme-dark .sidebar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border-right: 1px solid #4a5568 !important;
        }

        .theme-dark .sidebar-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .user-info {
            background: rgba(45, 55, 72, 0.8) !important;
            color: #f7fafc !important;
        }

        .theme-dark .user-name {
            color: #f7fafc !important;
        }

        .theme-dark .user-group {
            color: #cbd5e0 !important;
        }

        .theme-dark .nav-link {
            color: #e2e8f0 !important;
        }

        .theme-dark .nav-link:hover,
        .theme-dark .nav-link.active {
            color: white !important;
        }

        .theme-dark .top-bar {
            background: rgba(26, 32, 44, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .stat-number {
            color: #f7fafc !important;
        }

        .theme-dark .stat-label {
            color: #cbd5e0 !important;
        }

        .theme-dark .agent-card {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .agent-details h6 {
            color: #f7fafc !important;
        }

        .theme-dark .agent-details small {
            color: #cbd5e0 !important;
        }

        .theme-dark .section-title {
            color: #f7fafc !important;
        }

        .theme-dark .assignment-info {
            background: rgba(102, 126, 234, 0.2) !important;
        }

        .theme-dark .assignment-info h6 {
            color: #81e6d9 !important;
        }

        .theme-dark .assignment-info p {
            color: #cbd5e0 !important;
        }

        .theme-dark .management-panel {
            background: rgba(45, 55, 72, 0.95) !important;
            color: #f7fafc !important;
            border: 1px solid #4a5568 !important;
        }

        .theme-dark .management-text h3 {
            color: #f7fafc !important;
            -webkit-text-fill-color: #f7fafc !important;
        }

        .theme-dark .management-text p {
            color: #cbd5e0 !important;
        }

        .theme-dark .btn-new-assignment,
        .theme-dark .btn-bulk-actions {
            background: rgba(26, 32, 44, 0.9) !important;
            color: #f7fafc !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .btn-title {
            color: #f7fafc !important;
        }

        .theme-dark .btn-subtitle {
            color: #cbd5e0 !important;
        }

        .theme-dark .stat-item {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
        }

        .theme-dark .stat-number {
            color: #f7fafc !important;
        }

        .theme-dark .stat-label {
            color: #cbd5e0 !important;
        }

        /* ===== NOTIFICATIONS PANEL STYLES ===== */
        .notifications-panel {
            position: fixed;
            top: 0;
            right: -450px;
            width: 420px;
            height: 100vh;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-left: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 10000;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            box-shadow: -10px 0 30px rgba(0, 0, 0, 0.1);
        }

        .notifications-panel.show {
            right: 0;
        }

        .notifications-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(2px);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .notifications-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .notifications-header {
            padding: 30px 25px 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background: var(--gradient-primary);
            color: white;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .notifications-title {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notifications-count {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .notifications-actions {
            display: flex;
            gap: 10px;
        }

        .btn-notification-action {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-notification-action:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .notifications-list {
            padding: 0;
        }

        .notification-item {
            padding: 20px 25px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .notification-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .notification-item.unread::before {
            opacity: 1;
        }

        .notification-item:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: translateX(5px);
        }

        .notification-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            flex-shrink: 0;
        }

        .notification-icon.operacional {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .notification-icon.social {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .notification-icon.urgente {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .notification-icon.informativo {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
        }

        .notification-icon.general {
            background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            margin: 0 0 5px 0;
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            line-height: 1.3;
        }

        .notification-message {
            margin: 0 0 8px 0;
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
        }

        .notification-time {
            font-size: 11px;
            color: #9ca3af;
            font-weight: 500;
        }

        .empty-notifications {
            text-align: center;
            padding: 60px 25px;
            color: #6b7280;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .empty-notifications h6 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: #4a5568;
        }

        .empty-notifications p {
            margin: 0;
            font-size: 14px;
        }

        /* Dark theme notifications */
        .theme-dark .notifications-panel {
            background: rgba(26, 32, 44, 0.98) !important;
            border-left-color: #4a5568 !important;
        }

        .theme-dark .notifications-header {
            background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%) !important;
        }

        .theme-dark .notification-item {
            border-bottom-color: #4a5568 !important;
        }

        .theme-dark .notification-item:hover {
            background: rgba(102, 126, 234, 0.1) !important;
        }

        .theme-dark .notification-title {
            color: #f7fafc !important;
        }

        .theme-dark .notification-message {
            color: #cbd5e0 !important;
        }

        .theme-dark .notification-time {
            color: #a0aec0 !important;
        }

        .theme-dark .empty-notifications h6 {
            color: #f7fafc !important;
        }

        .theme-dark .empty-notifications p {
            color: #cbd5e0 !important;
        }

        /* ===== BULK ASSIGNMENT MODAL ===== */
        #bulkAssignModal .modal-dialog {
            max-width: 1200px;
        }

        #bulkAssignModal .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--shadow-hover);
        }

        #bulkAssignModal .modal-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 25px 30px;
        }

        #bulkAssignModal .modal-title {
            font-size: 24px;
            font-weight: 700;
        }

        #bulkAssignModal .modal-body {
            padding: 30px;
        }

        /* Tabs Navigation */
        .nav-pills .nav-link {
            background: rgba(102, 126, 234, 0.1);
            color: var(--primary-color);
            border-radius: 12px;
            padding: 12px 20px;
            margin-right: 10px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .nav-pills .nav-link:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .nav-pills .nav-link.active {
            background: var(--gradient-primary);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        /* Filter Panel */
        .filter-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .filter-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .filter-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .selection-summary {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 12px;
            padding: 15px;
            margin-top: 20px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .summary-label {
            font-weight: 600;
            color: #4a5568;
        }

        .summary-count {
            font-weight: 700;
            color: var(--primary-color);
        }

        /* Agents Selection Panel */
        .agents-selection-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            max-height: 600px;
            overflow-y: auto;
        }

        .panel-title {
            color: #2d3748;
            font-weight: 700;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(102, 126, 234, 0.1);
        }

        .agents-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 15px;
        }

        .agent-selection-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            padding: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .agent-selection-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(102, 126, 234, 0.3);
        }

        .agent-selection-card.selected {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
        }

        .selection-checkbox {
            position: relative;
        }

        .agent-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }

        .agent-avatar-section {
            position: relative;
        }

        .agent-avatar-bulk {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 3px solid var(--primary-color);
            object-fit: cover;
        }

        .agent-status-bulk {
            position: absolute;
            bottom: -2px;
            right: -2px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .agent-status-bulk.status-disponible {
            background: #48bb78;
        }

        .agent-status-bulk.status-colacion {
            background: #ed8936;
        }

        .agent-status-bulk.status-ocupado {
            background: #f56565;
        }

        .agent-status-bulk.status-fuera {
            background: #a0aec0;
        }

        .agent-info-bulk {
            flex: 1;
        }

        .agent-name-bulk {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .agent-sabre-bulk,
        .agent-grupo-bulk {
            margin: 0 0 3px 0;
            font-size: 12px;
            color: #6b7280;
        }

        /* Assignment Config Panel */
        .assignment-config-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            height: fit-content;
        }

        /* Selected Agents Panel */
        .selected-agents-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            max-height: 500px;
            overflow-y: auto;
        }

        .selected-agents-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .empty-selection {
            text-align: center;
            padding: 40px 20px;
            color: #6b7280;
        }

        .selected-agent-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .selected-agent-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
        }

        .selected-agent-info h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
        }

        .selected-agent-info small {
            color: #6b7280;
        }

        /* Review Panel */
        .review-panel {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 16px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .assignment-summary {
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .summary-section {
            margin-bottom: 20px;
        }

        .summary-section h6 {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 10px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        .summary-card .label {
            font-size: 12px;
            color: #6b7280;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .summary-card .value {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
        }

        .confirmation-actions {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding-top: 25px;
        }

        /* Step Navigation */
        .step-navigation {
            display: flex;
            gap: 10px;
        }

        /* Dark Theme for Bulk Modal */
        .theme-dark #bulkAssignModal .modal-content {
            background: #2d3748 !important;
            color: #f7fafc !important;
        }

        .theme-dark .filter-panel,
        .theme-dark .agents-selection-panel,
        .theme-dark .assignment-config-panel,
        .theme-dark .selected-agents-panel,
        .theme-dark .review-panel {
            background: rgba(26, 32, 44, 0.8) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .filter-title,
        .theme-dark .panel-title {
            color: #f7fafc !important;
        }

        .theme-dark .agent-selection-card {
            background: rgba(45, 55, 72, 0.9) !important;
            color: #f7fafc !important;
        }

        .theme-dark .agent-name-bulk {
            color: #f7fafc !important;
        }

        .theme-dark .agent-sabre-bulk,
        .theme-dark .agent-grupo-bulk {
            color: #cbd5e0 !important;
        }

        .theme-dark .summary-card {
            background: rgba(45, 55, 72, 0.8) !important;
            border-color: #4a5568 !important;
            color: #f7fafc !important;
        }

        .theme-dark .summary-card .value {
            color: #f7fafc !important;
        }

        /* ===== RESPONSIVE ===== */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                z-index: 9999;
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .agents-grid {
                grid-template-columns: 1fr;
            }

            .top-bar {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <img src="assets/images/logo-swissport.png" alt="Swissport Logo">
        </div>

        <div class="user-info">
            <img src="<?php echo getAvatarUrl($agente['foto_perfil']); ?>" alt="Avatar" class="user-avatar">
            <h6 class="mb-1 user-name"><?php echo htmlspecialchars($agente['nombre']); ?></h6>
            <small class="user-group"><?php echo htmlspecialchars($agente['grupo_nombre'] ?? 'Sin grupo asignado'); ?></small>
            <div class="mt-2">
                <span class="status-badge <?php
                    echo match($agente['estado']) {
                        '🟢Disponible' => 'status-disponible',
                        '🟡En Colación' => 'status-colacion',
                        '🔴Ocupado' => 'status-ocupado',
                        default => 'status-fuera'
                    };
                ?>"><?php echo $agente['estado']; ?></span>
            </div>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-item">
                <a href="index.php" class="nav-link">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="asignaciones.php" class="nav-link active">
                    <i class="fas fa-tasks"></i>
                    <span>Asignaciones</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="colaciones/ingreso.php" class="nav-link">
                    <i class="fas fa-coffee"></i>
                    <span>Colaciones</span>
                </a>
            </div>
            <?php if (hasPermission('manage_lobby')): ?>
            <div class="nav-item">
                <a href="lobby/gendec.php" class="nav-link">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Lobby</span>
                </a>
            </div>
            <?php endif; ?>
            <?php if (hasPermission('manage_resources')): ?>
            <div class="nav-item">
                <a href="crec/vuelos.php" class="nav-link">
                    <i class="fas fa-plane"></i>
                    <span>CREC</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item">
                <a href="informativos.php" class="nav-link">
                    <i class="fas fa-file-pdf"></i>
                    <span>Informativos</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="perfil.php" class="nav-link">
                    <i class="fas fa-user"></i>
                    <span>Mi Perfil</span>
                </a>
            </div>
            <div class="nav-item">
                <a href="buscar-agentes.php" class="nav-link">
                    <i class="fas fa-search"></i>
                    <span>Buscar Agentes</span>
                </a>
            </div>
            <?php if (hasPermission('all_permissions')): ?>
            <div class="nav-item">
                <a href="admin.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    <span>Administración</span>
                </a>
            </div>
            <?php endif; ?>
            <div class="nav-item mt-3">
                <a href="logout.php" class="nav-link text-danger">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Cerrar Sesión</span>
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div class="d-flex align-items-center">
                <button class="btn btn-link d-md-none me-3" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                <div>
                    <h2 class="mb-1" style="color: #2d3748; font-weight: 700;">
                        <i class="fas fa-tasks me-2" style="color: var(--primary-color);"></i>
                        Asignaciones de Turno
                    </h2>
                    <p class="mb-0" style="color: #6b7280; font-weight: 500;">
                        <?php echo date('l, j \d\e F \d\e Y'); ?> • <?php echo date('H:i'); ?>
                    </p>
                </div>
            </div>

            <div class="d-flex align-items-center gap-3">
                <!-- Panel de Notificaciones -->
                <div class="notification-bell-container">
                    <div class="notification-bell" onclick="toggleNotifications()">
                        🔔
                        <?php if (count($notificaciones) > 0): ?>
                            <span class="notification-counter"><?php echo count($notificaciones); ?></span>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Selector de tema moderno -->
                <?php
                $current_theme = getUserTheme();
                echo renderModernThemeSelector($current_theme);
                ?>
            </div>
        </div>

        <!-- Mensajes de éxito/error -->
        <?php if (!empty($message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (!empty($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Estadísticas de Agentes -->
        <div class="stats-grid">
            <div class="stat-card disponibles">
                <div class="stat-icon disponibles">
                    <i class="fas fa-user-check"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['disponibles']; ?></div>
                <div class="stat-label">Disponibles</div>
            </div>

            <div class="stat-card asignados">
                <div class="stat-icon asignados">
                    <i class="fas fa-user-cog"></i>
                </div>
                <div class="stat-number"><?php echo count($agentes_con_asignaciones); ?></div>
                <div class="stat-label">Con Asignaciones</div>
            </div>

            <div class="stat-card colacion">
                <div class="stat-icon colacion">
                    <i class="fas fa-coffee"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['en_colacion']; ?></div>
                <div class="stat-label">En Colación</div>
            </div>

            <div class="stat-card fuera">
                <div class="stat-icon fuera">
                    <i class="fas fa-user-times"></i>
                </div>
                <div class="stat-number"><?php echo $stats_agentes['fuera_turno']; ?></div>
                <div class="stat-label">Fuera de Turno</div>
            </div>
        </div>

        <?php if ($can_assign): ?>
        <!-- Panel de Gestión de Asignaciones Moderno -->
        <div class="management-panel">
            <div class="management-header">
                <div class="management-title-section">
                    <div class="management-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="management-text">
                        <h3 class="management-title">Gestión de Asignaciones</h3>
                        <p class="management-subtitle">Administra las funciones y turnos del personal</p>
                    </div>
                </div>

                <div class="management-actions">
                    <button class="btn-new-assignment" data-bs-toggle="modal" data-bs-target="#assignModal">
                        <div class="btn-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="btn-text">
                            <span class="btn-title">Nueva Asignación</span>
                            <span class="btn-subtitle">Asignar función a agente</span>
                        </div>
                        <div class="btn-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>

                    <button class="btn-bulk-actions" onclick="showBulkActions()">
                        <div class="btn-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <div class="btn-text">
                            <span class="btn-title">Acciones Masivas</span>
                            <span class="btn-subtitle">Gestionar múltiples asignaciones</span>
                        </div>
                        <div class="btn-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </button>
                </div>
            </div>

            <div class="management-stats">
                <div class="stat-item">
                    <div class="stat-icon disponibles">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($agentes_disponibles); ?></span>
                        <span class="stat-label">Disponibles</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon asignados">
                        <i class="fas fa-user-cog"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($asignaciones_hoy); ?></span>
                        <span class="stat-label">Asignados</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon terminales">
                        <i class="fas fa-building"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($terminales); ?></span>
                        <span class="stat-label">Terminales</span>
                    </div>
                </div>

                <div class="stat-item">
                    <div class="stat-icon funciones">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="stat-info">
                        <span class="stat-number"><?php echo count($funciones); ?></span>
                        <span class="stat-label">Funciones</span>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes Disponibles Sin Asignaciones -->
        <?php if (!empty($agentes_disponibles)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-check"></i>
                    Agentes Disponibles Sin Asignaciones (<?php echo count($agentes_disponibles); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_disponibles as $agente_disp): ?>
                <div class="agent-card">
                    <div class="agent-status disponible">Disponible</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_disp['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_disp['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_disp['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-assign" onclick="openAssignModal(<?php echo $agente_disp['id']; ?>, '<?php echo htmlspecialchars($agente_disp['nombre']); ?>')">
                            <i class="fas fa-plus me-1"></i>
                            Asignar Función
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes Con Asignaciones -->
        <?php if (!empty($asignaciones_hoy)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-user-cog"></i>
                    Agentes Con Asignaciones Hoy (<?php echo count($asignaciones_hoy); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($asignaciones_hoy as $asignacion): ?>
                <div class="agent-card">
                    <div class="agent-status ocupado">Asignado</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($asignacion['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($asignacion['agente_nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($asignacion['usuario_sabre']); ?></small>
                        </div>
                    </div>

                    <div class="assignment-info">
                        <h6><?php echo htmlspecialchars($asignacion['funcion_nombre']); ?></h6>
                        <p><?php echo htmlspecialchars($asignacion['terminal_nombre']); ?> • Turno <?php echo ucfirst($asignacion['turno']); ?></p>
                    </div>

                    <?php if ($can_assign): ?>
                    <div class="action-buttons">
                        <button class="btn-remove" onclick="removeAssignment(<?php echo $asignacion['id']; ?>)">
                            <i class="fas fa-times me-1"></i>
                            Remover
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Agentes En Colación -->
        <?php if (!empty($agentes_colacion)): ?>
        <div class="agents-section">
            <div class="section-header">
                <h3 class="section-title">
                    <i class="fas fa-coffee"></i>
                    Agentes En Colación (<?php echo count($agentes_colacion); ?>)
                </h3>
            </div>

            <div class="agents-grid">
                <?php foreach ($agentes_colacion as $agente_col): ?>
                <div class="agent-card">
                    <div class="agent-status colacion">En Colación</div>
                    <div class="agent-info">
                        <img src="<?php echo getAvatarUrl($agente_col['foto_perfil']); ?>" alt="Avatar" class="agent-avatar">
                        <div class="agent-details">
                            <h6><?php echo htmlspecialchars($agente_col['nombre']); ?></h6>
                            <small>Sabre: <?php echo htmlspecialchars($agente_col['usuario_sabre']); ?></small>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    </div>

    <!-- Panel de Notificaciones (fuera del navbar) -->
    <div class="notifications-panel" id="notificationsPanel">
        <!-- Header del panel -->
        <div class="notifications-header">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="notifications-title">
                    🔔 Notificaciones
                    <?php if (count($notificaciones) > 0): ?>
                        <span class="notifications-count"><?php echo count($notificaciones); ?></span>
                    <?php endif; ?>
                </h6>
                <div class="notifications-actions">
                    <button class="btn-notification-action" onclick="markAllAsRead()" title="Marcar todas como leídas">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button class="btn-notification-action" onclick="refreshNotifications()" title="Actualizar">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Lista de notificaciones -->
        <div class="notifications-list">
            <?php if (empty($notificaciones)): ?>
                <div class="empty-notifications">
                    <div class="empty-icon">📭</div>
                    <h6>¡Todo al día!</h6>
                    <p>No tienes notificaciones pendientes</p>
                </div>
            <?php else: ?>
                <?php foreach ($notificaciones as $index => $notif): ?>
                    <div class="notification-item <?php echo $notif['leida'] ? 'read' : 'unread'; ?>"
                         data-category="<?php echo $notif['categoria'] ?? 'general'; ?>"
                         data-id="<?php echo $notif['id']; ?>"
                         style="animation-delay: <?php echo $index * 0.1; ?>s">
                        <div class="notification-icon <?php echo $notif['categoria'] ?? 'general'; ?>">
                            <?php
                            $icon = match($notif['categoria'] ?? 'general') {
                                'operacional' => '✈️',
                                'social' => '👥',
                                'urgente' => '🚨',
                                'informativo' => 'ℹ️',
                                default => '📢'
                            };
                            echo $icon;
                            ?>
                        </div>
                        <div class="notification-content">
                            <h6 class="notification-title"><?php echo htmlspecialchars($notif['titulo']); ?></h6>
                            <p class="notification-message"><?php echo htmlspecialchars($notif['mensaje']); ?></p>
                            <span class="notification-time"><?php echo timeAgo($notif['created_at']); ?></span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Overlay para notificaciones -->
    <div class="notifications-overlay" id="notificationsOverlay" onclick="closeNotifications()"></div>

    <!-- Modal de Asignación -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="assignModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-plus me-2"></i>
                        Nueva Asignación
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="assign">

                        <div class="mb-3">
                            <label class="form-label">Agente</label>
                            <select name="agente_id" class="form-select" required id="agenteSelect">
                                <option value="">Seleccionar agente...</option>
                                <?php foreach ($agentes_disponibles as $agente_opt): ?>
                                <option value="<?php echo $agente_opt['id']; ?>">
                                    <?php echo htmlspecialchars($agente_opt['nombre']); ?> (<?php echo htmlspecialchars($agente_opt['usuario_sabre']); ?>)
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Terminal</label>
                            <select name="terminal_id" class="form-select" required>
                                <option value="">Seleccionar terminal...</option>
                                <?php foreach ($terminales as $terminal): ?>
                                <option value="<?php echo $terminal['id']; ?>">
                                    <?php echo htmlspecialchars($terminal['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Función</label>
                            <select name="funcion_id" class="form-select" required>
                                <option value="">Seleccionar función...</option>
                                <?php foreach ($funciones as $funcion): ?>
                                <option value="<?php echo $funcion['id']; ?>">
                                    <?php echo htmlspecialchars($funcion['nombre']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Turno</label>
                            <select name="turno" class="form-select" required>
                                <option value="">Seleccionar turno...</option>
                                <option value="mañana">Mañana</option>
                                <option value="tarde">Tarde</option>
                                <option value="noche">Noche</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Fecha</label>
                            <input type="date" name="fecha" class="form-control" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Observaciones (Opcional)</label>
                            <textarea name="observaciones" class="form-control" rows="3" placeholder="Observaciones adicionales..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            Crear Asignación
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Modal de Asignaciones Masivas -->
    <?php if ($can_assign): ?>
    <div class="modal fade" id="bulkAssignModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-layer-group me-2"></i>
                        Asignaciones Masivas
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs de navegación -->
                    <ul class="nav nav-pills mb-4" id="bulkTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="select-agents-tab" data-bs-toggle="pill" data-bs-target="#select-agents" type="button" role="tab">
                                <i class="fas fa-users me-2"></i>
                                1. Seleccionar Agentes
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="assign-functions-tab" data-bs-toggle="pill" data-bs-target="#assign-functions" type="button" role="tab">
                                <i class="fas fa-tasks me-2"></i>
                                2. Asignar Funciones
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="review-assignments-tab" data-bs-toggle="pill" data-bs-target="#review-assignments" type="button" role="tab">
                                <i class="fas fa-check-circle me-2"></i>
                                3. Revisar y Confirmar
                            </button>
                        </li>
                    </ul>

                    <!-- Contenido de los tabs -->
                    <div class="tab-content" id="bulkTabContent">
                        <!-- Tab 1: Seleccionar Agentes -->
                        <div class="tab-pane fade show active" id="select-agents" role="tabpanel">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="filter-panel">
                                        <h6 class="filter-title">
                                            <i class="fas fa-filter me-2"></i>
                                            Filtros
                                        </h6>

                                        <div class="mb-3">
                                            <label class="form-label">Estado</label>
                                            <select class="form-select" id="filterEstado" onchange="filterAgents()">
                                                <option value="">Todos los estados</option>
                                                <option value="🟢Disponible">🟢 Disponible</option>
                                                <option value="🟡En Colación">🟡 En Colación</option>
                                                <option value="🔴Ocupado">🔴 Ocupado</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Grupo</label>
                                            <select class="form-select" id="filterGrupo" onchange="filterAgents()">
                                                <option value="">Todos los grupos</option>
                                                <?php
                                                $grupos = array_unique(array_filter(array_column($todos_agentes, 'grupo_nombre')));
                                                foreach ($grupos as $grupo): ?>
                                                    <option value="<?php echo htmlspecialchars($grupo); ?>">
                                                        <?php echo htmlspecialchars($grupo); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Búsqueda</label>
                                            <input type="text" class="form-control" id="searchAgent" placeholder="Buscar por nombre o Sabre..." onkeyup="filterAgents()">
                                        </div>

                                        <div class="filter-actions">
                                            <button class="btn btn-outline-primary btn-sm" onclick="selectAllVisible()">
                                                <i class="fas fa-check-square me-1"></i>
                                                Seleccionar Visibles
                                            </button>
                                            <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()">
                                                <i class="fas fa-times me-1"></i>
                                                Limpiar Selección
                                            </button>
                                        </div>

                                        <div class="selection-summary">
                                            <div class="summary-item">
                                                <span class="summary-label">Seleccionados:</span>
                                                <span class="summary-count" id="selectedCount">0</span>
                                            </div>
                                            <div class="summary-item">
                                                <span class="summary-label">Total visible:</span>
                                                <span class="summary-count" id="visibleCount"><?php echo count($todos_agentes); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-8">
                                    <div class="agents-selection-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-users me-2"></i>
                                            Agentes Disponibles
                                        </h6>

                                        <div class="agents-grid" id="agentsGrid">
                                            <?php foreach ($todos_agentes as $agente_bulk): ?>
                                            <div class="agent-selection-card"
                                                 data-estado="<?php echo htmlspecialchars($agente_bulk['estado']); ?>"
                                                 data-grupo="<?php echo htmlspecialchars($agente_bulk['grupo_nombre'] ?? ''); ?>"
                                                 data-nombre="<?php echo htmlspecialchars($agente_bulk['nombre']); ?>"
                                                 data-sabre="<?php echo htmlspecialchars($agente_bulk['usuario_sabre']); ?>"
                                                 data-id="<?php echo $agente_bulk['id']; ?>">

                                                <div class="selection-checkbox">
                                                    <input type="checkbox" class="agent-checkbox" value="<?php echo $agente_bulk['id']; ?>" onchange="updateSelection()">
                                                </div>

                                                <div class="agent-avatar-section">
                                                    <img src="<?php echo getAvatarUrl($agente_bulk['foto_perfil']); ?>" alt="Avatar" class="agent-avatar-bulk">
                                                    <div class="agent-status-bulk <?php
                                                        echo match($agente_bulk['estado']) {
                                                            '🟢Disponible' => 'status-disponible',
                                                            '🟡En Colación' => 'status-colacion',
                                                            '🔴Ocupado' => 'status-ocupado',
                                                            default => 'status-fuera'
                                                        };
                                                    ?>"></div>
                                                </div>

                                                <div class="agent-info-bulk">
                                                    <h6 class="agent-name-bulk"><?php echo htmlspecialchars($agente_bulk['nombre']); ?></h6>
                                                    <p class="agent-sabre-bulk">Sabre: <?php echo htmlspecialchars($agente_bulk['usuario_sabre']); ?></p>
                                                    <p class="agent-grupo-bulk"><?php echo htmlspecialchars($agente_bulk['grupo_nombre'] ?? 'Sin grupo'); ?></p>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 2: Asignar Funciones -->
                        <div class="tab-pane fade" id="assign-functions" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="assignment-config-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-cog me-2"></i>
                                            Configuración de Asignación
                                        </h6>

                                        <form id="bulkAssignmentForm">
                                            <div class="mb-3">
                                                <label class="form-label">Terminal</label>
                                                <select class="form-select" id="bulkTerminal" required>
                                                    <option value="">Seleccionar terminal...</option>
                                                    <?php foreach ($terminales as $terminal): ?>
                                                    <option value="<?php echo $terminal['id']; ?>">
                                                        <?php echo htmlspecialchars($terminal['nombre']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Función</label>
                                                <select class="form-select" id="bulkFuncion" required>
                                                    <option value="">Seleccionar función...</option>
                                                    <?php foreach ($funciones as $funcion): ?>
                                                    <option value="<?php echo $funcion['id']; ?>">
                                                        <?php echo htmlspecialchars($funcion['nombre']); ?>
                                                    </option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Turno</label>
                                                <select class="form-select" id="bulkTurno" required>
                                                    <option value="">Seleccionar turno...</option>
                                                    <option value="mañana">Mañana</option>
                                                    <option value="tarde">Tarde</option>
                                                    <option value="noche">Noche</option>
                                                </select>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Fecha</label>
                                                <input type="date" class="form-control" id="bulkFecha" value="<?php echo date('Y-m-d'); ?>" required>
                                            </div>

                                            <div class="mb-3">
                                                <label class="form-label">Observaciones (Opcional)</label>
                                                <textarea class="form-control" id="bulkObservaciones" rows="3" placeholder="Observaciones para todas las asignaciones..."></textarea>
                                            </div>
                                        </form>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="selected-agents-panel">
                                        <h6 class="panel-title">
                                            <i class="fas fa-user-check me-2"></i>
                                            Agentes Seleccionados (<span id="selectedAgentsCount">0</span>)
                                        </h6>

                                        <div class="selected-agents-list" id="selectedAgentsList">
                                            <div class="empty-selection">
                                                <i class="fas fa-users fa-3x mb-3"></i>
                                                <p>No hay agentes seleccionados</p>
                                                <small>Regresa al paso anterior para seleccionar agentes</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tab 3: Revisar y Confirmar -->
                        <div class="tab-pane fade" id="review-assignments" role="tabpanel">
                            <div class="review-panel">
                                <h6 class="panel-title">
                                    <i class="fas fa-clipboard-check me-2"></i>
                                    Resumen de Asignaciones
                                </h6>

                                <div class="assignment-summary" id="assignmentSummary">
                                    <!-- El contenido se generará dinámicamente -->
                                </div>

                                <div class="confirmation-actions">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        <strong>Importante:</strong> Una vez confirmadas, las asignaciones se crearán inmediatamente.
                                        Asegúrate de revisar toda la información antes de proceder.
                                    </div>

                                    <div class="d-flex gap-3 justify-content-end">
                                        <button type="button" class="btn btn-secondary" onclick="goToPreviousStep()">
                                            <i class="fas fa-arrow-left me-2"></i>
                                            Volver a Editar
                                        </button>
                                        <button type="button" class="btn btn-success btn-lg" onclick="confirmBulkAssignments()">
                                            <i class="fas fa-check me-2"></i>
                                            Confirmar Asignaciones
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <div class="d-flex justify-content-between w-100">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>
                            Cancelar
                        </button>

                        <div class="step-navigation">
                            <button type="button" class="btn btn-outline-primary" id="prevStepBtn" onclick="previousStep()" style="display: none;">
                                <i class="fas fa-arrow-left me-2"></i>
                                Anterior
                            </button>
                            <button type="button" class="btn btn-primary" id="nextStepBtn" onclick="nextStep()">
                                Siguiente
                                <i class="fas fa-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/theme-selector.js"></script>
    <script>
        // Variables globales
        let currentTheme = '<?php echo $agente['tema'] ?? 'light'; ?>';

        // Toggle sidebar
        function toggleSidebar() {
            document.querySelector('.sidebar').classList.toggle('show');
        }

        // Toggle notifications con efectos completos
        function toggleNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            // Toggle panel y overlay
            const isShowing = panel.classList.contains('show');

            if (isShowing) {
                closeNotifications();
            } else {
                panel.classList.add('show');
                overlay.classList.add('show');

                // Animar las notificaciones
                const items = panel.querySelectorAll('.notification-item');
                items.forEach((item, index) => {
                    item.style.animation = `slideInNotification 0.5s ease-out ${index * 0.1}s`;
                });
            }
        }

        function closeNotifications() {
            const panel = document.getElementById('notificationsPanel');
            const overlay = document.getElementById('notificationsOverlay');

            panel.classList.remove('show');
            overlay.classList.remove('show');
        }

        // Open assign modal with pre-selected agent
        function openAssignModal(agenteId, agenteNombre) {
            const modal = new bootstrap.Modal(document.getElementById('assignModal'));
            const select = document.getElementById('agenteSelect');
            select.value = agenteId;
            modal.show();
        }

        // Remove assignment
        function removeAssignment(assignmentId) {
            if (confirm('¿Estás seguro de que deseas remover esta asignación?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="remove">
                    <input type="hidden" name="asignacion_id" value="${assignmentId}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Show bulk actions
        function showBulkActions() {
            const modal = new bootstrap.Modal(document.getElementById('bulkAssignModal'));
            modal.show();

            // Reset modal to first step
            resetBulkModal();
        }

        // Variables globales para el modal masivo
        let selectedAgents = [];
        let currentStep = 1;

        // Reset bulk modal
        function resetBulkModal() {
            currentStep = 1;
            selectedAgents = [];

            // Reset tabs
            document.querySelectorAll('#bulkTabs .nav-link').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#bulkTabContent .tab-pane').forEach(pane => pane.classList.remove('show', 'active'));

            document.getElementById('select-agents-tab').classList.add('active');
            document.getElementById('select-agents').classList.add('show', 'active');

            // Reset form
            document.getElementById('bulkAssignmentForm').reset();
            document.getElementById('bulkFecha').value = '<?php echo date('Y-m-d'); ?>';

            // Clear selections
            clearSelection();

            // Update navigation
            updateStepNavigation();
        }

        // Filter agents
        function filterAgents() {
            const estado = document.getElementById('filterEstado').value;
            const grupo = document.getElementById('filterGrupo').value;
            const search = document.getElementById('searchAgent').value.toLowerCase();

            const cards = document.querySelectorAll('.agent-selection-card');
            let visibleCount = 0;

            cards.forEach(card => {
                const cardEstado = card.dataset.estado;
                const cardGrupo = card.dataset.grupo;
                const cardNombre = card.dataset.nombre.toLowerCase();
                const cardSabre = card.dataset.sabre.toLowerCase();

                let show = true;

                if (estado && cardEstado !== estado) show = false;
                if (grupo && cardGrupo !== grupo) show = false;
                if (search && !cardNombre.includes(search) && !cardSabre.includes(search)) show = false;

                if (show) {
                    card.style.display = 'flex';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });

            document.getElementById('visibleCount').textContent = visibleCount;
        }

        // Select all visible agents
        function selectAllVisible() {
            const visibleCards = document.querySelectorAll('.agent-selection-card[style*="flex"], .agent-selection-card:not([style])');

            visibleCards.forEach(card => {
                const checkbox = card.querySelector('.agent-checkbox');
                if (!checkbox.checked) {
                    checkbox.checked = true;
                    card.classList.add('selected');
                }
            });

            updateSelection();
        }

        // Clear selection
        function clearSelection() {
            selectedAgents = [];

            document.querySelectorAll('.agent-checkbox').forEach(checkbox => {
                checkbox.checked = false;
            });

            document.querySelectorAll('.agent-selection-card').forEach(card => {
                card.classList.remove('selected');
            });

            updateSelection();
        }

        // Update selection
        function updateSelection() {
            selectedAgents = [];

            document.querySelectorAll('.agent-checkbox:checked').forEach(checkbox => {
                const card = checkbox.closest('.agent-selection-card');
                card.classList.add('selected');

                selectedAgents.push({
                    id: checkbox.value,
                    nombre: card.dataset.nombre,
                    sabre: card.dataset.sabre,
                    grupo: card.dataset.grupo,
                    avatar: card.querySelector('.agent-avatar-bulk').src
                });
            });

            document.querySelectorAll('.agent-checkbox:not(:checked)').forEach(checkbox => {
                const card = checkbox.closest('.agent-selection-card');
                card.classList.remove('selected');
            });

            document.getElementById('selectedCount').textContent = selectedAgents.length;
            document.getElementById('selectedAgentsCount').textContent = selectedAgents.length;

            updateSelectedAgentsList();
            updateStepNavigation();
        }

        // Update selected agents list
        function updateSelectedAgentsList() {
            const container = document.getElementById('selectedAgentsList');

            if (selectedAgents.length === 0) {
                container.innerHTML = `
                    <div class="empty-selection">
                        <i class="fas fa-users fa-3x mb-3"></i>
                        <p>No hay agentes seleccionados</p>
                        <small>Regresa al paso anterior para seleccionar agentes</small>
                    </div>
                `;
            } else {
                container.innerHTML = selectedAgents.map(agent => `
                    <div class="selected-agent-item">
                        <img src="${agent.avatar}" alt="Avatar" class="selected-agent-avatar">
                        <div class="selected-agent-info">
                            <h6>${agent.nombre}</h6>
                            <small>Sabre: ${agent.sabre} • ${agent.grupo || 'Sin grupo'}</small>
                        </div>
                    </div>
                `).join('');
            }
        }

        // Step navigation
        function nextStep() {
            if (currentStep === 1) {
                if (selectedAgents.length === 0) {
                    alert('Debes seleccionar al menos un agente para continuar.');
                    return;
                }
                goToStep(2);
            } else if (currentStep === 2) {
                if (!validateAssignmentForm()) {
                    return;
                }
                generateAssignmentSummary();
                goToStep(3);
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                goToStep(currentStep - 1);
            }
        }

        function goToStep(step) {
            currentStep = step;

            // Update tabs
            document.querySelectorAll('#bulkTabs .nav-link').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('#bulkTabContent .tab-pane').forEach(pane => pane.classList.remove('show', 'active'));

            if (step === 1) {
                document.getElementById('select-agents-tab').classList.add('active');
                document.getElementById('select-agents').classList.add('show', 'active');
            } else if (step === 2) {
                document.getElementById('assign-functions-tab').classList.add('active');
                document.getElementById('assign-functions').classList.add('show', 'active');
                updateSelectedAgentsList();
            } else if (step === 3) {
                document.getElementById('review-assignments-tab').classList.add('active');
                document.getElementById('review-assignments').classList.add('show', 'active');
            }

            updateStepNavigation();
        }

        function updateStepNavigation() {
            const prevBtn = document.getElementById('prevStepBtn');
            const nextBtn = document.getElementById('nextStepBtn');

            if (currentStep === 1) {
                prevBtn.style.display = 'none';
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = 'Siguiente <i class="fas fa-arrow-right ms-2"></i>';
                nextBtn.disabled = selectedAgents.length === 0;
            } else if (currentStep === 2) {
                prevBtn.style.display = 'block';
                nextBtn.style.display = 'block';
                nextBtn.innerHTML = 'Revisar <i class="fas fa-arrow-right ms-2"></i>';
                nextBtn.disabled = false;
            } else if (currentStep === 3) {
                prevBtn.style.display = 'block';
                nextBtn.style.display = 'none';
            }
        }

        // Validate assignment form
        function validateAssignmentForm() {
            const terminal = document.getElementById('bulkTerminal').value;
            const funcion = document.getElementById('bulkFuncion').value;
            const turno = document.getElementById('bulkTurno').value;
            const fecha = document.getElementById('bulkFecha').value;

            if (!terminal || !funcion || !turno || !fecha) {
                alert('Por favor completa todos los campos obligatorios.');
                return false;
            }

            return true;
        }

        // Generate assignment summary
        function generateAssignmentSummary() {
            const terminal = document.getElementById('bulkTerminal');
            const funcion = document.getElementById('bulkFuncion');
            const turno = document.getElementById('bulkTurno');
            const fecha = document.getElementById('bulkFecha').value;
            const observaciones = document.getElementById('bulkObservaciones').value;

            const summary = document.getElementById('assignmentSummary');

            summary.innerHTML = `
                <div class="summary-section">
                    <h6><i class="fas fa-info-circle me-2"></i>Detalles de la Asignación</h6>
                    <div class="summary-grid">
                        <div class="summary-card">
                            <div class="label">Terminal</div>
                            <div class="value">${terminal.options[terminal.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Función</div>
                            <div class="value">${funcion.options[funcion.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Turno</div>
                            <div class="value">${turno.options[turno.selectedIndex].text}</div>
                        </div>
                        <div class="summary-card">
                            <div class="label">Fecha</div>
                            <div class="value">${new Date(fecha).toLocaleDateString('es-ES')}</div>
                        </div>
                    </div>
                    ${observaciones ? `
                        <div class="summary-card">
                            <div class="label">Observaciones</div>
                            <div class="value">${observaciones}</div>
                        </div>
                    ` : ''}
                </div>

                <div class="summary-section">
                    <h6><i class="fas fa-users me-2"></i>Agentes Seleccionados (${selectedAgents.length})</h6>
                    <div class="selected-agents-list">
                        ${selectedAgents.map(agent => `
                            <div class="selected-agent-item">
                                <img src="${agent.avatar}" alt="Avatar" class="selected-agent-avatar">
                                <div class="selected-agent-info">
                                    <h6>${agent.nombre}</h6>
                                    <small>Sabre: ${agent.sabre} • ${agent.grupo || 'Sin grupo'}</small>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // Go to previous step from review
        function goToPreviousStep() {
            goToStep(2);
        }

        // Confirm bulk assignments
        function confirmBulkAssignments() {
            if (confirm(`¿Estás seguro de crear ${selectedAgents.length} asignaciones?`)) {
                // Crear formulario para envío
                const form = document.createElement('form');
                form.method = 'POST';
                form.style.display = 'none';

                // Datos de la asignación
                const terminal = document.getElementById('bulkTerminal').value;
                const funcion = document.getElementById('bulkFuncion').value;
                const turno = document.getElementById('bulkTurno').value;
                const fecha = document.getElementById('bulkFecha').value;
                const observaciones = document.getElementById('bulkObservaciones').value;

                // Agregar campos
                form.innerHTML = `
                    <input type="hidden" name="action" value="bulk_assign">
                    <input type="hidden" name="terminal_id" value="${terminal}">
                    <input type="hidden" name="funcion_id" value="${funcion}">
                    <input type="hidden" name="turno" value="${turno}">
                    <input type="hidden" name="fecha" value="${fecha}">
                    <input type="hidden" name="observaciones" value="${observaciones}">
                    <input type="hidden" name="agent_ids" value="${selectedAgents.map(a => a.id).join(',')}">
                `;

                document.body.appendChild(form);
                form.submit();
            }
        }

        // Mark all notifications as read
        function markAllAsRead() {
            // Implementar llamada AJAX para marcar todas como leídas
            console.log('Marcando todas las notificaciones como leídas...');
        }

        // Refresh notifications
        function refreshNotifications() {
            // Implementar recarga de notificaciones
            location.reload();
        }

        // Animaciones CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInNotification {
                from {
                    opacity: 0;
                    transform: translateX(20px);
                }
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Load saved theme and initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Sincronizar con localStorage
            localStorage.setItem('theme', currentTheme);

            // Aplicar tema inicial
            if (currentTheme === 'dark') {
                document.body.classList.add('theme-dark');
            }

            // Close notifications when clicking outside
            document.addEventListener('click', function(e) {
                const panel = document.getElementById('notificationsPanel');
                const bell = document.querySelector('.notification-bell');
                const overlay = document.getElementById('notificationsOverlay');

                if (!panel.contains(e.target) && !bell.contains(e.target)) {
                    closeNotifications();
                }
            });

            // Agregar efectos de hover a las tarjetas
            const cards = document.querySelectorAll('.stat-card, .agent-card, .management-panel');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    if (!this.classList.contains('management-panel')) {
                        this.style.transform = 'translateY(-5px) scale(1.02)';
                    }
                });

                card.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('management-panel')) {
                        this.style.transform = 'translateY(0) scale(1)';
                    }
                });
            });

            console.log('✅ Asignaciones panel inicializado correctamente');
        });
    </script>
</body>
</html>
