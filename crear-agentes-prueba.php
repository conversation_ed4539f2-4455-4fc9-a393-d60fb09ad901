<?php
// Script para crear agentes de prueba
session_start();

// Simular usuario logueado con permisos de administrador
$_SESSION['agente_id'] = 1;
$_SESSION['agente_nombre'] = 'Admin Test';

require_once 'config/functions.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Creando agentes de prueba...</h2>";
    
    // Primero verificar si ya existen agentes
    $check_query = "SELECT COUNT(*) as total FROM agentes WHERE activo = 1";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->execute();
    $total_existentes = $check_stmt->fetch()['total'];
    
    echo "<p>Agentes existentes: $total_existentes</p>";
    
    // Obtener grupos disponibles
    $grupos_query = "SELECT * FROM grupos ORDER BY nombre";
    $grupos_stmt = $conn->prepare($grupos_query);
    $grupos_stmt->execute();
    $grupos = $grupos_stmt->fetchAll();
    
    if (empty($grupos)) {
        echo "<p>❌ No hay grupos disponibles. Creando grupos básicos...</p>";
        
        // Crear grupos básicos
        $grupos_basicos = [
            ['Administradores', 'Administradores del sistema', '["all_permissions"]'],
            ['Agentes', 'Agentes operacionales básicos', '["view_dashboard", "view_flights", "manage_profile"]'],
            ['Lobby', 'Personal de lobby y atención al cliente', '["view_dashboard", "view_flights", "manage_profile", "view_assignments"]'],
            ['CREC', 'Personal de CREC', '["view_dashboard", "view_flights", "manage_profile", "view_assignments", "manage_flights"]']
        ];
        
        $insert_grupo_query = "INSERT INTO grupos (nombre, descripcion, permisos) VALUES (?, ?, ?)";
        $insert_grupo_stmt = $conn->prepare($insert_grupo_query);
        
        foreach ($grupos_basicos as $grupo) {
            try {
                $insert_grupo_stmt->execute($grupo);
                echo "<p>✅ Grupo creado: " . $grupo[0] . "</p>";
            } catch (Exception $e) {
                echo "<p>⚠️ Grupo ya existe o error: " . $grupo[0] . " - " . $e->getMessage() . "</p>";
            }
        }
        
        // Recargar grupos
        $grupos_stmt->execute();
        $grupos = $grupos_stmt->fetchAll();
    }
    
    echo "<h3>Grupos disponibles:</h3>";
    foreach ($grupos as $grupo) {
        echo "<p>ID: {$grupo['id']} - {$grupo['nombre']}</p>";
    }
    
    // Crear agentes de prueba si no existen muchos
    if ($total_existentes < 5) {
        $agentes_prueba = [
            [
                'nombre' => 'Juan Pérez',
                'usuario_sabre' => 'JPEREZ',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'grupo_id' => $grupos[0]['id'], // Primer grupo disponible
                'telefono' => '+56912345678'
            ],
            [
                'nombre' => 'María González',
                'usuario_sabre' => 'MGONZALEZ',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'grupo_id' => $grupos[1]['id'] ?? $grupos[0]['id'],
                'telefono' => '+56987654321'
            ],
            [
                'nombre' => 'Carlos Rodríguez',
                'usuario_sabre' => 'CRODRIGUEZ',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'grupo_id' => $grupos[2]['id'] ?? $grupos[0]['id'],
                'telefono' => '+56911223344'
            ],
            [
                'nombre' => 'Ana Silva',
                'usuario_sabre' => 'ASILVA',
                'email' => '<EMAIL>',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'grupo_id' => $grupos[3]['id'] ?? $grupos[0]['id'],
                'telefono' => '+56955667788'
            ]
        ];
        
        $insert_query = "INSERT INTO agentes (nombre, usuario_sabre, email, password, grupo_id, telefono) VALUES (?, ?, ?, ?, ?, ?)";
        $insert_stmt = $conn->prepare($insert_query);
        
        foreach ($agentes_prueba as $agente) {
            try {
                $insert_stmt->execute([
                    $agente['nombre'],
                    $agente['usuario_sabre'],
                    $agente['email'],
                    $agente['password'],
                    $agente['grupo_id'],
                    $agente['telefono']
                ]);
                echo "<p>✅ Agente creado: " . $agente['nombre'] . " (" . $agente['usuario_sabre'] . ")</p>";
            } catch (Exception $e) {
                echo "<p>⚠️ Error al crear agente " . $agente['nombre'] . ": " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>✅ Ya hay suficientes agentes en la base de datos.</p>";
    }
    
    // Verificar resultado final
    $check_stmt->execute();
    $total_final = $check_stmt->fetch()['total'];
    
    echo "<h3>Resultado final:</h3>";
    echo "<p><strong>Total de agentes activos:</strong> $total_final</p>";
    
    echo "<hr>";
    echo "<h3>Próximos pasos:</h3>";
    echo "<p><a href='admin/agentes.php' target='_blank'>🔗 Ir a Administración de Agentes</a></p>";
    echo "<p><a href='test-admin-agentes.php' target='_blank'>🔗 Ver datos de prueba</a></p>";
    
} catch (Exception $e) {
    echo "<h2>❌ Error:</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
