<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Verificar autenticación y permisos
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

if (!hasPermission('manage_admin')) {
    header('Location: ../index.php');
    exit;
}

$agente = getCurrentAgent();
$database = new Database();
$conn = $database->getConnection();

// Obtener documentos firmados
$query = "SELECT 
    ds.informativo_id,
    i.titulo as documento_titulo,
    i.tipo as documento_tipo,
    i.fecha_publicacion,
    ds.agente_id,
    a.nombre as agente_nombre,
    a.apellido as agente_apellido,
    a.sabre_id as agente_sabre_id,
    a.email as agente_email,
    ds.created_at as fecha_firma,
    ds.ip_address
FROM document_signatures ds
JOIN informativos i ON ds.informativo_id = i.id
JOIN agentes a ON ds.agente_id = a.id
WHERE ds.action_type = 'signed'
ORDER BY ds.created_at DESC";

$stmt = $conn->prepare($query);
$stmt->execute();
$documentos_firmados = $stmt->fetchAll();

// Agrupar por documento
$documentos_agrupados = [];
foreach ($documentos_firmados as $firma) {
    $doc_id = $firma['informativo_id'];
    if (!isset($documentos_agrupados[$doc_id])) {
        $documentos_agrupados[$doc_id] = [
            'documento' => [
                'id' => $doc_id,
                'titulo' => $firma['documento_titulo'],
                'tipo' => $firma['documento_tipo'],
                'fecha_publicacion' => $firma['fecha_publicacion']
            ],
            'firmas' => []
        ];
    }
    $documentos_agrupados[$doc_id]['firmas'][] = $firma;
}
?>

<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documentos Firmados - SwissportAgents</title>
    <link href="../css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        
        .admin-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .admin-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        .document-card {
            background: white;
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease;
            border-left: 4px solid #10b981;
        }
        
        .document-card:hover {
            transform: translateY(-2px);
        }
        
        .document-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }
        
        .document-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin: 0;
        }
        
        .document-meta {
            display: flex;
            gap: 20px;
            font-size: 14px;
            color: #64748b;
            margin-top: 8px;
        }
        
        .tipo-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .tipo-briefing { background: #dbeafe; color: #1d4ed8; }
        .tipo-boletin { background: #dcfce7; color: #166534; }
        .tipo-circular { background: #fef3c7; color: #92400e; }
        .tipo-otro { background: #f3e8ff; color: #7c3aed; }
        
        .signers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            gap: 15px;
        }
        
        .signer-card {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
            border: 2px solid #10b981;
            border-radius: 12px;
            padding: 18px;
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .signer-card::before {
            content: '';
            position: absolute;
            top: -2px;
            right: -2px;
            width: 20px;
            height: 20px;
            background: #10b981;
            border-radius: 0 10px 0 10px;
        }
        
        .signer-card::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 4px;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .signer-card:hover {
            transform: scale(1.02);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
        }
        
        .signer-avatar {
            width: 55px;
            height: 55px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
            font-weight: 700;
            font-size: 20px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        
        .signer-name {
            font-weight: 700;
            color: #065f46;
            margin-bottom: 6px;
            font-size: 14px;
        }
        
        .signer-info {
            font-size: 12px;
            color: #047857;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .signer-date {
            font-size: 11px;
            color: #059669;
            font-weight: 500;
        }
        
        .signature-badge {
            background: #10b981;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            margin-top: 8px;
            display: inline-block;
        }
        
        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: #10b981;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #64748b;
            font-weight: 600;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: transform 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .signature-icon {
            color: #10b981;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2">
                        <i class="fas fa-signature me-3 signature-icon"></i>
                        Documentos Firmados Digitalmente
                    </h1>
                    <p class="text-muted mb-0">Registro de agentes que han firmado digitalmente los documentos informativos</p>
                </div>
                <a href="../admin.php" class="back-btn">
                    <i class="fas fa-arrow-left"></i>
                    Volver al Panel
                </a>
            </div>
        </div>

        <!-- Estadísticas -->
        <div class="stats-row">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($documentos_agrupados); ?></div>
                <div class="stat-label">Documentos con Firmas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($documentos_firmados); ?></div>
                <div class="stat-label">Total de Firmas</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">
                    <?php 
                    $agentes_unicos = array_unique(array_column($documentos_firmados, 'agente_id'));
                    echo count($agentes_unicos);
                    ?>
                </div>
                <div class="stat-label">Agentes que Firmaron</div>
            </div>
        </div>

        <!-- Lista de documentos -->
        <?php if (empty($documentos_agrupados)): ?>
            <div class="document-card text-center">
                <i class="fas fa-signature fa-3x text-muted mb-3"></i>
                <h5>No hay firmas digitales registradas</h5>
                <p class="text-muted">Aún no hay agentes que hayan firmado documentos informativos digitalmente.</p>
            </div>
        <?php else: ?>
            <?php foreach ($documentos_agrupados as $doc_data): ?>
                <div class="document-card">
                    <div class="document-header">
                        <div class="flex-grow-1">
                            <h3 class="document-title">
                                <i class="fas fa-certificate signature-icon"></i>
                                <?php echo htmlspecialchars($doc_data['documento']['titulo']); ?>
                            </h3>
                            <div class="document-meta">
                                <span>
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo date('d/m/Y', strtotime($doc_data['documento']['fecha_publicacion'])); ?>
                                </span>
                                <span>
                                    <i class="fas fa-signature me-1"></i>
                                    <?php echo count($doc_data['firmas']); ?> firmas digitales
                                </span>
                            </div>
                        </div>
                        <span class="tipo-badge tipo-<?php echo $doc_data['documento']['tipo']; ?>">
                            <?php echo ucfirst($doc_data['documento']['tipo']); ?>
                        </span>
                    </div>

                    <div class="signers-grid">
                        <?php foreach ($doc_data['firmas'] as $firma): ?>
                            <div class="signer-card">
                                <div class="signer-avatar">
                                    <?php echo strtoupper(substr($firma['agente_nombre'], 0, 1) . substr($firma['agente_apellido'], 0, 1)); ?>
                                </div>
                                <div class="signer-name">
                                    <?php echo htmlspecialchars($firma['agente_nombre'] . ' ' . $firma['agente_apellido']); ?>
                                </div>
                                <div class="signer-info">
                                    ID: <?php echo htmlspecialchars($firma['agente_sabre_id']); ?>
                                </div>
                                <div class="signature-badge">
                                    FIRMADO DIGITALMENTE
                                </div>
                                <div class="signer-date">
                                    <?php echo date('d/m/Y H:i', strtotime($firma['fecha_firma'])); ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <script src="../js/bootstrap.bundle.min.js"></script>
</body>
</html>
