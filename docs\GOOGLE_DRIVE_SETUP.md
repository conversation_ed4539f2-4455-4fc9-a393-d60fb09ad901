# Configuración de Google Drive para Informativos

Esta guía te ayudará a configurar la integración con Google Drive para permitir que los usuarios seleccionen documentos directamente desde su Google Drive sin subirlos al servidor.

## 📋 Requisitos Previos

1. Una cuenta de Google Cloud Console
2. Acceso de administrador al proyecto
3. Dominio web configurado (para producción)

## 🚀 Pasos de Configuración

### 1. Crear Proyecto en Google Cloud Console

1. Ve a [Google Cloud Console](https://console.cloud.google.com/)
2. Crea un nuevo proyecto o selecciona uno existente
3. Anota el ID del proyecto

### 2. Habilitar APIs Necesarias

1. En el menú lateral, ve a **APIs y servicios > Biblioteca**
2. Busca y habilita las siguientes APIs:
   - **Google Drive API**
   - **Google Picker API** (opcional, para mejor UX)

### 3. Crear Credenciales

#### API Key
1. Ve a **APIs y servicios > Credenciales**
2. Haz clic en **+ CREAR CREDENCIALES > Clave de API**
3. Copia la API Key generada
4. (Opcional) Restringe la clave a las APIs específicas

#### OAuth 2.0 Client ID
1. En **Credenciales**, haz clic en **+ CREAR CREDENCIALES > ID de cliente de OAuth 2.0**
2. Selecciona **Aplicación web**
3. Configura:
   - **Nombre**: SwissportAgents Google Drive
   - **Orígenes autorizados de JavaScript**:
     - `http://localhost` (para desarrollo)
     - `https://tu-dominio.com` (para producción)
   - **URIs de redirección autorizados**:
     - `http://localhost/v5/informativos.php` (para desarrollo)
     - `https://tu-dominio.com/informativos.php` (para producción)
4. Copia el Client ID generado

### 4. Configurar Pantalla de Consentimiento OAuth

1. Ve a **APIs y servicios > Pantalla de consentimiento de OAuth**
2. Selecciona **Externo** (para usuarios de cualquier cuenta de Google)
3. Completa la información requerida:
   - **Nombre de la aplicación**: SwissportAgents
   - **Correo electrónico de asistencia**: <EMAIL>
   - **Logotipo de la aplicación**: (opcional)
   - **Dominios autorizados**: tu-dominio.com
4. Agrega los scopes necesarios:
   - `https://www.googleapis.com/auth/drive.readonly`

### 5. Actualizar Configuración en el Código

1. Edita el archivo `config/google-drive-config.js`
2. Reemplaza los valores de configuración:

```javascript
const GOOGLE_DRIVE_CONFIG = {
    CLIENT_ID: 'TU_CLIENT_ID_REAL.apps.googleusercontent.com',
    API_KEY: 'TU_API_KEY_REAL',
    // ... resto de la configuración
};
```

### 6. Ejecutar Script de Base de Datos

1. Ejecuta el script SQL para agregar soporte a Google Drive:
```sql
-- Ejecutar en tu base de datos MySQL
source sql/add_google_drive_support.sql;
```

## 🔧 Configuración de Desarrollo vs Producción

### Desarrollo (localhost)
```javascript
// En google-drive-config.js
const GOOGLE_DRIVE_CONFIG = {
    CLIENT_ID: 'tu-client-id.apps.googleusercontent.com',
    API_KEY: 'tu-api-key',
    // ... resto
};
```

### Producción
1. Asegúrate de que tu dominio esté en la lista de orígenes autorizados
2. Usa HTTPS en producción
3. Considera restringir la API Key a tu dominio específico

## 🛡️ Seguridad y Permisos

### Scopes Utilizados
- `https://www.googleapis.com/auth/drive.readonly`: Solo lectura de archivos de Google Drive

### Datos Almacenados
- **ID del archivo**: Para referenciar el documento
- **Nombre del archivo**: Para mostrar en la interfaz
- **URL de visualización**: Para acceso directo al documento
- **NO se almacena**: Contenido del archivo, tokens de acceso permanentes

### Privacidad
- Los usuarios deben autorizar explícitamente el acceso
- Solo se accede a archivos que el usuario selecciona
- No se almacenan credenciales permanentes

## 🧪 Pruebas

### Verificar Configuración
1. Abre `informativos.php`
2. Haz clic en "Subir Documento"
3. Selecciona la pestaña "Google Drive"
4. Haz clic en "Conectar con Google Drive"
5. Autoriza la aplicación
6. Selecciona un archivo de tu Drive

### Solución de Problemas Comunes

#### Error: "Invalid client ID"
- Verifica que el Client ID sea correcto
- Asegúrate de que el dominio esté autorizado

#### Error: "Access blocked"
- Verifica la configuración de la pantalla de consentimiento
- Asegúrate de que los scopes sean correctos

#### Error: "API key not valid"
- Verifica que la API Key sea correcta
- Asegúrate de que las APIs estén habilitadas

## 📝 Notas Importantes

1. **Límites de Cuota**: Google Drive API tiene límites de uso. Para aplicaciones con muchos usuarios, considera solicitar cuotas adicionales.

2. **Verificación de la App**: Para uso en producción con muchos usuarios, es recomendable verificar la aplicación con Google.

3. **Backup**: Los archivos permanecen en Google Drive del usuario. Considera tener una estrategia de backup si el usuario elimina archivos.

4. **Acceso Offline**: Esta implementación requiere que el usuario tenga acceso a internet para ver los documentos.

## 🔄 Mantenimiento

- Revisa periódicamente las cuotas de uso en Google Cloud Console
- Mantén actualizadas las librerías de Google APIs
- Monitorea los logs de errores para problemas de autenticación

## 📞 Soporte

Si tienes problemas con la configuración:
1. Revisa los logs del navegador (F12 > Console)
2. Verifica la configuración en Google Cloud Console
3. Consulta la [documentación oficial de Google Drive API](https://developers.google.com/drive/api/v3/about-sdk)
