<?php
require_once 'config/config.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>Debug de Informativos</h2>";
    
    // Verificar si la tabla existe
    $tables_query = "SHOW TABLES LIKE 'informativos'";
    $tables_stmt = $conn->prepare($tables_query);
    $tables_stmt->execute();
    $table_exists = $tables_stmt->fetch();
    
    if ($table_exists) {
        echo "<p>✅ Tabla 'informativos' existe</p>";
        
        // Contar registros
        $count_query = "SELECT COUNT(*) as total FROM informativos";
        $count_stmt = $conn->prepare($count_query);
        $count_stmt->execute();
        $count = $count_stmt->fetch()['total'];
        
        echo "<p>📊 Total de registros en informativos: $count</p>";
        
        if ($count > 0) {
            // Mostrar algunos registros
            $query = "SELECT * FROM informativos ORDER BY fecha_publicacion DESC LIMIT 5";
            $stmt = $conn->prepare($query);
            $stmt->execute();
            $informativos = $stmt->fetchAll();
            
            echo "<h3>Últimos 5 informativos:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Título</th><th>Tipo</th><th>Archivo</th><th>Fecha</th><th>Subido por</th></tr>";
            
            foreach ($informativos as $info) {
                echo "<tr>";
                echo "<td>" . $info['id'] . "</td>";
                echo "<td>" . htmlspecialchars($info['titulo']) . "</td>";
                echo "<td>" . $info['tipo'] . "</td>";
                echo "<td>" . $info['archivo'] . "</td>";
                echo "<td>" . $info['fecha_publicacion'] . "</td>";
                echo "<td>" . $info['subido_por'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Verificar agentes
            echo "<h3>Verificar agentes:</h3>";
            $agentes_query = "SELECT id, nombre FROM agentes LIMIT 5";
            $agentes_stmt = $conn->prepare($agentes_query);
            $agentes_stmt->execute();
            $agentes = $agentes_stmt->fetchAll();
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Nombre</th></tr>";
            foreach ($agentes as $agente) {
                echo "<tr><td>" . $agente['id'] . "</td><td>" . htmlspecialchars($agente['nombre']) . "</td></tr>";
            }
            echo "</table>";
            
            // Probar la consulta con JOIN
            echo "<h3>Consulta con JOIN:</h3>";
            $join_query = "SELECT i.*, a.nombre as subido_por_nombre 
                          FROM informativos i 
                          LEFT JOIN agentes a ON i.subido_por = a.id 
                          ORDER BY i.fecha_publicacion DESC LIMIT 3";
            $join_stmt = $conn->prepare($join_query);
            $join_stmt->execute();
            $join_result = $join_stmt->fetchAll();
            
            echo "<pre>";
            print_r($join_result);
            echo "</pre>";
            
        } else {
            echo "<p>❌ No hay registros en la tabla informativos</p>";
            echo "<p><a href='setup-test-data.php'>Ejecutar script de datos de prueba</a></p>";
        }
        
    } else {
        echo "<p>❌ La tabla 'informativos' no existe</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
