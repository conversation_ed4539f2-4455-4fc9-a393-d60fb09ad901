<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Botones Informativos</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 40px 20px;
            font-family: 'Inter', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        
        .test-card {
            background: #f8fafc;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px solid #e2e8f0;
        }
        
        .btn-test {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            margin: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-test:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        
        .log-area {
            background: #1a1a1a;
            color: #00ff00;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .alert-custom {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-bug me-2"></i>
            Test de Botones Informativos
        </h1>
        
        <div class="test-card">
            <h3>Test de Funciones JavaScript</h3>
            <p>Haz clic en los botones para probar las funciones:</p>
            
            <button class="btn-test" onclick="testMarkAsRead()">
                <i class="fas fa-eye me-2"></i>
                Test Marcar como Leído
            </button>
            
            <button class="btn-test" onclick="testSignDocument()">
                <i class="fas fa-signature me-2"></i>
                Test Firmar Documento
            </button>
            
            <button class="btn-test" onclick="testDownloadDocument()">
                <i class="fas fa-download me-2"></i>
                Test Descargar
            </button>
            
            <button class="btn-test" onclick="testDeleteInformativo()">
                <i class="fas fa-trash me-2"></i>
                Test Eliminar
            </button>
            
            <button class="btn-test" onclick="clearLog()">
                <i class="fas fa-broom me-2"></i>
                Limpiar Log
            </button>
        </div>
        
        <div class="test-card">
            <h3>Test de API Endpoints</h3>
            <p>Prueba directa de los endpoints de la API:</p>
            
            <button class="btn-test" onclick="testApiMarkRead()">
                <i class="fas fa-server me-2"></i>
                Test API Mark Read
            </button>
            
            <button class="btn-test" onclick="testApiSignDocument()">
                <i class="fas fa-server me-2"></i>
                Test API Sign Document
            </button>
            
            <button class="btn-test" onclick="testApiDelete()">
                <i class="fas fa-server me-2"></i>
                Test API Delete
            </button>
        </div>
        
        <div class="test-card">
            <h3>Console Log</h3>
            <div id="logArea" class="log-area">
                === Test de Botones Informativos ===<br>
                Esperando interacción del usuario...<br>
            </div>
        </div>
    </div>

    <script>
        // Función para agregar logs
        function addLog(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ff4444' : type === 'success' ? '#44ff44' : '#00ff00';
            
            logArea.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '=== Log Limpiado ===<br>';
        }
        
        // Test de funciones locales
        function testMarkAsRead() {
            addLog('🔍 Probando markAsRead(123)...');
            try {
                markAsRead(123);
                addLog('✅ Función markAsRead ejecutada', 'success');
            } catch (error) {
                addLog('❌ Error en markAsRead: ' + error.message, 'error');
            }
        }
        
        function testSignDocument() {
            addLog('🔍 Probando signDocument(123)...');
            try {
                signDocument(123);
                addLog('✅ Función signDocument ejecutada', 'success');
            } catch (error) {
                addLog('❌ Error en signDocument: ' + error.message, 'error');
            }
        }
        
        function testDownloadDocument() {
            addLog('🔍 Probando downloadDocument("test.pdf")...');
            try {
                downloadDocument('test.pdf');
                addLog('✅ Función downloadDocument ejecutada', 'success');
            } catch (error) {
                addLog('❌ Error en downloadDocument: ' + error.message, 'error');
            }
        }
        
        function testDeleteInformativo() {
            addLog('🔍 Probando deleteInformativo(123)...');
            try {
                deleteInformativo(123);
                addLog('✅ Función deleteInformativo ejecutada', 'success');
            } catch (error) {
                addLog('❌ Error en deleteInformativo: ' + error.message, 'error');
            }
        }
        
        // Test de API endpoints
        function testApiMarkRead() {
            addLog('🌐 Probando API mark_read.php...');
            
            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: 999,
                    action: 'read'
                })
            })
            .then(response => {
                addLog(`📡 Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`📦 Response data: ${JSON.stringify(data)}`);
                if (data.success) {
                    addLog('✅ API mark_read funcionando', 'success');
                } else {
                    addLog('⚠️ API mark_read respondió con error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('❌ Error de conexión API mark_read: ' + error.message, 'error');
            });
        }
        
        function testApiSignDocument() {
            addLog('🌐 Probando API sign document...');
            
            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: 999,
                    action: 'sign'
                })
            })
            .then(response => {
                addLog(`📡 Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`📦 Response data: ${JSON.stringify(data)}`);
                if (data.success) {
                    addLog('✅ API sign funcionando', 'success');
                } else {
                    addLog('⚠️ API sign respondió con error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('❌ Error de conexión API sign: ' + error.message, 'error');
            });
        }
        
        function testApiDelete() {
            addLog('🌐 Probando API delete_informativo.php...');
            
            fetch('api/delete_informativo.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: 999 })
            })
            .then(response => {
                addLog(`📡 Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`📦 Response data: ${JSON.stringify(data)}`);
                if (data.success) {
                    addLog('✅ API delete funcionando', 'success');
                } else {
                    addLog('⚠️ API delete respondió con error: ' + data.message, 'error');
                }
            })
            .catch(error => {
                addLog('❌ Error de conexión API delete: ' + error.message, 'error');
            });
        }
        
        // Funciones copiadas del archivo original para test
        function markAsRead(informativoId) {
            addLog(`🔍 markAsRead llamada con ID: ${informativoId}`);
            
            fetch('api/mark_read.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    informativo_id: informativoId,
                    action: 'read'
                })
            })
            .then(response => {
                addLog(`📡 Response status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                addLog(`📦 Response data: ${JSON.stringify(data)}`);
                if (data.success) {
                    showNotification('Documento marcado como leído', 'success');
                } else {
                    showNotification(data.message || 'Error al marcar como leído', 'error');
                }
            })
            .catch(error => {
                addLog('❌ Error: ' + error.message, 'error');
                showNotification('Error de conexión', 'error');
            });
        }
        
        function signDocument(informativoId) {
            addLog(`🔍 signDocument llamada con ID: ${informativoId}`);
            
            if (confirm('¿Estás seguro de que quieres firmar digitalmente este documento?')) {
                fetch('api/mark_read.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        informativo_id: informativoId,
                        action: 'sign'
                    })
                })
                .then(response => {
                    addLog(`📡 Sign response status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addLog(`📦 Sign response data: ${JSON.stringify(data)}`);
                    if (data.success) {
                        showNotification('Documento firmado digitalmente', 'success');
                    } else {
                        showNotification('Error: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    addLog('❌ Error: ' + error.message, 'error');
                    showNotification('Error de conexión', 'error');
                });
            }
        }
        
        function deleteInformativo(id) {
            addLog(`🔍 deleteInformativo llamada con ID: ${id}`);
            
            if (confirm('¿Estás seguro de que quieres eliminar este documento?')) {
                fetch('api/delete_informativo.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: id })
                })
                .then(response => {
                    addLog(`📡 Delete response status: ${response.status}`);
                    return response.json();
                })
                .then(data => {
                    addLog(`📦 Delete response data: ${JSON.stringify(data)}`);
                    if (data.success) {
                        showNotification('Documento eliminado exitosamente', 'success');
                    } else {
                        showNotification('Error al eliminar el documento: ' + data.message, 'error');
                    }
                })
                .catch(error => {
                    addLog('❌ Error: ' + error.message, 'error');
                    showNotification('Error de conexión al eliminar el documento', 'error');
                });
            }
        }
        
        function downloadDocument(filePath) {
            addLog(`🔍 downloadDocument llamada con path: ${filePath}`);
            
            const link = document.createElement('a');
            link.href = filePath;
            link.download = filePath.split('/').pop();
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Descarga iniciada', 'info');
        }
        
        function showNotification(message, type = 'info') {
            addLog(`🔔 Notificación: ${message} (${type})`);
            
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-custom`;
            notification.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
        
        // Log inicial
        addLog('🚀 Sistema de test cargado correctamente');
        addLog('📋 Funciones disponibles: markAsRead, signDocument, deleteInformativo, downloadDocument');
    </script>
</body>
</html>
